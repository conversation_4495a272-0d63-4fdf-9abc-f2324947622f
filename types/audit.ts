// export interface AuditDetail {
//     column_name: string
//     old_value: any
//     new_value: any
// }

// export interface RepairHistoryEntry {
//     audit_id: string
//     car_id: string
//     audit_date: string
//     user_id: string
//     details: AuditDetail[]
// }

// export interface TransferHistoryEntry {
//     audit_id: string
//     car_id: string
//     audit_date: string
//     user_id: string
//     details: AuditDetail[]
// }

// export interface AuditHeader {
//     audit_id: string | number
//     car_id: string
//     timestamp: string
//     description: string
// }


// Define types for audit tables
export interface AuditHeader {
    audit_id: number
    car_id: string
    timestamp: string
    description?: string
}

export interface AuditDetail {
    audit_detail_id: number
    audit_id: number
    column_name: string
    old_value: number | null
    new_value: number | null
}

export interface RepairHistoryEntry {
    audit_id: number
    car_id: string
    timestamp: string
    description?: string
    details: {
        column_name: string
        old_value: number | null
        new_value: number | null
    }[]
}

// New types for transfer history
export interface TransferHistoryEntry {
    transfer_id: number
    car_id: string
    timestamp: string
    description?: string
    details: {
        column_name: string
        old_value: string | number | null
        new_value: string | number | null
    }[]
}
