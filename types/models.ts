export interface BaseModel {
    car_id: string
    created_at: string
    updated_at: string
}

// Main table - car_stock_info
export interface CarStockInfo extends BaseModel {
    car_id: string                      // UUID
    index_number: number               // SERIAL (auto-incremented)
    is_auction_car: boolean | null     // nullable boolean
    registration_book_received_date: string | null // 'YYYY-MM-DD' date string
    old_license_plate: string | null
    new_license_plate: string | null
    registration_date: string | null   // 'YYYY-MM-DD' date string
    total_investment: number | null
    listed_price: number | null
    car_status:
    | 'purchase'
    | 'transfer'
    | 'repair'
    | 'available'
    | 'finance_request'
    | 'finance_done'
    | 'sold'
    | 'reserved'
    notes: string | null
}

// car_buyin table
export interface CarBuyin extends BaseModel {
    car_id: string                             // UUID
    purchase_date: string | null               // 'YYYY-MM-DD'
    parking_location: string | null
    transport_1_auction_lot: number | null
    transport_2_personal_payment: number | null
    transport_3_tl_payment: number | null
    qc1_auction_lot: number | null
    initial_check: number | null
    ems_registration_qc3: number | null
    qc3: number | null
    registration_fee: number | null
    brand: string | null
    model: string | null
    color: string | null
    year: number | null
    vat_percent: number | null                // numeric(5, 2)
    purchase_price: number | null
    purchase_vat_percent: number | null       // numeric(10, 2)
    operation_cost_incl_vat: number | null
    tax_insurance_cost_zero: number | null
    other_costs_seven: number | null
    five_three_tax_percentage: number | null  // numeric(10, 2)
    total_purchase_cost: number | null
    auction_location: string | null
    auction_provinced: string | null
    auction_order: string | null
    auction_checker: string | null
    transport_personal: string | null
    transport_company: string | null
    tank_number: string | null
    engine_number: string | null
    book_deposit: number | null
    type_of_transport: string | null
}


// car_repair table
export interface CarRepair extends BaseModel {
    car_id: string                         // UUID
    repainting_cost: number | null
    engine_repair_cost: number | null
    suspension_repair_cost: number | null
    autopart_cost: number | null
    battery_cost: number | null
    tires_wheels_cost: number | null
}

// car_finance table
export interface CarFinance extends BaseModel {
    car_id: string                                     // UUID
    finance_received_date: string | null               // 'YYYY-MM-DD'
    car_tax_invoice_date: string | null
    car_tax_invoice_number: string | null
    car_amount: number | null
    car_vat_amount: number | null
    commission_tax_invoice_date: string | null
    commission_tax_invoice_number: string | null
    car_commission_amount: number | null
    input_vat_commission: number | null
    withholding_tax: number | null
    salesperson: string | null
    bank: string | null
    marketing_person: string | null
    promotion_customer: number | null
    bonus_insurance_car_life_engine: number | null
    customer_name: string | null
    customer_address_or_advance_payment: string | null
    down_payment: number | null
    loan_protection_insurance: number | null
    accident_insurance: number | null
    car_insurance: number | null
    bank_documents: number | null
}

// car_sellout table
export interface CarSellout extends BaseModel {
    car_id: string                               // UUID
    sale_date: string | null                     // 'YYYY-MM-DD'
    owner_name: string | null
    customer_address: string | null
    actual_selling_price: number | null
    commission_s: number | null
    commission_agent: number | null
    commission_manager: number | null
    sales_channel: string | null
}

// Additional type for relationships
export interface CarComplete {
    stockInfo: CarStockInfo
    buyin?: CarBuyin
    repair?: CarRepair
    finance?: CarFinance
    sellout?: CarSellout
}


// Monthly Summary Interface
/**
 * Monthly Summary Interface - Focuses on sold cars only
 * Contains both direct database fields and calculated fields
 */
export interface MonthlySummary {
    // Unique identifier for the summary record
    summary_id: string

    // Car identification
    car_id: string
    car_number: number // Sequential number for cars sold in the month

    // Basic information (from database)
    purchase_date: string // From CarBuyin
    sale_date: string // From CarSellout
    tank_number: string // From CarBuyin
    brand: string // From CarBuyin
    vat_percent: number // From CarBuyin

    // Time calculations
    time_in_stock: number // Days: sale_date - purchase_date

    // Financial calculations - Revenue
    actual_selling_price: number // From CarSellout
    car_amount_finance: number // actual_selling_price - 50000
    vat_on_car_amount: number // car_amount_finance * vat_percent
    car_commission_amount: number // From CarFinance
    total_revenue: number // Sum of (actual_selling_price, car_amount_finance, car_commission_amount)

    // Financial calculations - Investment
    purchase_price: number // From CarBuyin
    purchase_vat_percent: number // From CarBuyin
    operation_cost_incl_vat: number // From CarBuyin
    transport_1_auction_lot: number // From CarBuyin
    initial_check: number // From CarBuyin
    tax_insurance_cost_zero: number // From CarBuyin
    other_costs_seven: number // From CarBuyin
    five_three_tax_percentage: number // From CarBuyin
    qc1_auction_lot: number // From CarBuyin
    transport_2_personal_payment: number // From CarBuyin
    transport_3_tl_payment: number // From CarBuyin
    repainting_cost: number // From CarRepair
    total_investment: number // From CarStockInfo

    // Tax calculations
    thirty_document: number // vat_on_car_amount - purchase_vat_percent

    // Commission and fees
    commission_s: number // From CarSellout
    commission_agent: number // From CarSellout
    commission_manager: number // From CarSellout
    qc_siriporn: number // If owner_name == "TONG" then some value else 0
    miscellaneous_siriporn: number // Fixed rate: 1000
    marketing_bak_cost: number // Fixed rate: 2000
    finance_payment_process: number // actual_selling_price * 0.012 (1.2%)
    ems_registration: number // From CarBuyin (separated field)
    qc3: number // From CarBuyin (separated field)
    registration_fee: number // From CarBuyin
    rush_transport: number // Fixed rate: 600
    socials_ads: number // Fixed rate: 3000
    promotion_customer: number // From CarFinance
    bonus_insurance_car_life_engine: number // From CarFinance

    // Maintenance costs
    fuel_cost: number // time_in_stock in months * 500
    parking_cost: number // time_in_stock in months * 500
    liquidor_cost: number // Fixed rate: 500
    engine_repair_cost: number // From CarRepair
    suspension_repair_cost: number // From CarRepair
    autopart_cost: number // From CarRepair
    battery_cost: number // From CarRepair
    tires_wheels_cost: number // From CarRepair

    // Aggregated costs
    repair_and_management_cost: number // Sum of (commission_s, commission_agent, commission_manager,
    // ems_registration, qc3, registration_fee, promotion_customer,
    // bonus_insurance_car_life_engine, liquidor_cost, engine_repair_cost,
    // suspension_repair_cost, autopart_cost, battery_cost, tires_wheels_cost)

    // Profit calculations
    payment_fee: number // Fixed rate: 50
    init_profit: number // total_revenue - total_investment - repair_and_management_cost - payment_fee
    deduct_transfer_twelve: number // total_investment * 0.10 * time_in_stock / 365
    profit_and_loss: number // init_profit - deduct_transfer_twelve
    profit_and_loss_percent: number // profit_and_loss / total_investment * 100

    // Additional information
    bank: string // From CarFinance
    salesperson: string // From CarFinance
}

// Helper functions for monthly summary calculations
export const monthlySummaryCalculations = {
    /**
     * Calculate time in stock in days
     * @param purchaseDate Date when car was purchased
     * @param saleDate Date when car was sold
     * @returns Number of days the car was in stock
     */
    calculateTimeInStock: (purchaseDate: string, saleDate: string): number => {
        const purchase = new Date(purchaseDate)
        const sale = new Date(saleDate)
        const diffTime = Math.abs(sale.getTime() - purchase.getTime())
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    },

    /**
     * Calculate time-based costs (fuel, parking)
     * @param timeInStock Number of days in stock
     * @param ratePerMonth Rate per month (default: 500)
     * @returns Cost based on months in stock
     */
    calculateTimeBasedCost: (timeInStock: number, ratePerMonth = 500): number => {
        const monthsInStock = Math.ceil(timeInStock / 30)
        return monthsInStock * ratePerMonth
    },

    /**
     * Calculate car amount for finance
     * @param actualSellingPrice The actual selling price
     * @returns Car amount for finance calculation
     */
    calculateCarAmountFinance: (actualSellingPrice: number): number => {
        return actualSellingPrice - 50000
    },

    /**
     * Calculate VAT on car amount
     * @param carAmountFinance Car amount for finance
     * @param vatPercent VAT percentage
     * @returns VAT amount
     */
    calculateVatOnCarAmount: (carAmountFinance: number, vatPercent: number): number => {
        return carAmountFinance * (vatPercent / 100)
    },

    /**
     * Calculate finance payment process fee
     * @param actualSellingPrice The actual selling price
     * @returns Finance payment process fee (1.2% of selling price)
     */
    calculateFinancePaymentProcess: (actualSellingPrice: number): number => {
        return actualSellingPrice * 0.012
    },

    /**
     * Calculate deduction for transfer (12% annual rate prorated by time in stock)
     * @param totalInvestment Total investment in the car
     * @param timeInStock Number of days in stock
     * @returns Deduction amount
     */
    calculateDeductTransfer: (totalInvestment: number, timeInStock: number): number => {
        return totalInvestment * 0.1 * (timeInStock / 365)
    },
}


export const __non_empty_module_flag__ = true