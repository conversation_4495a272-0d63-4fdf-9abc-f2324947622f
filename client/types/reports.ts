export interface DailyAuctionParams {
  date: string;
  location?: string;
  province?: string;
}

export interface AuctionItem {
  id: string;
  indexNumber: string;
  auctionOrder: string;
  brand: string;
  model: string;
  color: string;
  year: number;
  tankNumber: string;
  engineNumber: string;
  purchasePrice: number;
  operationCost: number;
  transportCost: number;
  otherCosts: number;
}

export interface AuctionProvince {
  province: string;
  items: AuctionItem[];
}

export interface AuctionLocation {
  location: string;
  provinces: AuctionProvince[];
}

export interface DailyAuctionResponse {
  date: string;
  locations: AuctionLocation[];
}
