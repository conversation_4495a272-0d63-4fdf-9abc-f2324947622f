"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { mockCars } from "@/data/mock-data"
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer, Toolt<PERSON> } from "recharts"

export function BrandComparisonChart() {
  // Get brand purchase and sales data
  const brandData = getBrandComparisonData()

  return (
    <Card className="h-full">
      <CardHeader className="p-4 pb-0">
        <CardTitle className="text-lg">Brand Purchase vs. Sales Ratio</CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="h-[280px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={brandData} margin={{ top: 10, right: 10, left: 0, bottom: 50 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="brand" angle={-45} textAnchor="end" height={50} tick={{ fontSize: 11 }} tickMargin={5} />
              <YAxis tick={{ fontSize: 11 }} />
              <Tooltip
                formatter={(value) => [`${value} vehicles`, ""]}
                labelFormatter={(label) => `Brand: ${label}`}
                contentStyle={{ fontSize: "12px" }}
              />
              <Legend wrapperStyle={{ fontSize: "11px", paddingTop: "5px" }} iconSize={10} align="center" />
              <Bar dataKey="purchased" name="Purchased" fill="#4f46e5" />
              <Bar dataKey="sold" name="Sold" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )

  // Helper function to get brand comparison data
  function getBrandComparisonData() {
    // Count purchases by brand
    const purchasesByBrand: Record<string, number> = {}
    mockCars.forEach((car) => {
      if (car.buyin?.brand) {
        const brand = car.buyin.brand
        purchasesByBrand[brand] = (purchasesByBrand[brand] || 0) + 1
      }
    })

    // Count sales by brand
    const salesByBrand: Record<string, number> = {}
    mockCars.forEach((car) => {
      if (car.stockInfo.car_status === "sold" && car.buyin?.brand) {
        const brand = car.buyin.brand
        salesByBrand[brand] = (salesByBrand[brand] || 0) + 1
      }
    })

    // Combine data for chart
    const brands = Object.keys(purchasesByBrand)
    const data = brands.map((brand) => ({
      brand,
      purchased: purchasesByBrand[brand] || 0,
      sold: salesByBrand[brand] || 0,
      total: (purchasesByBrand[brand] || 0) + (salesByBrand[brand] || 0), // For sorting
    }))

    // Sort by total (purchased + sold) in descending order
    return data.sort((a, b) => b.total - a.total)
  }
}
