"use client"

import { Card, CardContent } from "@/components/ui/card"
import { mockCars } from "@/data/mock-data"

export function InventoryCountWidget() {
  // Count cars by status
  const statusCounts = mockCars.reduce(
    (acc, car) => {
      const status = car.stockInfo.car_status
      if (status === "available") acc.available++
      else if (status === "sold") acc.sold++
      else if (status === "repair" || status === "in_repair") acc.repair++
      else if (status === "reserved") acc.reserved++
      else if (status === "purchase") acc.purchase++
      else if (status === "transfer") acc.transfer++
      else if (status === "finance_request") acc.financeRequest++
      else if (status === "finance_done") acc.financeDone++
      return acc
    },
    {
      available: 0,
      sold: 0,
      repair: 0,
      reserved: 0,
      purchase: 0,
      transfer: 0,
      financeRequest: 0,
      financeDone: 0,
    },
  )

  // Calculate total inventory
  const totalInventory = mockCars.length

  // Calculate percentages
  const getPercent = (count: number) => Math.round((count / totalInventory) * 100)

  return (
    <Card>
      <CardContent className="p-4">
        <div>
          <h3 className="text-lg font-bold">Inventory Status</h3>
          <p className="text-muted-foreground text-xs">Items in STOCK</p>
        </div>

        <div className="mt-2 mb-3">
          <span className="text-4xl font-bold text-green-600">{totalInventory}</span>
        </div>

        <div className="grid grid-cols-2 gap-x-4 gap-y-1.5">
          <StatusItem label="Purchase" count={statusCounts.purchase} percent={getPercent(statusCounts.purchase)} />
          <StatusItem label="Transfer" count={statusCounts.transfer} percent={getPercent(statusCounts.transfer)} />
          <StatusItem label="Repair" count={statusCounts.repair} percent={getPercent(statusCounts.repair)} />
          <StatusItem label="Available" count={statusCounts.available} percent={getPercent(statusCounts.available)} />
          <StatusItem
            label="Finance Req"
            count={statusCounts.financeRequest}
            percent={getPercent(statusCounts.financeRequest)}
          />
          <StatusItem
            label="Finance Done"
            count={statusCounts.financeDone}
            percent={getPercent(statusCounts.financeDone)}
          />
          <StatusItem
            label="Sold"
            count={statusCounts.sold}
            percent={getPercent(statusCounts.sold)}
            textColor="text-red-600"
          />
          <StatusItem label="Reserved" count={statusCounts.reserved} percent={getPercent(statusCounts.reserved)} />
        </div>
      </CardContent>
    </Card>
  )
}

// Helper component for status items
function StatusItem({
  label,
  count,
  percent,
  textColor = "text-gray-900",
}: {
  label: string
  count: number
  percent: number
  textColor?: string
}) {
  return (
    <div className="flex justify-between items-center border-b border-gray-100 pb-1">
      <div className="flex items-center gap-1.5">
        <span className="text-xs font-medium text-gray-600">{label}</span>
        <span className="text-[10px] text-gray-400">({percent}%)</span>
      </div>
      <span className={`text-sm font-bold ${textColor}`}>{count}</span>
    </div>
  )
}
