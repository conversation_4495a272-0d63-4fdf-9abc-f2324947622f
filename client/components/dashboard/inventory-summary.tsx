"use client"

import { Card, CardContent } from "@/components/ui/card"
import { mockCars } from "@/data/mock-data"
import {
  Car,
  PenToolIcon as <PERSON><PERSON>,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  FileText,
  TruckIcon,
  ShoppingCart,
} from "lucide-react"

export function InventorySummary() {
  // Count cars by status
  const statusCounts = mockCars.reduce(
    (acc, car) => {
      const status = car.stockInfo.car_status
      if (status === "available") acc.available++
      else if (status === "sold") acc.sold++
      else if (status === "repair" || status === "in_repair") acc.repair++
      else if (status === "reserved") acc.reserved++
      else if (status === "purchase") acc.purchase++
      else if (status === "transfer") acc.transfer++
      else if (status === "finance_request") acc.financeRequest++
      else if (status === "finance_done") acc.financeDone++
      return acc
    },
    {
      available: 0,
      sold: 0,
      repair: 0,
      reserved: 0,
      purchase: 0,
      transfer: 0,
      financeRequest: 0,
      financeDone: 0,
    },
  )

  // Calculate percentages
  const total = mockCars.length
  const getPercent = (count: number) => Math.round((count / total) * 100)

  // Status card configurations
  const statusCards = [
    {
      name: "Purchase",
      count: statusCounts.purchase,
      percent: getPercent(statusCounts.purchase),
      icon: <ShoppingCart className="h-5 w-5 text-gray-600" />,
      bgColor: "bg-gray-100",
      textColor: "text-gray-600",
    },
    {
      name: "Transfer",
      count: statusCounts.transfer,
      percent: getPercent(statusCounts.transfer),
      icon: <TruckIcon className="h-5 w-5 text-indigo-600" />,
      bgColor: "bg-indigo-100",
      textColor: "text-indigo-600",
    },
    {
      name: "Repair",
      count: statusCounts.repair,
      percent: getPercent(statusCounts.repair),
      icon: <Tools className="h-5 w-5 text-amber-600" />,
      bgColor: "bg-amber-100",
      textColor: "text-amber-600",
    },
    {
      name: "Available",
      count: statusCounts.available,
      percent: getPercent(statusCounts.available),
      icon: <Car className="h-5 w-5 text-green-600" />,
      bgColor: "bg-green-100",
      textColor: "text-green-600",
    },
    {
      name: "Finance Request",
      count: statusCounts.financeRequest,
      percent: getPercent(statusCounts.financeRequest),
      icon: <FileText className="h-5 w-5 text-blue-600" />,
      bgColor: "bg-blue-100",
      textColor: "text-blue-600",
    },
    {
      name: "Finance Done",
      count: statusCounts.financeDone,
      percent: getPercent(statusCounts.financeDone),
      icon: <DollarSign className="h-5 w-5 text-emerald-600" />,
      bgColor: "bg-emerald-100",
      textColor: "text-emerald-600",
    },
    {
      name: "Sold",
      count: statusCounts.sold,
      percent: getPercent(statusCounts.sold),
      icon: <CheckCircle className="h-5 w-5 text-red-600" />,
      bgColor: "bg-red-100",
      textColor: "text-red-600",
    },
    {
      name: "Reserved",
      count: statusCounts.reserved,
      percent: getPercent(statusCounts.reserved),
      icon: <AlertTriangle className="h-5 w-5 text-purple-600" />,
      bgColor: "bg-purple-100",
      textColor: "text-purple-600",
    },
  ]

  return (
    <div className="grid gap-3 md:grid-cols-4">
      {statusCards.map((card) => (
        <Card key={card.name} className="overflow-hidden">
          <CardContent className="p-0">
            <div className="flex items-center">
              <div className={`${card.bgColor} p-3 flex items-center justify-center`}>{card.icon}</div>
              <div className="p-3 flex-1">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-xs font-medium text-muted-foreground">{card.name}</p>
                    <p className="text-lg font-bold">{card.count}</p>
                  </div>
                  <div className="text-right">
                    <span className={`text-xs font-medium ${card.textColor}`}>{card.percent}%</span>
                    <div className="w-12 h-1 bg-gray-200 rounded-full mt-1">
                      <div
                        className={`h-1 rounded-full ${card.textColor.replace("text", "bg")}`}
                        style={{ width: `${card.percent}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
