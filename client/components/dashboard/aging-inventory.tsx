"use client"

import { mockCars } from "@/data/mock-data"
import { Badge } from "@/components/ui/badge"
import { formatCurrency, formatDate } from "@/lib/utils"
import { Clock } from "lucide-react"

export function AgingInventory() {
  // Get aging inventory (available cars for more than 30 days)
  const agingCars = getAgingInventory()

  return (
    <div className="space-y-4">
      {agingCars.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-[200px] text-center">
          <Clock className="h-10 w-10 text-gray-300 mb-2" />
          <p className="text-sm text-gray-500">No aging inventory detected</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-[300px] overflow-auto pr-2">
          {agingCars.map((car) => (
            <div key={car.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex-1">
                <div className="flex items-center">
                  <p className="font-medium">
                    {car.brand} {car.model} - {car.index}
                  </p>
                  <Badge variant="outline" className={`ml-2 text-xs ${getAgeColor(car.daysAvailable)}`}>
                    {getAgeLabel(car.daysAvailable)}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Available for {car.daysAvailable} days (since {formatDate(car.date)})
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">{formatCurrency(car.price)}</p>
                <p className="text-xs text-muted-foreground">Listed Price</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )

  // Helper function to get aging inventory
  function getAgingInventory() {
    const agingCars: Array<{
      id: string
      index: string
      brand: string
      model: string
      price: number
      date: string
      daysAvailable: number
    }> = []

    const today = new Date()

    mockCars.forEach((car) => {
      if (car.stockInfo.car_status === "available") {
        // For simplicity, we'll use updated_at as the status change date
        const availableDate = new Date(car.stockInfo.updated_at)

        // Calculate days available
        const daysAvailable = Math.floor((today.getTime() - availableDate.getTime()) / (1000 * 60 * 60 * 24))

        if (daysAvailable > 30) {
          agingCars.push({
            id: car.stockInfo.car_id,
            index: car.stockInfo.index_number,
            brand: car.buyin?.brand || "Unknown",
            model: car.buyin?.model || "Unknown",
            price: car.stockInfo.listed_price,
            date: car.stockInfo.updated_at,
            daysAvailable,
          })
        }
      }
    })

    // Sort by days available (descending)
    return agingCars.sort((a, b) => b.daysAvailable - a.daysAvailable)
  }

  // Helper function to get age label
  function getAgeLabel(days: number) {
    if (days > 90) return "Critical"
    if (days > 60) return "At Risk"
    return "Aging"
  }

  // Helper function to get age color
  function getAgeColor(days: number) {
    if (days > 90) return "bg-red-100 text-red-800 hover:bg-red-100 hover:text-red-800"
    if (days > 60) return "bg-amber-100 text-amber-800 hover:bg-amber-100 hover:text-amber-800"
    return "bg-blue-100 text-blue-800 hover:bg-blue-100 hover:text-blue-800"
  }
}
