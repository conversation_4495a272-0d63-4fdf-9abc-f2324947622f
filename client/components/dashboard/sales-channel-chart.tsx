"use client"

import { mockCars } from "@/data/mock-data"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from "recharts"

export function SalesChannelChart() {
  // Count sales by channel
  const salesByChannel = mockCars.reduce(
    (acc, car) => {
      if (car.sellout && car.sellout.sales_channel) {
        const channel = car.sellout.sales_channel
        acc[channel] = (acc[channel] || 0) + 1
      }
      return acc
    },
    {} as Record<string, number>,
  )

  // Convert to array for chart
  const data = Object.entries(salesByChannel).map(([name, value]) => ({
    name,
    value,
  }))

  // Sort by value descending
  data.sort((a, b) => b.value - a.value)

  // Colors for the chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

  // Calculate percentages
  const total = data.reduce((sum, entry) => sum + entry.value, 0)

  // Adjusted to fill the available height
  return (
    <div className="h-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={70}
            paddingAngle={5}
            dataKey="value"
            label={({ name, percent }) =>
              `${name.substring(0, 8)}${name.length > 8 ? "..." : ""} (${(percent * 100).toFixed(0)}%)`
            }
            labelLine={false}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [`${value} vehicles (${((value / total) * 100).toFixed(1)}%)`, "Sales"]}
            contentStyle={{ fontSize: "10px" }}
          />
          <Legend
            wrapperStyle={{ fontSize: 10, bottom: 0 }}
            verticalAlign="bottom"
            height={36}
            iconSize={7}
            layout="horizontal"
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
