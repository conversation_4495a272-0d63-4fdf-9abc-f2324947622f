"use client"

import { mockCars } from "@/data/mock-data"
import { Badge } from "@/components/ui/badge"
import { formatDate } from "@/lib/utils"
import { AlertCircle } from "lucide-react"

export function BottleneckTracker({ statusFilter = "all" }: { statusFilter?: string }) {
  // Get cars stuck in process for more than 10 days
  const bottlenecks = getBottleneckedCars(statusFilter)

  return (
    <div className="space-y-4">
      {bottlenecks.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-[200px] text-center">
          <AlertCircle className="h-10 w-10 text-gray-300 mb-2" />
          <p className="text-sm text-gray-500">No bottlenecks detected</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-[300px] overflow-auto pr-2">
          {bottlenecks.map((car) => (
            <div key={car.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex-1">
                <div className="flex items-center">
                  <p className="font-medium">
                    {car.brand} {car.model} - {car.index}
                  </p>
                  <Badge variant="outline" className={`ml-2 text-xs ${getStatusColor(car.status)}`}>
                    {car.status.replace(/_/g, " ")}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Stuck for {car.daysStuck} days (since {formatDate(car.date)})
                </p>
              </div>
              <div className="text-right">
                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                  {car.daysStuck} days
                </Badge>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )

  // Helper function to get bottlenecked cars
  function getBottleneckedCars(filter: string) {
    const bottlenecks: Array<{
      id: string
      index: string
      brand: string
      model: string
      status: string
      date: string
      daysStuck: number
    }> = []

    const today = new Date()
    const tenDaysAgo = new Date(today)
    tenDaysAgo.setDate(tenDaysAgo.getDate() - 10)

    mockCars.forEach((car) => {
      const status = car.stockInfo.car_status

      // Filter by status if a specific status is selected
      if (filter !== "all" && status !== filter) {
        return
      }

      // Only check cars in these statuses
      if (
        status === "in_repair" ||
        status === "repair" ||
        status === "transfer" ||
        status === "finance_request" ||
        status === "reserved"
      ) {
        // For simplicity, we'll use updated_at as the status change date
        const statusDate = new Date(car.stockInfo.updated_at)

        // Calculate days stuck
        const daysStuck = Math.floor((today.getTime() - statusDate.getTime()) / (1000 * 60 * 60 * 24))

        if (daysStuck > 10) {
          bottlenecks.push({
            id: car.stockInfo.car_id,
            index: car.stockInfo.index_number,
            brand: car.buyin?.brand || "Unknown",
            model: car.buyin?.model || "Unknown",
            status: car.stockInfo.car_status,
            date: car.stockInfo.updated_at,
            daysStuck,
          })
        }
      }
    })

    // Sort by days stuck (descending)
    return bottlenecks.sort((a, b) => b.daysStuck - a.daysStuck)
  }

  // Helper function to get status color
  function getStatusColor(status: string) {
    switch (status) {
      case "in_repair":
      case "repair":
        return "bg-amber-100 text-amber-800 hover:bg-amber-100 hover:text-amber-800"
      case "transfer":
        return "bg-indigo-100 text-indigo-800 hover:bg-indigo-100 hover:text-indigo-800"
      case "finance_request":
        return "bg-blue-100 text-blue-800 hover:bg-blue-100 hover:text-blue-800"
      case "reserved":
        return "bg-purple-100 text-purple-800 hover:bg-purple-100 hover:text-purple-800"
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100 hover:text-gray-800"
    }
  }
}
