"use client"

import { mockCars } from "@/data/mock-data"
import { <PERSON>, <PERSON><PERSON>hart, XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer, Toolt<PERSON> } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function FinancialSnapshot() {
  // Generate financial data based on mock cars
  const financialData = generateFinancialData()

  return (
    <Card className="w-full">
      <CardHeader className="pb-2 pt-4">
        <CardTitle className="text-lg font-bold">Financial Snapshot</CardTitle>
        <CardDescription className="text-xs">Monthly revenue and investment overview</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Single row layout for metrics with smaller text */}
        <div className="flex justify-between gap-4">
          <div className="bg-gray-50 p-3 rounded-md flex-1">
            <p className="text-xs text-muted-foreground">Total Revenue</p>
            <p className="text-lg font-bold">THB {formatNumber(calculateTotalRevenue())}</p>
          </div>
          <div className="bg-gray-50 p-3 rounded-md flex-1">
            <p className="text-xs text-muted-foreground">Average Margin</p>
            <p className="text-lg font-bold">{calculateAverageMargin()}%</p>
          </div>
          <div className="bg-gray-50 p-3 rounded-md flex-1">
            <p className="text-xs text-muted-foreground">Avg Profit/Car</p>
            <p className="text-lg font-bold">THB {formatNumber(calculateAverageProfitPerCar())}</p>
          </div>
        </div>

        {/* Chart showing monthly revenue and investment */}
        <div className="h-[250px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={financialData} margin={{ top: 10, right: 30, left: 0, bottom: 50 }}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                dy={10}
                tick={{ fontSize: 11 }}
                interval={0} // Show all ticks
              />
              <YAxis
                yAxisId="left"
                orientation="left"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 10 }}
                tickFormatter={(value) => value.toLocaleString()}
              />
              <Tooltip
                formatter={(value, name) => {
                  return [value.toLocaleString(), name === "revenue" ? "Revenue" : "Investment"]
                }}
                contentStyle={{ fontSize: "11px" }}
              />
              <Legend
                verticalAlign="bottom"
                height={36}
                iconType="circle"
                wrapperStyle={{ paddingTop: 20, bottom: 0, fontSize: "11px" }}
              />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="revenue"
                stroke="#4ade80"
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 5 }}
                name="Revenue"
              />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="investment"
                stroke="#f43f5e"
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 5 }}
                name="Investment"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )

  // Helper function to calculate total revenue
  function calculateTotalRevenue() {
    return mockCars.reduce((total, car) => {
      if (car.sellout && car.sellout.actual_selling_price) {
        return total + car.sellout.actual_selling_price
      }
      return total
    }, 0)
  }

  // Helper function to calculate average margin
  function calculateAverageMargin() {
    let totalMargin = 0
    let count = 0

    mockCars.forEach((car) => {
      if (car.sellout && car.sellout.actual_selling_price && car.stockInfo.total_investment) {
        const margin =
          ((car.sellout.actual_selling_price - car.stockInfo.total_investment) / car.stockInfo.total_investment) * 100
        totalMargin += margin
        count++
      }
    })

    return count > 0 ? Math.round(totalMargin / count) : 0
  }

  // Helper function to calculate average profit per car
  function calculateAverageProfitPerCar() {
    let totalProfit = 0
    let count = 0

    mockCars.forEach((car) => {
      if (car.sellout && car.sellout.actual_selling_price && car.stockInfo.total_investment) {
        const profit = car.sellout.actual_selling_price - car.stockInfo.total_investment
        totalProfit += profit
        count++
      }
    })

    return count > 0 ? totalProfit / count : 0
  }

  // Helper function to generate financial data for the chart
  function generateFinancialData() {
    // Expanded to show all 12 months
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]

    return months.map((month, index) => {
      // Generate data with realistic patterns
      // Base revenue starts at 800k and grows with seasonal variations
      const seasonalFactor =
        index < 3
          ? 0.8 + index * 0.1
          : // Q1: Slow start
            index < 6
            ? 1.1 + (index - 3) * 0.05
            : // Q2: Growth
              index < 9
              ? 1.2 - (index - 6) * 0.03
              : // Q3: Peak then slight decline
                1.0 + (index - 9) * 0.15 // Q4: Holiday season boost

      const baseRevenue = 800000 * seasonalFactor

      // Add some randomness to make the chart look more realistic
      const randomVariation = Math.random() * 0.2 - 0.1 // -10% to +10%
      const revenue = Math.round(baseRevenue * (1 + randomVariation))

      // Investment is typically 70-85% of revenue with some variation
      const investmentRatio = 0.7 + Math.random() * 0.15
      const investment = Math.round(revenue * investmentRatio)

      return {
        month,
        revenue,
        investment,
      }
    })
  }

  // Helper function to format numbers with commas
  function formatNumber(num: number) {
    return num.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }
}
