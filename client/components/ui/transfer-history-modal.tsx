"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import type { TransferHistoryEntry } from "@/types/audit"
import { formatDate } from "@/lib/utils"
import { CustomSelect } from "@/components/ui/custom-select"

interface TransferHistoryModalProps {
  isOpen: boolean
  onClose: () => void
  onSave?: () => void
  history: TransferHistoryEntry[]
}

export function TransferHistoryModal({ isOpen, onClose, onSave, history }: TransferHistoryModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col p-0">
        <DialogHeader className="px-6 pt-6 pb-2 flex-shrink-0 border-b">
          <DialogTitle className="text-xl font-bold text-primary-dark">Transfer History</DialogTitle>
        </DialogHeader>

        <div className="flex-grow overflow-hidden">
          <ScrollArea className="h-full max-h-[calc(80vh-130px)]">
            <div className="px-6 py-4 space-y-8">
              {history.length === 0 ? (
                <div className="text-center py-8 text-gray-500">No transfer history found.</div>
              ) : (
                history.map((entry, index) => (
                  <div key={entry.transfer_id} className="border rounded-lg overflow-hidden">
                    <div className="bg-gray-50 px-4 py-3 border-b">
                      <div className="flex justify-between items-center">
                        <h3 className="font-medium">
                          {entry.description || `Transfer on ${formatDate(entry.timestamp)}`}
                        </h3>
                        <span className="text-sm text-gray-500">{formatDate(entry.timestamp)}</span>
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                        {/* Parking Location */}
                        {/* {renderField(entry, "parking_location", "Parking Location")} */}
                        <div className="space-y-2">
                          <Label>Parking Location</Label>
                          <CustomSelect
                            id={`parking_location_${index}`}
                            label=""
                            value={entry.parking_location || ""}
                            options={[]}
                            onChange={() => {}}
                            disabled={true}
                          />
                        </div>

                        {/* Type of Transport */}
                        {/* {renderField(entry, "type_of_transport", "Type of Transport")} */}
                        <div className="space-y-2">
                          <Label>Type of Transport</Label>
                          <CustomSelect
                            id={`type_of_transport_${index}`}
                            label=""
                            value={entry.type_of_transport || ""}
                            options={[]}
                            onChange={() => {}}
                            disabled={true}
                          />
                        </div>

                        {/* Auction Transporter */}
                        {/* {renderField(entry, "auction_transporter", "Auction Transporter")} */}
                        <div className="space-y-2">
                          <Label>Auction Transporter</Label>
                          <CustomSelect
                            id={`auction_transporter_${index}`}
                            label=""
                            value={entry.auction_transporter || ""}
                            options={[]}
                            onChange={() => {}}
                            disabled={true}
                          />
                        </div>

                        {/* Transport Personal */}
                        {/* {renderField(entry, "transport_personal", "Transport Personal")} */}
                        <div className="space-y-2">
                          <Label>Transport Personal</Label>
                          <CustomSelect
                            id={`transport_personal_${index}`}
                            label=""
                            value={entry.transport_personal || ""}
                            options={[]}
                            onChange={() => {}}
                            disabled={true}
                          />
                        </div>

                        {/* Transport Company */}
                        {/* {renderField(entry, "transport_company", "Transport Company")} */}
                        <div className="space-y-2">
                          <Label>Transport Company</Label>
                          <CustomSelect
                            id={`transport_company_${index}`}
                            label=""
                            value={entry.transport_company || ""}
                            options={[]}
                            onChange={() => {}}
                            disabled={true}
                          />
                        </div>

                        {/* Transport 2 Personal Payment */}
                        {renderField(entry, "transport_2_personal_payment", "Transport 2 Personal Payment", true)}

                        {/* Transport 3 TL Payment */}
                        {renderField(entry, "transport_3_tl_payment", "Transport 3 TL Payment", true)}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>

        <DialogFooter className="px-6 py-4 border-t bg-gray-50 mt-auto">
          <div className="flex gap-3 ml-auto">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-primary text-primary hover:bg-primary/10"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (onSave) onSave()
                onClose()
              }}
              className="bg-primary hover:bg-primary-dark text-white px-6"
            >
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Helper function to render a field
function renderField(entry: TransferHistoryEntry, fieldName: string, label: string, isNumeric = false) {
  const detail = entry.details.find((d) => d.column_name === fieldName)

  if (!detail) {
    return (
      <div className="space-y-2">
        <Label>{label}</Label>
        <Input value="0" disabled className="bg-gray-100" />
      </div>
    )
  }

  const value = detail.new_value !== null ? detail.new_value : isNumeric ? "0" : ""

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <Input value={value.toString()} disabled className="bg-gray-100" />
    </div>
  )
}
