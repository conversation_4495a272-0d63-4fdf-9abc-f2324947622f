"use client"

import * as React from "react"
import { Check, ChevronDown, ArrowLef<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"

export interface Option {
  value: string
  label: string
}

interface CustomSelectProps {
  id: string
  label: string
  value: string
  options: Option[]
  onChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
  icon?: React.ReactNode
  triggerOption?: string
  triggerLabel?: string
}

export function CustomSelect({
  id,
  label,
  value,
  options,
  onChange,
  placeholder = "Select an option",
  disabled = false,
  icon,
  triggerOption = "etc",
  triggerLabel = "Other (specify)",
}: CustomSelectProps) {
  // Simple state management without complex logic
  const [open, setOpen] = React.useState(false)
  const [isTextMode, setIsTextMode] = React.useState(false)
  const [customValue, setCustomValue] = React.useState("")
  const inputRef = React.useRef<HTMLInputElement>(null)

  // Add the "Other" option to the options array
  const allOptions = React.useMemo(() => {
    return [...options, { value: triggerOption, label: triggerLabel }]
  }, [options, triggerOption, triggerLabel])

  // Check if the current value is in the options
  const isValueInOptions = React.useMemo(() => {
    return options.some((option) => option.value === value)
  }, [options, value])

  // Initialize text mode if value is not in options
  React.useEffect(() => {
    if (value && !isValueInOptions && value !== triggerOption) {
      setIsTextMode(true)
      setCustomValue(value)
    } else {
      setIsTextMode(false)
      setCustomValue("")
    }
  }, [value, isValueInOptions, triggerOption])

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === triggerOption) {
      setIsTextMode(true)
      setCustomValue("")
      setTimeout(() => inputRef.current?.focus(), 100)
    } else {
      onChange(selectedValue)
      setOpen(false)
    }
  }

  const handleTextInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomValue(e.target.value)
  }

  const handleSaveCustomValue = () => {
    if (customValue.trim()) {
      onChange(customValue.trim())
      setOpen(false)
    }
  }

  const handleBackToSelect = () => {
    setIsTextMode(false)
    setCustomValue("")
    onChange("")
  }

  // Handle keyboard events in the input
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault()
      if (customValue.trim()) {
        handleSaveCustomValue()
      }
    } else if (e.key === "Escape") {
      // Prevent the default escape behavior which would close the popover
      e.stopPropagation()
      handleBackToSelect()
    }
  }

  const selectedOption = allOptions.find((option) => option.value === value)

  return (
    <div className="space-y-2">
      <label htmlFor={id} className="text-sm font-medium text-gray-700">
        {label}
      </label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              !value && "text-muted-foreground",
              disabled && "opacity-50 cursor-not-allowed",
            )}
            disabled={disabled}
            tabIndex={0}
          >
            <div className="flex items-center gap-2 truncate">
              {icon && <span className="flex-shrink-0">{icon}</span>}
              <span className="truncate">
                {isTextMode ? customValue || triggerLabel : selectedOption?.label || placeholder}
              </span>
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0 w-[var(--radix-popover-trigger-width)]">
          {isTextMode ? (
            <div className="p-4 space-y-4">
              <div className="flex items-center">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 text-xs"
                  onClick={handleBackToSelect}
                >
                  <ArrowLeft className="mr-1 h-3 w-3" />
                  Back to options
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <Input
                  ref={inputRef}
                  value={customValue}
                  onChange={handleTextInputChange}
                  onKeyDown={handleKeyDown}
                  className="flex-1"
                  placeholder="Enter custom value..."
                  autoFocus
                />
                <Button type="button" size="sm" onClick={handleSaveCustomValue} disabled={!customValue.trim()}>
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <Command>
              <CommandInput placeholder={`Search ${label.toLowerCase()}...`} />
              <CommandList>
                <CommandEmpty>No {label.toLowerCase()} found.</CommandEmpty>
                <CommandGroup>
                  {allOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => handleSelectChange(option.value)}
                    >
                      <Check className={cn("mr-2 h-4 w-4", value === option.value ? "opacity-100" : "opacity-0")} />
                      {option.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          )}
        </PopoverContent>
      </Popover>
    </div>
  )
}
