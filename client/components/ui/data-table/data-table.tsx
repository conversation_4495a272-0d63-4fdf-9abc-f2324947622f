"use client"

import React from "react"

import { useState, useMemo, useEffect } from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Filter,
  Search,
  ArrowRight,
  Layers,
  RotateCcw,
  FolderOpen,
  FileText,
  X,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AdvancedFilter } from "./advanced-filter"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Import the new date utilities
import { formatToDisplayDate } from "@/lib/date-utils"

interface FilterOption {
  field: string
  operator: string
  value: string
  fieldType: string
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
}

// Column groups with their Thai labels - matching the advanced filter options
const columnGroups = [
  {
    name: "ข้อมูลทั่วไป",
    fields: [
      { id: "stockInfo_index_number", label: "ลำดับ" },
      { id: "stockInfo_car_status", label: "สถานะรถ" },
      { id: "stockInfo_old_license_plate", label: "ทะเบียนเดิม" },
      { id: "stockInfo_new_license_plate", label: "ทะเบียนใหม่" },
      { id: "stockInfo_registration_date", label: "วันจดทะเบียน" },
      { id: "stockInfo_registration_book_received_date", label: "วันที่รับเล่มทะเบียน" },
      { id: "stockInfo_total_investment", label: "ทุนรวมทั้งหมด" },
      { id: "stockInfo_listed_price", label: "ราคาตั้งขาย" },
      { id: "stockInfo_notes", label: "หมายเหตุ" },
      { id: "stockInfo_is_auction_car", label: "รถประมูล" },
    ],
  },
  {
    name: "ข้อมูลรถยนต์",
    fields: [
      { id: "buyin_brand", label: "ยี่ห้อ" },
      { id: "buyin_model", label: "รุ่น" },
      { id: "buyin_color", label: "สี" },
      { id: "buyin_year", label: "ปี" },
      { id: "buyin_tank_number", label: "เลขตัวถัง" },
      { id: "buyin_engine_number", label: "เลขเครื่องยนต์" },
    ],
  },
  {
    name: "ข้อมูลการซื้อ",
    fields: [
      { id: "buyin_purchase_date", label: "วันที่ซื้อ" },
      { id: "buyin_purchase_price", label: "ราคาซื้อเข้า" },
      { id: "buyin_vat_percent", label: "VAT(%)" },
      { id: "buyin_purchase_vat_percent", label: "VAT(%)ซื้อ" },
      { id: "buyin_operation_cost_incl_vat", label: "ค่าดำเนินการรวม VAT" },
      { id: "buyin_transport_1_auction_lot", label: "ค่าขนส่ง 1 ลานประมูล" },
      { id: "buyin_initial_check", label: "ตรวจสอบเบื้องต้น" },
      { id: "buyin_tax_insurance_cost_zero", label: "ภาษี-พรบ.+คชจ(0%)" },
      { id: "buyin_other_costs_seven", label: "คชจ.อื่นๆ(7%)" },
      { id: "buyin_five_three_tax_percentage", label: "ภงด.53" },
      { id: "buyin_total_purchase_cost", label: "รวมซื้อเข้า" },
    ],
  },
  {
    name: "ข้อมูลประมูล",
    fields: [
      { id: "buyin_auction_name", label: "ชื่อลานประมูล" },
      { id: "buyin_auction_provinced", label: "จังหวัดที่ประมูล" },
      { id: "buyin_auction_order", label: "ลำดับประมูล" },
      { id: "buyin_auction_checker", label: "ผู้ตรวจสอบประมูล" },
      { id: "buyin_auction_transporter", label: "ผู้ขนส่งประมูล" },
    ],
  },
  {
    name: "ข้อมูลการขนส่ง",
    fields: [
      { id: "buyin_parking_location", label: "สถานที่จอด" },
      { id: "buyin_type_of_transport", label: "ประเภทการขนส่ง" },
      { id: "buyin_transport_personal", label: "ขนส่งบุคคล" },
      { id: "buyin_transport_company", label: "บริษัทขนส่ง" },
      { id: "buyin_transport_2_personal_payment", label: "ค่าขนส่ง 2 บุคคล" },
      { id: "buyin_transport_3_tl_payment", label: "ค่าขนส่ง 3 TL" },
    ],
  },
  {
    name: "ข้อมูลการซ่อม",
    fields: [
      { id: "repair_repair_date", label: "วันที่ซ่อม" },
      { id: "repair_repainting_cost", label: "ทำสี" },
      { id: "repair_engine_repair_cost", label: "ซ่อมเครื่องยนต์" },
      { id: "repair_suspension_repair_cost", label: "ซ่อมช่วงล่าง" },
      { id: "repair_autopart_cost", label: "ประดับยนต์" },
      { id: "repair_battery_cost", label: "แบตเตอรี่" },
      { id: "repair_tires_wheels_cost", label: "ยางรถยนต์/แม็ก" },
    ],
  },
  {
    name: "ข้อมูลไฟแนนซ์",
    fields: [
      { id: "finance_finance_request_date", label: "วันที่ขอไฟแนนซ์" },
      { id: "finance_finance_received_date", label: "วันที่รับเงินไฟแนนซ์" },
      { id: "finance_car_tax_invoice_date", label: "วันออกภาษี-ขาย-Car" },
      { id: "finance_car_tax_invoice_number", label: "เลขที่/เล่มที่ออกภาษี-Car" },
      { id: "finance_car_amount", label: "CAR AMOUNT" },
      { id: "finance_car_vat_amount", label: "VAT ON CAR AMOUNT" },
      { id: "finance_commission_tax_invoice_date", label: "วันออกภาษี-ขาย-Com" },
      { id: "finance_commission_tax_invoice_number", label: "เลขที่/เล่มที่ออกภาษี-Comm" },
      { id: "finance_car_commission_amount", label: "COMMISSION OF CAR" },
      { id: "finance_input_vat_commission", label: "Input-VAT-comm." },
      { id: "finance_withholding_tax", label: "WITHOLDING TAX" },
      { id: "finance_salesperson", label: "พนักงานขาย" },
      { id: "finance_bank", label: "ธนาคาร" },
      { id: "finance_marketing_person", label: "MKT." },
      { id: "finance_promotion_customer", label: "Promotion-ให้ลค." },
      { id: "finance_bonus_insurance_car_life_engine", label: "ประกันแถม(รถ+ชีวิต+เครื่อง)" },
      { id: "finance_customer_name", label: "ลูกค้า" },
      { id: "finance_customer_address_or_advance_payment", label: "ที่อยู่/ค่างวดล่วงหน้า" },
      { id: "finance_down_payment", label: "เงินดาวน์(บาท)" },
      { id: "finance_loanprotectioninsurance", label: "ประกันคุ้มครองสินเชื่อ" },
      { id: "finance_accident_insurance", label: "ประกันอุบัติเหตุ" },
      { id: "finance_car_insurance", label: "ประกันรถยนต์" },
      { id: "finance_bank_documents", label: "จัดเอกสาร-Bank" },
    ],
  },
  {
    name: "ข้อมูลการขาย",
    fields: [
      { id: "sellout_sale_date", label: "วันที่ขาย" },
      { id: "sellout_owner_name", label: "เจ้าของ" },
      { id: "sellout_customer_address", label: "ที่อยู่" },
      { id: "sellout_actual_selling_price", label: "ราคาขายจริง" },
      { id: "sellout_sales_channel", label: "ช่องทางการขาย" },
      { id: "sellout_commission_s", label: "Comm.-S" },
      { id: "sellout_commission_agent", label: "Comm-นายหน้า" },
      { id: "sellout_commission_manager", label: "Comm-ผจก" },
    ],
  },
]

// Create a mapping of column IDs to their labels for easy lookup
const columnLabelsMap: Record<string, string> = {}
columnGroups.forEach((group) => {
  group.fields.forEach((field) => {
    columnLabelsMap[field.id] = field.label
  })
})

// Custom filter functions for number comparisons
const numberFilterFns = {
  equals: (row: any, columnId: string, filterValue: number) => {
    const value = row.getValue(columnId)
    return value === filterValue
  },
  greaterThanEqual: (row: any, columnId: string, filterValue: number) => {
    const value = row.getValue(columnId)
    return typeof value === "number" && value >= filterValue
  },
  lessThanEqual: (row: any, columnId: string, filterValue: number) => {
    const value = row.getValue(columnId)
    return typeof value === "number" && value <= filterValue
  },
  empty: (row: any, columnId: string) => {
    const value = row.getValue(columnId)
    return value === undefined || value === null || value === ""
  },
}

// Replace the formatDateCell function with this:
// Format date cells to display in DD/MM/YYYY format
const formatDateCell = (value: any) => {
  if (!value || typeof value !== "string") return value

  // Check if the value is a date string
  const date = new Date(value)
  if (isNaN(date.getTime())) return value

  // Format as DD/MM/YYYY
  return formatToDisplayDate(date)
}

export function DataTable<TData, TValue>({ columns, data }: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false)
  const [searchField, setSearchField] = useState("buyin.tank_number")
  const [searchValue, setSearchValue] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [columnSearchQuery, setColumnSearchQuery] = useState("")

  const [dateFilters, setDateFilters] = useState<FilterOption[]>([])
  const [numberFilters, setNumberFilters] = useState<FilterOption[]>([])
  const [advancedFilters, setAdvancedFilters] = useState<FilterOption[]>([])

  // Define the default visible columns
  const defaultVisibleColumns = [
    "stockInfo_index_number",
    "buyin_purchase_date",
    "buyin_brand",
    "buyin_model",
    "buyin_tank_number",
    "buyin_engine_number",
    "stockInfo_old_license_plate",
    "buyin_parking_location",
    "stockInfo_car_status",
    "actions",
  ]

  // Function to reset columns to default visibility
  const resetToDefaultColumns = () => {
    const newVisibility: VisibilityState = {}

    // First hide all columns
    table.getAllColumns().forEach((column) => {
      if (column.getCanHide()) {
        newVisibility[column.id] = false
      }
    })

    // Then show only the default columns
    defaultVisibleColumns.forEach((columnId) => {
      newVisibility[columnId] = true
    })

    // Always show the actions column
    if (newVisibility["actions"] !== undefined) {
      newVisibility["actions"] = true
    }

    setColumnVisibility(newVisibility)
  }

  // Set initial column visibility based on meta.hidden property
  useEffect(() => {
    // Define the default visible columns
    const defaultVisibleColumns = [
      "stockInfo_index_number",
      "buyin_purchase_date",
      "buyin_brand",
      "buyin_model",
      "buyin_tank_number",
      "buyin_engine_number",
      "stockInfo_old_license_plate",
      "buyin_parking_location",
      "stockInfo_car_status",
      "actions",
    ]

    // Initialize all columns as hidden
    const initialVisibility: VisibilityState = {}

    // Set all columns to hidden by default
    columns.forEach((column) => {
      if (column.id) {
        initialVisibility[column.id] = false
      }
    })

    // Then set only the default columns to visible
    defaultVisibleColumns.forEach((columnId) => {
      initialVisibility[columnId] = true
    })

    // Always show the actions column
    if (initialVisibility["actions"] !== undefined) {
      initialVisibility["actions"] = true
    }

    setColumnVisibility(initialVisibility)
  }, [columns])

  // Pre-filter the data based on date filters and number filters
  const filteredData = useMemo(() => {
    let result = data

    // Apply date filters
    if (dateFilters.length > 0) {
      result = result.filter((row: any) => {
        return dateFilters.every((filter) => {
          const fieldPath = filter.field.split(".")
          let value = row

          // Navigate through the object path to get the value
          for (const path of fieldPath) {
            if (!value || value[path] === undefined) return false
            value = value[path]
          }

          // Handle empty operator
          if (filter.operator === "empty") {
            return !value || value === ""
          }

          if (!value) return false

          // Handle date range
          if (filter.operator === "range") {
            try {
              const range = JSON.parse(filter.value)
              const rowDate = new Date(value)
              if (isNaN(rowDate.getTime())) return false

              // Set time to beginning of day for consistent comparison
              rowDate.setHours(0, 0, 0, 0)

              let passes = true

              // Check start date if provided
              if (range.start) {
                const startDate = new Date(range.start)
                startDate.setHours(0, 0, 0, 0)
                if (rowDate < startDate) passes = false
              }

              // Check end date if provided
              if (range.end) {
                const endDate = new Date(range.end)
                endDate.setHours(0, 0, 0, 0)
                if (rowDate > endDate) passes = false
              }

              return passes
            } catch (e) {
              return false
            }
          }

          return true
        })
      })
    }

    // Apply number filters
    if (numberFilters.length > 0) {
      result = result.filter((row: any) => {
        return numberFilters.every((filter) => {
          const fieldPath = filter.field.split(".")
          let value = row

          // Navigate through the object path to get the value
          for (const path of fieldPath) {
            if (!value || value[path] === undefined) return false
            value = value[path]
          }

          // Handle empty operator
          if (filter.operator === "empty") {
            return value === undefined || value === null || value === ""
          }

          // Convert to number for comparison
          const numValue = Number(value)
          const filterValue = Number(filter.value)

          if (isNaN(numValue)) return false

          // Compare based on operator
          switch (filter.operator) {
            case "equals":
              return numValue === filterValue
            case "greater_than_equal":
              return numValue >= filterValue
            case "less_than_equal":
              return numValue <= filterValue
            default:
              return true
          }
        })
      })
    }

    return result
  }, [data, dateFilters, numberFilters])

  const table = useReactTable({
    data: filteredData, // Use pre-filtered data
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    initialState: {
      pagination: {
        pageSize: 15,
      },
    },
    columnResizeMode: "onChange",
    filterFns: {
      deepFilter: (row, columnId, filterValue) => {
        const value = row.getValue(columnId)
        if (typeof value === "string") {
          return value.toLowerCase().includes(filterValue.toLowerCase())
        }
        return false
      },
      empty: (row, columnId) => {
        const value = row.getValue(columnId)
        return value === undefined || value === null || value === ""
      },
      notEmpty: (row, columnId) => {
        const value = row.getValue(columnId)
        return value !== undefined && value !== null && value !== ""
      },
      notEquals: (row, columnId, filterValue) => {
        const value = row.getValue(columnId)
        return value !== filterValue
      },
      notContains: (row, columnId, filterValue) => {
        const value = row.getValue(columnId)
        if (typeof value === "string") {
          return !value.toLowerCase().includes(filterValue.toLowerCase())
        }
        return true
      },
      greaterThanEqual: (row, columnId, filterValue) => {
        const value = row.getValue(columnId)
        return typeof value === "number" && value >= filterValue
      },
      lessThanEqual: (row, columnId, filterValue) => {
        const value = row.getValue(columnId)
        return typeof value === "number" && value <= filterValue
      },
    },
  })
  const handleSearchChange = (value: string) => {
    setSearchValue(value)
  }

  const applySearch = () => {
    setIsSearching(true)

    table.getColumn(searchField.replace(".", "_"))?.setFilterValue(undefined)

    if (searchValue.trim()) {
      table.getColumn(searchField.replace(".", "_"))?.setFilterValue(searchValue)
    }

    setIsSearching(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      applySearch()
    }
  }

  const handleSearchFieldChange = (value: string) => {
    table.getColumn(searchField.replace(".", "_"))?.setFilterValue(undefined)
    setSearchField(value)
    setSearchValue("")
  }

  const clearAllFilters = () => {
    setSearchValue("")
    table.resetColumnFilters()
    setDateFilters([]) // Clear date filters
    setNumberFilters([]) // Clear number filters
    setAdvancedFilters([]) // Clear all advanced filters
  }

  // New approach for handling advanced filters
  const handleAdvancedFilterApply = (filters: FilterOption[]) => {
    // Store all filters for reference
    setAdvancedFilters(filters)

    // Separate filters by type
    const newDateFilters: FilterOption[] = []
    const newNumberFilters: FilterOption[] = []
    const textFilters: FilterOption[] = []

    filters.forEach((filter) => {
      if (filter.fieldType === "date") {
        newDateFilters.push(filter)
      } else if (filter.fieldType === "number") {
        newNumberFilters.push(filter)
      } else {
        textFilters.push(filter)
      }
    })

    // Update filter states
    setDateFilters(newDateFilters)
    setNumberFilters(newNumberFilters)

    // Clear existing column filters
    table.getAllColumns().forEach((column) => {
      if (column.getCanFilter()) {
        column.setFilterValue(undefined)
      }
    })

    // Apply text filters only (number and date are handled in filteredData)
    textFilters.forEach((filter) => {
      const columnId = filter.field.replace(".", "_")
      const column = table.getColumn(columnId)

      if (column) {
        if (filter.operator === "empty") {
          column.setFilterValue("empty")
        } else if (filter.operator === "contains") {
          column.setFilterValue(filter.value)
        }
      }
    })
  }

  // Toggle all columns in a group
  const toggleGroupColumns = (groupFields: { id: string; label: string }[], value: boolean) => {
    const newVisibility = { ...table.getState().columnVisibility }

    groupFields.forEach((field) => {
      const column = table.getColumn(field.id)
      if (column && column.getCanHide()) {
        newVisibility[field.id] = value
      }
    })

    setColumnVisibility(newVisibility)
  }

  // Toggle all columns
  const toggleAllColumns = (value: boolean) => {
    const newVisibility: VisibilityState = {}
    table.getAllColumns().forEach((column) => {
      if (column.getCanHide()) {
        newVisibility[column.id] = value
      }
    })
    setColumnVisibility(newVisibility)
  }

  // Check if all columns are visible
  const allColumnsVisible = table
    .getAllColumns()
    .filter((column) => column.getCanHide())
    .every((column) => column.getIsVisible())

  // Check if all columns in a group are visible
  const isGroupVisible = (groupFields: { id: string; label: string }[]) => {
    return groupFields.every((field) => {
      const column = table.getColumn(field.id)
      return column ? column.getIsVisible() : false
    })
  }

  const handleColumnDropdownOpenChange = (open: boolean) => {
    if (!open) {
      setColumnSearchQuery("")
    }
  }

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex items-center justify-between gap-2 mb-2">
        <div className="flex-1 max-w-md flex">
          <Select value={searchField} onValueChange={handleSearchFieldChange}>
            <SelectTrigger className="w-[140px] rounded-r-none border-r-0 shadow-none h-8 text-xs thaifont">
              <SelectValue placeholder="ค้นหาโดย" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="buyin.tank_number" className="thaifont">
                เลขตัวถัง
              </SelectItem>
              <SelectItem value="buyin.engine_number" className="thaifont">
                เลขเครื่องยนต์
              </SelectItem>
              <SelectItem value="stockInfo.index_number" className="thaifont">
                เลขดัชนี
              </SelectItem>
              <SelectItem value="stockInfo.old_license_plate" className="thaifont">
                ทะเบียนรถ
              </SelectItem>
            </SelectContent>
          </Select>
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={
                searchField.includes("tank")
                  ? "ค้นหาเลขตัวถัง..."
                  : searchField.includes("engine")
                    ? "ค้นหาเลขเครื่องยนต์..."
                    : searchField.includes("index")
                      ? "ค้นหาเลขดัชนี..."
                      : "ค้นหาทะเบียนรถ..."
              }
              value={searchValue}
              onChange={(e) => handleSearchChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pl-8 rounded-l-none rounded-r-none border-r-0 shadow-none h-8 text-xs thaifont"
            />
          </div>
          <Button
            onClick={applySearch}
            className="rounded-l-none bg-primary hover:bg-primary-dark h-8 px-2"
            disabled={isSearching}
          >
            {isSearching ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
            ) : (
              <ArrowRight className="h-4 w-4" />
            )}
            <span className="sr-only">ค้นหา</span>
          </Button>
        </div>
        <div className="flex items-center gap-2">
          {(columnFilters.length > 0 || dateFilters.length > 0 || numberFilters.length > 0) && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFilters}
              className="h-8 text-xs thaifont hover:bg-red-50 hover:text-red-600 hover:border-red-200 transition-colors"
            >
              <X className="mr-1 h-3 w-3" />
              ล้างตัวกรอง
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvancedFilter(!showAdvancedFilter)}
            className={`h-8 text-xs thaifont transition-colors ${
              showAdvancedFilter
                ? "bg-primary-light text-white border-primary-light hover:bg-primary hover:text-white"
                : "hover:bg-primary/10 hover:border-primary/30"
            }`}
          >
            <Filter className="mr-1 h-3 w-3" />
            ตัวกรองขั้นสูง
          </Button>
          <DropdownMenu onOpenChange={handleColumnDropdownOpenChange}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 text-xs thaifont">
                คอลัมน์ <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[240px] max-h-[500px] overflow-auto">
              <div className="p-2 sticky top-0 bg-background z-10 border-b">
                <div className="relative">
                  <Search className="absolute left-2 top-1.5 h-3.5 w-3.5 text-muted-foreground" />
                  <Input
                    placeholder="ค้นหาคอลัมน์..."
                    value={columnSearchQuery}
                    onChange={(e) => setColumnSearchQuery(e.target.value)}
                    className="pl-7 h-8 text-xs thaifont"
                  />
                </div>
              </div>

              <div className="p-2 space-y-1.5 border-b">
                <DropdownMenuCheckboxItem
                  className="capitalize text-xs thaifont font-semibold bg-primary/5 rounded-md"
                  checked={allColumnsVisible}
                  onCheckedChange={toggleAllColumns}
                >
                  <Layers className="h-3.5 w-3.5 mr-2 text-primary" />
                  แสดงทั้งหมด
                </DropdownMenuCheckboxItem>

                <DropdownMenuCheckboxItem
                  className="capitalize text-xs thaifont font-semibold bg-primary/5 rounded-md"
                  onCheckedChange={() => resetToDefaultColumns()}
                >
                  <RotateCcw className="h-3.5 w-3.5 mr-2 text-primary" />
                  แสดงค่าเริ่มต้น
                </DropdownMenuCheckboxItem>
              </div>

              {columnGroups.map((group) => {
                // Filter fields based on search query
                const filteredFields = group.fields.filter(
                  (field) =>
                    columnSearchQuery === "" || field.label.toLowerCase().includes(columnSearchQuery.toLowerCase()),
                )

                // Skip the group if no fields match the search
                if (filteredFields.length === 0) return null

                const groupVisible = isGroupVisible(group.fields)

                return (
                  <React.Fragment key={group.name}>
                    <div className="px-2 py-1.5 bg-muted/50">
                      <DropdownMenuCheckboxItem
                        className="capitalize text-xs thaifont font-semibold rounded-md"
                        checked={groupVisible}
                        onCheckedChange={(value) => toggleGroupColumns(group.fields, !!value)}
                      >
                        <FolderOpen className="h-3.5 w-3.5 mr-2 text-primary/70" />
                        {group.name}
                      </DropdownMenuCheckboxItem>
                    </div>

                    <div className="py-1">
                      {filteredFields.map((field) => {
                        const column = table.getColumn(field.id)
                        if (!column || !column.getCanHide()) return null

                        return (
                          <DropdownMenuCheckboxItem
                            key={field.id}
                            className="capitalize text-xs thaifont pl-8 mx-1 rounded hover:bg-muted"
                            checked={column.getIsVisible()}
                            onCheckedChange={(value) => column.toggleVisibility(!!value)}
                          >
                            <FileText className="h-3 w-3 mr-2 text-muted-foreground" />
                            {field.label}
                          </DropdownMenuCheckboxItem>
                        )
                      })}
                    </div>
                    <DropdownMenuSeparator />
                  </React.Fragment>
                )
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {showAdvancedFilter && (
        <div className="rounded-md border p-2 bg-gray-50 mb-2">
          <AdvancedFilter onFilterApply={handleAdvancedFilterApply} />
        </div>
      )}

      <div className="rounded-md border overflow-hidden flex-1 flex flex-col shadow-sm">
        <div className="flex-1 overflow-auto no-scrollbar">
          <Table className="data-table">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="bg-primary-dark hover:bg-primary-dark">
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        className="text-white font-medium text-xs"
                        style={{ width: header.column.getSize() }}
                      >
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                    {row.getVisibleCells().map((cell) => {
                      // Check if this is a date column and format it
                      const columnId = cell.column.id
                      const value = cell.getValue()

                      // Format date cells to DD/MM/YYYY with Buddhist year
                      const formattedValue = columnId.includes("date") && value ? formatDateCell(value) : value

                      return (
                        <TableCell key={cell.id} className="text-xs" style={{ width: cell.column.getSize() }}>
                          {formattedValue !== value
                            ? formattedValue
                            : flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      )
                    })}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-10 text-center text-xs thaifont">
                    ไม่พบข้อมูล
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      <div className="flex items-center justify-between mt-2">
        <div className="text-xs text-muted-foreground thaifont">
          แสดง <span className="font-medium text-primary">{table.getFilteredRowModel().rows.length}</span> จาก{" "}
          <span className="font-medium">{data.length}</span> รายการ
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-xs text-muted-foreground thaifont">
            หน้า <span className="font-medium text-primary">{table.getState().pagination.pageIndex + 1}</span> จาก{" "}
            <span className="font-medium">{table.getPageCount()}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="h-7 text-xs px-2 thaifont hover:bg-primary/10"
            >
              <ChevronLeft className="h-3 w-3 mr-1" />
              ก่อนหน้า
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="h-7 text-xs px-2 thaifont hover:bg-primary/10"
            >
              ถัดไป
              <ChevronRight className="h-3 w-3 ml-1" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
