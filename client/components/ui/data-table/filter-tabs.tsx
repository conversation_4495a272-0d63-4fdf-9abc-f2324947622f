"use client"

import { useState, useEffect } from "react"
import type { CarComplete } from "@/types/models"

interface FilterTabsProps {
  onFilterChange: (value: string) => void
  data?: CarComplete[]
}

export function FilterTabs({ onFilterChange, data = [] }: FilterTabsProps) {
  const [activeTab, setActiveTab] = useState("all")
  const [counts, setCounts] = useState({
    all: 0,
    purchase: 0,
    transfer: 0,
    repair: 0,
    available: 0,
    finance_request: 0,
    finance_done: 0,
    sold: 0,
    reserved: 0,
  })

  useEffect(() => {
    if (data.length) {
      const newCounts = {
        all: data.length,
        purchase: data.filter((car) => car.stockInfo.car_status === "purchase").length,
        transfer: data.filter((car) => car.stockInfo.car_status === "transfer").length,
        repair: data.filter((car) => car.stockInfo.car_status === "repair" || car.stockInfo.car_status === "in_repair")
          .length,
        available: data.filter((car) => car.stockInfo.car_status === "available").length,
        finance_request: data.filter((car) => car.stockInfo.car_status === "finance_request").length,
        finance_done: data.filter((car) => car.stockInfo.car_status === "finance_done").length,
        sold: data.filter((car) => car.stockInfo.car_status === "sold").length,
        reserved: data.filter((car) => car.stockInfo.car_status === "reserved").length,
      }
      setCounts(newCounts)
    }
  }, [data])

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    onFilterChange(value)
  }

  // Define tabs with Thai translations
  const tabs = [
    { value: "all", label: "ทั้งหมด", count: counts.all },
    { value: "purchase", label: "ซื้อเข้า", count: counts.purchase },
    { value: "transfer", label: "ขนส่ง", count: counts.transfer },
    { value: "repair", label: "ซ่อม", count: counts.repair },
    { value: "available", label: "พร้อมขาย", count: counts.available },
    { value: "finance_request", label: "ขอไฟแนนซ์", count: counts.finance_request },
    { value: "finance_done", label: "ไฟแนนซ์เสร็จ", count: counts.finance_done },
    { value: "sold", label: "ขายแล้ว", count: counts.sold },
    { value: "reserved", label: "จอง", count: counts.reserved },
  ]

  return (
    <div className="flex space-x-1 overflow-auto pb-1 mb-1">
      {tabs.map((tab) => (
        <button
          key={tab.value}
          onClick={() => handleTabChange(tab.value)}
          className={`px-3 py-1 rounded-md flex items-center space-x-1 whitespace-nowrap transition-colors text-sm thaifont ${
            activeTab === tab.value ? "bg-primary text-white" : "bg-gray-100 hover:bg-gray-200 text-gray-700"
          }`}
        >
          <span>{tab.label}</span>
          <span
            className={`ml-1 rounded-full px-1.5 py-0.5 text-xs ${
              activeTab === tab.value ? "bg-white text-primary" : "bg-gray-200 text-gray-700"
            }`}
          >
            {tab.count}
          </span>
        </button>
      ))}
    </div>
  )
}
