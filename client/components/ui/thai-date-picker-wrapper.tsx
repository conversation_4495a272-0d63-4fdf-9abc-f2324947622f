"use client"

import { useEffect, useState, useRef } from "react"
import { ThaiDatePicker } from "thaidatepicker-react"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import dayjs from "dayjs"
import "dayjs/locale/th"
import buddhistEra from "dayjs/plugin/buddhistEra"

dayjs.extend(buddhistEra)
dayjs.locale("th")

export interface ThaiDatePickerWrapperProps {
    value?: string
    onChange?: (date: string) => void
    placeholder?: string
    disabled?: boolean
    className?: string
    required?: boolean
    id?: string
    name?: string
    label?: string
    showClearButton?: boolean
}

export function ThaiDatePickerWrapper({
    value,
    onChange,
    placeholder = "dd/mm/yyyy",
    disabled = false,
    className,
    required = false,
    id,
    name,
    label,
    showClearButton = true,
}: ThaiDatePickerWrapperProps) {
    const [selectedDate, setSelectedDate] = useState<string>(value || "")
    const [manualInput, setManualInput] = useState<string>("")
    const [error, setError] = useState<string>("")
    const [isCalendarOpen, setIsCalendarOpen] = useState(false)
    const containerRef = useRef<HTMLDivElement>(null)
    const inputRef = useRef<HTMLInputElement>(null)
    const datePickerRef = useRef<HTMLDivElement>(null)

    // Helper function to convert Buddhist Era date to Gregorian for calendar display
    const convertBuddhistToGregorianForCalendar = (buddhistDateString: string): string => {
        if (!buddhistDateString) return ""

        try {
            const date = dayjs(buddhistDateString)
            if (date.isValid()) {
                const year = date.year()
                // If year > 2500, it's likely Buddhist Era, convert to Gregorian
                if (year > 2500) {
                    const gregorianYear = year - 543
                    const month = (date.month() + 1).toString().padStart(2, '0')
                    const day = date.date().toString().padStart(2, '0')
                    return `${gregorianYear}-${month}-${day}`
                }
                // If year <= 2500, assume it's already Gregorian
                return buddhistDateString
            }
        } catch (e) {
            console.error('Error converting Buddhist to Gregorian for calendar:', e)
        }
        return ""
    }

    // Update internal state when value prop changes
    useEffect(() => {
        if (value) {
            setSelectedDate(value)
            try {
                // Format the date for display in the input field (DD/MM/YYYY)
                // The date is already in Buddhist Era format from the database
                const date = dayjs(value)
                if (date.isValid()) {
                    // Get the date components
                    const day = date.date().toString().padStart(2, '0')
                    const month = (date.month() + 1).toString().padStart(2, '0')

                    // Display the Buddhist Era year directly (no conversion needed)
                    // since users work with Buddhist Era years
                    const buddhistYear = date.year()

                    // Format the date to display the Buddhist Era year
                    const formattedDate = `${day}/${month}/${buddhistYear}`
                    setManualInput(formattedDate)
                }
            } catch (e) {
                // If there's an error parsing the date, clear the input
                setManualInput("")
            }
        } else {
            setSelectedDate("")
            setManualInput("")
        }
    }, [value])

    // Handle date selection from the calendar
    const handleDateChange = (christDate: string, buddhistDate: string) => {
        console.log('Date selected:', { christDate, buddhistDate });

        // IMPORTANT: The christDate is in Gregorian format from the calendar selection
        // We need to convert it to Buddhist Era for our storage and display
        try {
            // Parse the Gregorian date from calendar selection
            const gregorianDate = dayjs(christDate)
            if (gregorianDate.isValid()) {
                // Get the date components
                const day = gregorianDate.date().toString().padStart(2, '0')
                const month = (gregorianDate.month() + 1).toString().padStart(2, '0')
                const gregorianYear = gregorianDate.year()

                // Convert Gregorian year to Buddhist Era year for storage and display
                const buddhistYear = gregorianYear + 543

                // Create Buddhist Era date string for storage
                const buddhistDateForStorage = `${buddhistYear}-${month}-${day}`

                // Format for display in input field (Buddhist Era)
                const formattedDate = `${day}/${month}/${buddhistYear}`

                setSelectedDate(buddhistDateForStorage)
                setManualInput(formattedDate)
                setError("")

                if (onChange) {
                    onChange(buddhistDateForStorage)
                }
            }
        } catch (e) {
            setManualInput("")
            console.error('Error processing calendar selection:', e)
        }

        // Close the calendar after selecting a date
        setIsCalendarOpen(false)
    }

    // Handle manual input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        const prevValue = manualInput

        // Only allow numbers and slashes
        if (value && !/^[0-9/]*$/.test(value)) {
            return
        }

        // Prevent more than 10 characters (DD/MM/YYYY format)
        if (value.length > 10) {
            return
        }

        // Handle backspace/deletion specifically
        // If user is deleting a slash, also remove the character before it
        if (value.length < prevValue.length) {
            // Check if we're deleting a slash
            if (
                (prevValue.length === 3 && value.length === 2 && prevValue.endsWith('/')) ||
                (prevValue.length === 6 && value.length === 5 && prevValue.charAt(5) === '/')
            ) {
                // Remove the character before the slash too
                const newValue = value.slice(0, -1)
                setManualInput(newValue)

                // Clear error when user is editing
                if (error) setError("")

                // If input is now empty, clear the date
                if (!newValue) {
                    setSelectedDate("")
                    if (onChange) onChange("")
                }
                return
            }
        }

        setManualInput(value)

        // Clear error when user starts typing
        if (error) setError("")

        // If input is empty, clear the date
        if (!value) {
            setSelectedDate("")
            if (onChange) onChange("")
            return
        }

        // Auto-format the input as the user types
        if (value.length === 2 || value.length === 5) {
            if (!value.endsWith('/')) {
                setManualInput(value + '/')
            }
        }

        // Try to parse the input as a date when it's complete
        if (value.length === 10) { // Complete date format (DD/MM/YYYY)
            try {
                const [day, month, year] = value.split('/')
                if (day && month && year) {
                    const inputYear = parseInt(year)

                    // IMPORTANT: The user is entering a Buddhist Era year (e.g., 2565) in the input field
                    // We use this directly without conversion since users work with Buddhist Era
                    const buddhistYear = inputYear;

                    // Validate date using the Buddhist year directly
                    const dateStr = `${buddhistYear}-${month}-${day}`
                    const date = dayjs(dateStr)

                    if (date.isValid()) {
                        // Store the date with Buddhist year format for the database
                        const buddhistDate = date.format("YYYY-MM-DD")
                        setSelectedDate(buddhistDate)
                        setError("")
                        if (onChange) onChange(buddhistDate)
                    } else {
                        setError("วันที่ไม่ถูกต้อง")
                    }
                }
            } catch (e) {
                setError("รูปแบบวันที่ไม่ถูกต้อง")
            }
        }
    }

    // Apply Thai font to all elements when component mounts
    useEffect(() => {
        if (!containerRef.current) return

        // Add Thai font class to all date picker elements
        const applyThaiFontToDatePicker = () => {
            const datePickerElements = containerRef.current?.querySelectorAll("*")
            if (datePickerElements) {
                datePickerElements.forEach((element) => {
                    if (element instanceof HTMLElement) {
                        element.classList.add("thaifont")
                        element.style.fontFamily = "var(--font-sarabun)"
                    }
                })
            }
        }

        // Run initially
        applyThaiFontToDatePicker()

        // Set up observer for dynamically added elements
        const observer = new MutationObserver(applyThaiFontToDatePicker)
        observer.observe(containerRef.current, { childList: true, subtree: true })

        return () => observer.disconnect()
    }, [])

    // Handle clicks outside the component to close the calendar
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            // Only process if calendar is open
            if (!isCalendarOpen) return

            // Check if the click was outside the component
            if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
                setIsCalendarOpen(false)
            }
        }

        // Handle keyboard events (Escape key to close calendar)
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && isCalendarOpen) {
                setIsCalendarOpen(false)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        document.addEventListener('keydown', handleKeyDown)

        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
            document.removeEventListener('keydown', handleKeyDown)
        }
    }, [isCalendarOpen])

    // Handle clearing the date
    const handleClear = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent calendar from opening
        setSelectedDate("");
        setManualInput("");
        setError("");
        setIsCalendarOpen(false); // Close calendar if open
        if (onChange) {
            onChange("");
        }
    }

    // Handle calendar icon click
    const handleCalendarIconClick = () => {
        // Toggle calendar visibility
        setIsCalendarOpen(!isCalendarOpen)
    }

    return (
        <div ref={containerRef} className={cn("space-y-1 thai-datepicker-container", className)}>
            {label && (
                <Label htmlFor={id} className="text-xs font-medium thaifont">
                    {label} {required && <span className="text-red-500">*</span>}
                </Label>
            )}
            <div className="thai-date-picker-wrapper relative">
                <div className="relative">
                    <input
                        ref={inputRef}
                        type="text"
                        id={id}
                        name={name}
                        value={manualInput}
                        onChange={handleInputChange}
                        onKeyDown={(e) => {
                            // Handle Delete key to clear the entire field
                            if (e.key === 'Delete' && e.ctrlKey) {
                                e.preventDefault();
                                setManualInput("");
                                setSelectedDate("");
                                setError("");
                                if (onChange) onChange("");
                                return;
                            }

                            // Handle backspace at positions right after a slash
                            if (e.key === 'Backspace') {
                                const input = e.target as HTMLInputElement;
                                const cursorPosition = input.selectionStart;

                                // If cursor is right after a slash, delete both the slash and the character before it
                                if (cursorPosition === 3 || cursorPosition === 6) {
                                    if (manualInput.charAt(cursorPosition - 1) === '/') {
                                        e.preventDefault();
                                        const newValue =
                                            manualInput.substring(0, cursorPosition - 2) +
                                            manualInput.substring(cursorPosition);
                                        setManualInput(newValue);

                                        // Set cursor position
                                        setTimeout(() => {
                                            if (input) {
                                                input.selectionStart = cursorPosition - 2;
                                                input.selectionEnd = cursorPosition - 2;
                                            }
                                        }, 0);
                                    }
                                }
                            }

                            // Handle Tab key to move to the next field
                            if (e.key === 'Tab') {
                                // Let the default tab behavior work
                                // This will move focus to the next focusable element
                            }
                        }}
                        placeholder={placeholder}
                        disabled={disabled}
                        tabIndex={0}
                        className={cn(
                            "w-full rounded-md border border-input bg-background px-3 pr-8 py-1 text-sm shadow-sm transition-colors",
                            "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",
                            disabled && "cursor-not-allowed opacity-50",
                            "h-7 text-xs thaifont thai-date-input form-input",
                            error && "border-red-500",
                            className
                        )}
                    />
                    <button
                        type="button"
                        className={cn(
                            "absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none",
                            isCalendarOpen && "text-primary"
                        )}
                        onClick={handleCalendarIconClick}
                        aria-label="Open calendar"
                        tabIndex={0}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        >
                            <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                            <line x1="16" x2="16" y1="2" y2="6" />
                            <line x1="8" x2="8" y1="2" y2="6" />
                            <line x1="3" x2="21" y1="10" y2="10" />
                        </svg>
                    </button>
                </div>

                {/* Calendar popup container */}
                {isCalendarOpen && (
                    <div
                        ref={datePickerRef}
                        className="absolute top-full left-0 mt-1 z-[9999] bg-white shadow-lg rounded-md border border-gray-200"
                    >
                        <ThaiDatePicker
                            value={convertBuddhistToGregorianForCalendar(selectedDate)}
                            onChange={handleDateChange}
                            yearBoundary={50}
                            clearable={false}
                            reactDatePickerProps={{
                                popperClassName: "thai-datepicker-popper thaifont",
                                calendarClassName: "thaifont",
                                dayClassName: () => "thaifont",
                                monthClassName: () => "thaifont",
                                timeClassName: () => "thaifont",
                                weekDayClassName: () => "thaifont",
                                popperPlacement: "bottom-start",
                                open: true, // Force the calendar to be open
                                onCalendarOpen: () => {
                                    setIsCalendarOpen(true)
                                },
                                onCalendarClose: () => {
                                    setIsCalendarOpen(false)
                                },
                                // Inline styles to ensure the calendar is visible
                                inline: true
                            }}
                        />
                    </div>
                )}

                {showClearButton && manualInput && (
                    <button
                        type="button"
                        onClick={handleClear}
                        className="absolute right-8 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 z-10"
                        aria-label="Clear date"
                        tabIndex={0}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        >
                            <circle cx="12" cy="12" r="10" />
                            <path d="m15 9-6 6" />
                            <path d="m9 9 6 6" />
                        </svg>
                    </button>
                )}
                {error && (
                    <div className="text-red-500 text-xs mt-1 thaifont">
                        {error}
                    </div>
                )}
            </div>
        </div>
    )
}
