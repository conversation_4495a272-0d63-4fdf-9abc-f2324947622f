"use client"

import React from "react"
import { format } from "date-fns"
import { th } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { BuddhistCalendar } from "@/components/ui/buddhist-calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"

export interface BuddhistDatePickerProps {
    value?: Date | string
    onChange?: (date: string) => void
    placeholder?: string
    disabled?: boolean
    className?: string
    calendarClassName?: string
    showClearButton?: boolean
    required?: boolean
    id?: string
    name?: string
    label?: string
    format?: string // Add format prop
}

export function BuddhistDatePicker({
    value,
    onChange,
    placeholder = "dd/MM/yyyy", // Update default placeholder
    disabled = false,
    className,
    calendarClassName,
    showClearButton = true,
    required = false,
    id,
    name,
    label,
    format: displayFormat = "dd/MM/yyyy", // Default display format
}: BuddhistDatePickerProps) {
    // Convert string date to Date object if needed
    const parseDate = (dateValue: Date | string | undefined): Date | undefined => {
        if (!dateValue) return undefined
        if (dateValue instanceof Date) return dateValue
        try {
            return new Date(dateValue)
        } catch (e) {
            console.error("Invalid date format:", e)
            return undefined
        }
    }

    const [date, setDate] = React.useState<Date | undefined>(parseDate(value))
    const [open, setOpen] = React.useState(false)

    // Update internal state when value prop changes
    React.useEffect(() => {
        setDate(parseDate(value))
    }, [value])

    // Format date to Buddhist Era (BE) for display
    const formatBuddhistDate = (date: Date | undefined): string => {
        if (!date) return ""

        try {
            // Format date with Thai locale which uses Buddhist calendar
            return format(date, displayFormat, { locale: th })
        } catch (e) {
            console.error("Error formatting date:", e)
            return ""
        }
    }

    // Handle date selection
    const handleSelect = (selectedDate: Date | undefined) => {
        setDate(selectedDate)
        setOpen(false)

        if (onChange && selectedDate) {
            // Format the selected date to yyyy-MM-dd for consistency
            const formattedDate = format(selectedDate, "yyyy-MM-dd")
            onChange(formattedDate)
        } else if (onChange && !selectedDate) {
            onChange("")
        }
    }

    // Handle clear button click
    const handleClear = (e: React.MouseEvent) => {
        e.stopPropagation()
        handleSelect(undefined)
    }

    return (
        <div className={cn("space-y-2", className)}>
            {label && (
                <label htmlFor={id} className="text-sm font-medium thaifont">
                    {label} {required && <span className="text-red-500">*</span>}
                </label>
            )}
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        variant="outline"
                        className={cn(
                            "w-full justify-start text-left font-normal h-10",
                            !date && "text-muted-foreground",
                            disabled && "opacity-50 cursor-not-allowed",
                        )}
                        disabled={disabled}
                    >
                        <div className="flex items-center w-full justify-between">
                            <div className="flex items-center">
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {date ? formatBuddhistDate(date) : placeholder}
                            </div>
                            {showClearButton && date && (
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-full" onClick={handleClear}>
                                    <span className="sr-only">Clear</span>
                                    <span className="text-xs">✕</span>
                                </Button>
                            )}
                        </div>
                    </Button>
                </PopoverTrigger>
                <PopoverContent className={cn("w-auto p-0", calendarClassName)} align="start">
                    <BuddhistCalendar
                        mode="single"
                        selected={date}
                        onSelect={handleSelect}
                        disabled={disabled}
                        className="border-none shadow-none"
                    />
                </PopoverContent>
            </Popover>
        </div>
    )
}
