"use client"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import type { RepairHistoryEntry } from "@/types/audit"
import { formatCurrency } from "@/lib/utils"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface RepairHistoryModalProps {
  isOpen: boolean
  onClose: () => void
  onSave?: () => void
  history: RepairHistoryEntry[]
}

export function RepairHistoryModal({ isOpen, onClose, onSave, history }: RepairHistoryModalProps) {
  // Format the timestamp for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Format column name for display
  const formatColumnName = (columnName: string) => {
    return columnName
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  // All repair fields that should be displayed
  const allRepairFields = [
    "repainting_cost",
    "engine_repair_cost",
    "suspension_repair_cost",
    "autopart_cost",
    "battery_cost",
    "tires_wheels_cost",
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] flex flex-col p-0">
        <DialogHeader className="px-6 py-5 border-b bg-gray-50">
          <DialogTitle className="text-xl font-bold text-primary-dark">Repair History</DialogTitle>
          <DialogDescription className="mt-2">View the complete repair history for this vehicle</DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-[calc(90vh-180px)] w-full">
            {history.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">No repair history found for this vehicle.</div>
            ) : (
              <div className="space-y-8 p-6">
                {history.map((entry) => {
                  // Create a map of column_name to new_value for this entry
                  const detailsMap = entry.details.reduce(
                    (map, detail) => {
                      map[detail.column_name] = detail.new_value || 0
                      return map
                    },
                    {} as Record<string, number>,
                  )

                  return (
                    <div key={entry.audit_id} className="border rounded-lg overflow-hidden shadow-sm">
                      <div className="bg-primary/5 p-5 border-b flex justify-between items-center">
                        <div>
                          <h3 className="font-medium text-primary-dark text-base">
                            {entry.description || "Repair Service"}
                          </h3>
                          <p className="text-sm text-muted-foreground mt-1">{formatDate(entry.timestamp)}</p>
                        </div>
                        <Badge className="bg-primary-light text-white px-3 py-1.5 text-sm">
                          Audit #{entry.audit_id}
                        </Badge>
                      </div>

                      <div className="p-6 bg-white">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {/* Always display all repair fields */}
                          {allRepairFields.map((fieldName) => (
                            <div key={fieldName} className="space-y-2.5">
                              <Label
                                htmlFor={`${entry.audit_id}-${fieldName}`}
                                className="text-sm font-medium text-gray-700"
                              >
                                {formatColumnName(fieldName)}
                              </Label>
                              <Input
                                id={`${entry.audit_id}-${fieldName}`}
                                value={formatCurrency(detailsMap[fieldName] || 0)}
                                readOnly
                                className="bg-gray-50 border-gray-200 font-medium text-gray-800"
                              />
                            </div>
                          ))}
                        </div>

                        <div className="mt-6 pt-5 border-t border-gray-200">
                          <div className="space-y-2.5">
                            <Label htmlFor={`total-${entry.audit_id}`} className="text-sm font-medium text-gray-700">
                              Total Repair Cost
                            </Label>
                            <Input
                              id={`total-${entry.audit_id}`}
                              value={formatCurrency(
                                allRepairFields.reduce((sum, field) => sum + (detailsMap[field] || 0), 0),
                              )}
                              readOnly
                              className="bg-primary/5 border-gray-200 font-semibold text-primary-dark"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </ScrollArea>
        </div>

        <DialogFooter className="px-6 py-4 border-t bg-gray-50 mt-auto">
          <div className="flex gap-3 ml-auto">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-primary text-primary hover:bg-primary/10"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (onSave) onSave()
                onClose()
              }}
              className="bg-primary hover:bg-primary-dark text-white px-6"
            >
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
