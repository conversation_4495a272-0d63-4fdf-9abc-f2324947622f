"use client"

import React, { useState, useEffect, useMemo } from "react"
import "@/styles/date-picker-custom.css"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "lucide-react"
import { CustomSelect } from "@/components/ui/custom-select"
import { ThaiDatePickerWrapper } from "@/components/ui/thai-date-picker-wrapper"
import { getProvinceOptions } from "@/utils/province-utils"
import { cn } from "@/lib/utils"

// Utility functions for number formatting
const formatNumber = (value: string | number): string => {
  if (value === undefined || value === null || value === '') return '';

  // Convert to number and handle potential NaN
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return '';

  // Format with commas for thousands
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const parseFormattedNumber = (formattedValue: string): number => {
  // Remove commas and convert to number
  const cleanValue = formattedValue.replace(/,/g, '');
  const num = parseFloat(cleanValue);
  return isNaN(num) ? 0 : num;
};

// ControlledInput component for formatted number inputs
const ControlledInput = React.memo(
  ({
    id,
    label,
    type = "text",
    initialValue = "",
    onChange,
    disabled = false,
    error = "",
    className = "",
    suffix = "",
    formatAsNumber = type === "number", // Default to true for number fields
  }: {
    id: string
    label: string
    type?: string
    initialValue?: string
    onChange: (value: string) => void
    disabled?: boolean
    error?: string
    className?: string
    suffix?: string
    formatAsNumber?: boolean
  }) => {
    // Format the initial value if it's a number field
    const formattedInitialValue = useMemo(() => {
      if (formatAsNumber && initialValue) {
        return formatNumber(initialValue);
      }
      return initialValue;
    }, [formatAsNumber, initialValue]);

    // Use local state to manage the input value
    const [value, setValue] = useState(formattedInitialValue);
    const [isFocused, setIsFocused] = useState(false);

    // Update local state when initialValue changes
    useEffect(() => {
      if (!isFocused) {
        if (formatAsNumber && initialValue) {
          setValue(formatNumber(initialValue));
        } else {
          setValue(initialValue);
        }
      }
    }, [initialValue, formatAsNumber, isFocused]);

    // Handle input changes locally
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;

      if (formatAsNumber) {
        // Remove any existing commas first
        const cleanValue = newValue.replace(/,/g, '');

        // Allow typing numbers, decimal point, and backspace
        if (/^[0-9]*\.?[0-9]*$/.test(cleanValue) || cleanValue === '') {
          // For real-time formatting, we need to:
          // 1. Keep track of cursor position
          const cursorPosition = e.target.selectionStart || 0;
          const previousLength = e.target.value.length;

          // 2. Format the number for display
          if (cleanValue === '') {
            setValue('');
          } else {
            // Only format with commas if there's no decimal point being typed at the end
            if (cleanValue.endsWith('.')) {
              // If user just typed a decimal point, don't format yet
              setValue(cleanValue);
            } else {
              // Split by decimal point to format only the integer part
              const parts = cleanValue.split('.');
              const integerPart = parts[0];
              const decimalPart = parts.length > 1 ? parts[1] : '';

              // Format integer part with commas
              let formattedInteger = '';
              if (integerPart) {
                formattedInteger = Number(integerPart).toLocaleString('en-US');
              }

              // Combine with decimal part if it exists
              const formattedValue = decimalPart
                ? `${formattedInteger}.${decimalPart}`
                : formattedInteger;

              setValue(formattedValue);

              // 3. Adjust cursor position after formatting
              setTimeout(() => {
                if (e.target) {
                  // Calculate new cursor position based on how many commas were added/removed
                  const lengthDifference = formattedValue.length - previousLength;
                  const newPosition = cursorPosition + lengthDifference;
                  e.target.setSelectionRange(newPosition, newPosition);
                }
              }, 0);
            }
          }
        }
      } else {
        setValue(newValue);
      }
      // Only propagate changes to parent on blur to reduce re-renders
    };

    // Handle focus to maintain the current display
    const handleFocus = () => {
      setIsFocused(true);
    };

    // Propagate changes to parent on blur and ensure consistent formatting
    const handleBlur = () => {
      setIsFocused(false);

      if (formatAsNumber) {
        // Parse the value, send the raw number to parent
        const cleanValue = value.replace(/,/g, '');
        const parsedValue = parseFormattedNumber(cleanValue).toString();
        onChange(parsedValue);

        // Ensure consistent formatting with 2 decimal places
        if (value !== '') {
          setValue(formatNumber(parsedValue));
        }
      } else {
        onChange(value);
      }
    };

    return (
      <div className="space-y-2">
        <Label htmlFor={id} className={error ? "text-red-500" : disabled ? "text-gray-400" : ""}>
          {label} {suffix && <span className="text-gray-500">{suffix}</span>}
        </Label>
        <Input
          id={id}
          type={formatAsNumber ? "text" : type} // Use text type for formatted numbers
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          className={cn(className, error ? "border-red-500" : "", disabled ? "bg-gray-100" : "")}
          tabIndex={0} // Ensure all inputs are tabbable
        />
        {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
      </div>
    )
  },
)
ControlledInput.displayName = "ControlledInput"

interface AddItemModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: any) => void
}

export function AddItemModal({ isOpen, onClose, onSave }: AddItemModalProps) {
  const [formData, setFormData] = useState({
    // Important Information
    is_auction_car: "Personal car",
    purchase_date: "",

    // Purchase Auction Information
    auction_order: "",
    auction_name: "",
    auction_provinced: "",
    qc1_auction_lot: 0,
    auction_checker: "",
    parking_location: "",
    type_of_transport: "",
    auction_transporter: "",
    transport_3_tl_payment: 0,
    transport_2_personal_payment: 0,
    purchase_price: 0,
    operation_cost_incl_vat: 0,
    transport_1_auction_lot: 0,
    initial_check: 0,
    tax_insurance_cost_zero: 0,
    other_costs_seven: 0,

    // Car Important Information
    brand: "",
    model: "",
    color: "",
    gear: "",
    year: new Date().getFullYear().toString(), // Changed to string for custom input
    tank_number: "",
    engine_number: "",
    old_license_plate: "",
    registration_date: "",

    // Additional fields (not shown in main form but kept for data consistency)
    vat_percent: 0.07,
    purchase_vat_percent: 0,
    five_three_tax_percentage: 0,
    total_purchase_cost: 0,
    book_deposit: 0,
    ems_registration_qc3: 0,
    registration_fee: 0,

    // Repair costs
    repainting_cost: 0,
    engine_repair_cost: 0,
    suspension_repair_cost: 0,
    autopart_cost: 0,
    battery_cost: 0,
    tires_wheels_cost: 0,

    // Commission costs
    commission_s: 0,
    commission_agent: 0,
    commission_manager: 0,

    // Dependent calculated values
    total_investment: 0,
    listed_price: 0,
  })

  // Effect to handle conditional field logic
  useEffect(() => {
    // When transport type changes, reset the disabled field to 0
    if (formData.type_of_transport === "Personal") {
      setFormData((prev) => ({ ...prev, transport_3_tl_payment: 0 }))
    } else if (formData.type_of_transport) {
      setFormData((prev) => ({ ...prev, transport_2_personal_payment: 0 }))
    }
  }, [formData.type_of_transport])

  // Effect to calculate dependent values whenever relevant fields change
  useEffect(() => {
    // Calculate purchase_vat_percent
    const purchase_vat_percent = Number.parseFloat((formData.purchase_price * formData.vat_percent).toFixed(2))

    // Calculate five_three_tax_percentage
    const five_three_tax_percentage = formData.vat_percent
      ? Number.parseFloat(
        (
          (formData.operation_cost_incl_vat +
            formData.transport_1_auction_lot +
            formData.initial_check +
            formData.other_costs_seven) *
          (100 / 107) * -0.03
        ).toFixed(2),
      )
      : 0

    // Calculate total_purchase_cost
    const total_purchase_cost = Number.parseFloat(
      (
        formData.purchase_price +
        purchase_vat_percent +
        formData.operation_cost_incl_vat +
        formData.transport_1_auction_lot +
        formData.initial_check +
        formData.tax_insurance_cost_zero +
        formData.other_costs_seven +
        five_three_tax_percentage
      ).toFixed(2),
    )

    // Calculate total_investment
    const total_investment = Number.parseFloat(
      (
        formData.transport_2_personal_payment +
        formData.transport_3_tl_payment +
        formData.registration_fee +
        formData.ems_registration_qc3 +
        formData.purchase_price +
        purchase_vat_percent +
        formData.operation_cost_incl_vat +
        formData.transport_1_auction_lot +
        formData.initial_check +
        formData.tax_insurance_cost_zero +
        formData.other_costs_seven +
        five_three_tax_percentage +
        formData.repainting_cost +
        formData.engine_repair_cost +
        formData.suspension_repair_cost +
        formData.autopart_cost +
        formData.battery_cost +
        formData.tires_wheels_cost +
        formData.commission_s +
        formData.commission_agent +
        formData.commission_manager +
        formData.qc1_auction_lot
      ).toFixed(2),
    )

    // Calculate listed_price
    const listed_price = Number.parseFloat((total_investment + 60000).toFixed(2))

    setFormData((prev) => ({
      ...prev,
      purchase_vat_percent,
      five_three_tax_percentage,
      total_purchase_cost,
      total_investment,
      listed_price,
    }))
  }, [
    formData.purchase_price,
    formData.vat_percent,
    formData.operation_cost_incl_vat,
    formData.transport_1_auction_lot,
    formData.initial_check,
    formData.tax_insurance_cost_zero,
    formData.other_costs_seven,
    formData.transport_2_personal_payment,
    formData.transport_3_tl_payment,
    formData.registration_fee,
    formData.ems_registration_qc3,
    formData.repainting_cost,
    formData.engine_repair_cost,
    formData.suspension_repair_cost,
    formData.autopart_cost,
    formData.battery_cost,
    formData.tires_wheels_cost,
    formData.commission_s,
    formData.commission_agent,
    formData.commission_manager,
    formData.qc1_auction_lot,
  ])

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  // Handler for number inputs with two decimal places
  const handleNumberChange = (field: string, value: string) => {
    // Parse the value to ensure it's a number
    const numValue = value === "" ? 0 : Number.parseFloat(Number.parseFloat(value).toFixed(2))
    setFormData((prev) => ({ ...prev, [field]: numValue }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Convert year from string to number before saving
    const processedData = { ...formData }
    const yearAsNumber = Number.parseInt(formData.year) || new Date().getFullYear()
    processedData.year = yearAsNumber.toString()

    // All dependent calculations are already in the formData state
    onSave(processedData)
  }

  // Generate years for the year selector (current year down to 30 years ago)
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 30 }, (_, i) => ({
    value: (currentYear - i).toString(),
    label: (currentYear - i).toString(),
  }))

  // Define options for select components
  const brandsOptions = [
    { value: "Toyota", label: "Toyota" },
    { value: "Honda", label: "Honda" },
    { value: "Nissan", label: "Nissan" },
    { value: "Mazda", label: "Mazda" },
    { value: "Mitsubishi", label: "Mitsubishi" },
    { value: "Isuzu", label: "Isuzu" },
    { value: "Ford", label: "Ford" },
    { value: "Chevrolet", label: "Chevrolet" },
  ]

  const gearsOptions = [
    { value: "Automatic", label: "Automatic" },
    { value: "Manual", label: "Manual" },
  ]

  const locationsOptions = [
    { value: "Lamphun-1", label: "LP1" },
    { value: "Lamphun-2", label: "LP2" },
    { value: "Chiang mai", label: "CNX" },
    { value: "Bangkok", label: "BKK" }
  ]

  const auctionNamesOptions = [
    { value: "ST.", label: "สท." },
    { value: "SIA", label: "SIA" },
    { value: "MOTTO", label: "MOTTO" },
    { value: "SH", label: "สห" },
    { value: "APPLE", label: "APPLE" },
    { value: "IA", label: "IA" },
    { value: "PIA", label: "PIA" },
    { value: "SCB", label: "SCB" },
    { value: "AAA", label: "AAA" },
    { value: "INTER", label: "สากล" },
    { value: "APP", label: "APP" },
    { value: "AX", label: "AX" }
  ];
  const transportTypesOptions = [
    { value: "Personal", label: "พนักงานนำส่ง" },
    { value: "Company", label: "บริษัทนำส่ง" },
  ]

  const carTypesOptions = [
    { value: "Personal car", label: "รถบุคคลธรรมดา" },
    { value: "Company car", label: "รถนิติบุคคล" },
  ]
  const auctionCheckersOptions = [
    { value: "P", label: "P" },
    { value: "NOK", label: "NOK" },
    { value: "HOM", label: "HOM" },
    { value: "NAT", label: "NAT" },
    { value: "TOP", label: "TOP" },
    { value: "MAT", label: "MAT" },
    { value: "J", label: "J" },
    { value: "RUNG", label: "RUNG" },
    { value: "S-BO", label: "S-BO" },
    { value: "NON", label: "NON" },
    { value: "S-JACK", label: "S-JACK" },
    { value: "MAY", label: "MAY" },
    { value: "TONG", label: "TONG" }
  ];
  // Replace the static provincesOptions with a state variable
  const [provincesOptions, setProvincesOptions] = useState<Array<{ value: string; label: string }>>([])

  // Add useEffect to load provinces when component mounts
  useEffect(() => {
    try {
      const options = getProvinceOptions()
      setProvincesOptions(options)
    } catch (error) {
      console.error("Failed to load provinces:", error)
      // Fallback to some default provinces if the geothai package fails
      setProvincesOptions([
        { value: "Bangkok", label: "กรุงเทพฯ" },
        { value: "Chiang Mai", label: "เชียงใหม่" },
        { value: "Phuket", label: "ภูเก็ต" },
        { value: "Chonburi", label: "ชลบุรี" },
        { value: "Khon Kaen", label: "ขอนแก่น" },
        { value: "Songkhla", label: "สงขลา" },
      ])
    }
  }, [])
  // Static provinces list (reverting to previous approach)
  // const provincesOptions = [
  //   { value: "Bangkok", label: "Bangkok" },
  //   { value: "Chiang Mai", label: "Chiang Mai" },
  //   { value: "Phuket", label: "Phuket" },
  //   { value: "Chonburi", label: "Chonburi" },
  //   { value: "Khon Kaen", label: "Khon Kaen" },
  //   { value: "Songkhla", label: "Songkhla" },
  // ]

  // Check if a field should be disabled based on conditions
  const isFieldDisabled = (fieldName: string): boolean => {
    if (fieldName === "transport_3_tl_payment") {
      return formData.type_of_transport === "Personal"
    }
    if (fieldName === "transport_2_personal_payment") {
      return formData.type_of_transport !== "" && formData.type_of_transport !== "Personal"
    }
    return false
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col p-6">
        <DialogHeader className="px-0">
          <DialogTitle className="text-xl font-bold text-primary-dark thaifont">เพิ่มรถยนต์ใหม่</DialogTitle>
          <DialogDescription className="thaifont">กรอกรายละเอียดรถยนต์ด้านล่างเพื่อเพิ่มรายการใหม่ในคลัง</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex-1 overflow-hidden flex flex-col">
          <ScrollArea className="h-[calc(100vh-300px)] pr-4 -mr-4">
            <div className="space-y-6 pb-4">
              {/* Important Information Section */}
              <Card>
                <CardHeader className="bg-primary/10 py-2">
                  <CardTitle className="text-primary text-lg thaifont">ข้อมูลสำคัญ</CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <CustomSelect
                      id="is_auction_car"
                      label="ประเภทรถ"
                      value={formData.is_auction_car}
                      options={carTypesOptions}
                      onChange={(value) => handleChange("is_auction_car", value)}
                    />

                    <div className="space-y-2">
                      <Label htmlFor="purchase_date" className="thaifont">
                        วันที่ซื้อ
                      </Label>
                      <ThaiDatePickerWrapper
                        id="purchase_date"
                        name="purchase_date"
                        value={formData.purchase_date}
                        onChange={(value) => handleChange("purchase_date", value)}
                        placeholder="dd/mm/yyyy"
                        required
                        className="modal-date-picker"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Car Important Information Section */}
              <Card>
                <CardHeader className="bg-primary/10 py-2">
                  <CardTitle className="text-primary text-lg thaifont">ข้อมูลสำคัญรถยนต์</CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <CustomSelect
                      id="brand"
                      label="ยี่ห้อ"
                      value={formData.brand}
                      options={brandsOptions}
                      onChange={(value) => handleChange("brand", value)}
                    />

                    <div className="space-y-2">
                      <Label htmlFor="model" className="thaifont">
                        รุ่น
                      </Label>
                      <Input
                        id="model"
                        value={formData.model}
                        onChange={(e) => handleChange("model", e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="color" className="thaifont">
                        สี
                      </Label>
                      <Input
                        id="color"
                        value={formData.color}
                        onChange={(e) => handleChange("color", e.target.value)}
                      />
                    </div>

                    <CustomSelect
                      id="gear"
                      label="ประเภทเกียร์"
                      value={formData.gear}
                      options={gearsOptions}
                      onChange={(value) => handleChange("gear", value)}
                    />

                    <CustomSelect
                      id="year"
                      label="ปี"
                      value={formData.year}
                      options={years}
                      onChange={(value) => handleChange("year", value)}
                      icon={<Calendar className="h-4 w-4 text-gray-500" />}
                    />

                    <div className="space-y-2">
                      <Label htmlFor="tank_number" className="thaifont">
                        เลขตัวถัง
                      </Label>
                      <Input
                        id="tank_number"
                        value={formData.tank_number}
                        onChange={(e) => handleChange("tank_number", e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="engine_number" className="thaifont">
                        เลขเครื่องยนต์
                      </Label>
                      <Input
                        id="engine_number"
                        value={formData.engine_number}
                        onChange={(e) => handleChange("engine_number", e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="old_license_plate" className="thaifont">
                        ทะเบียนรถ
                      </Label>
                      <Input
                        id="old_license_plate"
                        value={formData.old_license_plate}
                        onChange={(e) => handleChange("old_license_plate", e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="registration_date" className="thaifont">
                        วันที่จดทะเบียน
                      </Label>
                      <ThaiDatePickerWrapper
                        id="registration_date"
                        name="registration_date"
                        value={formData.registration_date}
                        onChange={(value) => handleChange("registration_date", value)}
                        placeholder="dd/mm/yyyy"
                        required
                        className="modal-date-picker"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Purchase Auction Information Section */}
              <Card>
                <CardHeader className="bg-primary/10 py-2">
                  <CardTitle className="text-primary text-lg thaifont">ข้อมูลการประมูลซื้อ</CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="auction_order" className="thaifont">
                        ลำดับประมูล
                      </Label>
                      <Input
                        id="auction_order"
                        value={formData.auction_order}
                        onChange={(e) => handleChange("auction_order", e.target.value)}
                      />
                    </div>

                    <CustomSelect
                      id="auction_name"
                      label="ชื่อลานประมูล"
                      value={formData.auction_name}
                      options={auctionNamesOptions}
                      onChange={(value) => handleChange("auction_name", value)}
                    />

                    <CustomSelect
                      id="auction_provinced"
                      label="จังหวัดที่ประมูล"
                      value={formData.auction_provinced}
                      options={provincesOptions}
                      onChange={(value) => handleChange("auction_provinced", value)}
                    />
                    <ControlledInput
                      id="purchase_price"
                      label="ราคาซื้อ"
                      type="number"
                      initialValue={formData.purchase_price.toString()}
                      onChange={(value) => handleNumberChange("purchase_price", value)}
                    />



                    <CustomSelect
                      id="auction_checker"
                      label="ผู้ตรวจสอบประมูล"
                      value={formData.auction_checker}
                      options={auctionCheckersOptions}
                      onChange={(value) => handleChange("auction_checker", value)}
                    />

                    <CustomSelect
                      id="parking_location"
                      label="สถานที่จอดรถ"
                      value={formData.parking_location}
                      options={locationsOptions}
                      onChange={(value) => handleChange("parking_location", value)}
                    />
                    <ControlledInput
                      id="transport_1_auction_lot"
                      label="ค่าขนส่ง 1 ลานประมูล"
                      type="number"
                      initialValue={formData.transport_1_auction_lot.toString()}
                      onChange={(value) => handleNumberChange("transport_1_auction_lot", value)}
                    />

                    <ControlledInput
                      id="qc1_auction_lot"
                      label="QC1 ลานประมูล"
                      type="number"
                      initialValue={formData.qc1_auction_lot.toString()}
                      onChange={(value) => handleNumberChange("qc1_auction_lot", value)}
                    />
                    <CustomSelect
                      id="type_of_transport"
                      label="ประเภทการขนส่ง"
                      value={formData.type_of_transport}
                      options={transportTypesOptions}
                      onChange={(value) => handleChange("type_of_transport", value)}
                    />

                    <div className="space-y-2">
                      <Label htmlFor="auction_transporter" className="thaifont">
                        ผู้ขนส่งประมูล
                      </Label>
                      <Input
                        id="auction_transporter"
                        value={formData.auction_transporter}
                        onChange={(e) => handleChange("auction_transporter", e.target.value)}
                      />
                    </div>

                    <ControlledInput
                      id="transport_3_tl_payment"
                      label="ค่าขนส่ง 3 TL"
                      type="number"
                      initialValue={formData.transport_3_tl_payment.toString()}
                      onChange={(value) => handleNumberChange("transport_3_tl_payment", value)}
                      disabled={isFieldDisabled("transport_3_tl_payment")}
                      className={isFieldDisabled("transport_3_tl_payment") ? "bg-gray-100" : ""}
                    />

                    <ControlledInput
                      id="transport_2_personal_payment"
                      label="ค่าขนส่ง 2 ส่วนตัว"
                      type="number"
                      initialValue={formData.transport_2_personal_payment.toString()}
                      onChange={(value) => handleNumberChange("transport_2_personal_payment", value)}
                      disabled={isFieldDisabled("transport_2_personal_payment")}
                      className={isFieldDisabled("transport_2_personal_payment") ? "bg-gray-100" : ""}
                    />



                    <ControlledInput
                      id="operation_cost_incl_vat"
                      label="ค่าดำเนินการรวม VAT"
                      type="number"
                      initialValue={formData.operation_cost_incl_vat.toString()}
                      onChange={(value) => handleNumberChange("operation_cost_incl_vat", value)}
                    />

                    <ControlledInput
                      id="initial_check"
                      label="เช็คต้น"
                      type="number"
                      initialValue={formData.initial_check.toString()}
                      onChange={(value) => handleNumberChange("initial_check", value)}
                    />

                    <ControlledInput
                      id="tax_insurance_cost_zero"
                      label="ภาษี-พรบ.+คชจ(0%)"
                      type="number"
                      initialValue={formData.tax_insurance_cost_zero.toString()}
                      onChange={(value) => handleNumberChange("tax_insurance_cost_zero", value)}
                    />

                    <ControlledInput
                      id="other_costs_seven"
                      label="คชจ.อื่นๆ(7%)"
                      type="number"
                      initialValue={formData.other_costs_seven.toString()}
                      onChange={(value) => handleNumberChange("other_costs_seven", value)}
                    />

                    <ControlledInput
                      id="total_purchase_cost"
                      label="ต้นทุนการซื้อทั้งหมด"
                      type="number"
                      initialValue={formData.total_purchase_cost.toString()}
                      onChange={() => {}} // No-op since this is read-only
                      disabled
                      className="font-medium"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Hidden fields for repair costs - not shown in UI but needed for calculations */}
              <input type="hidden" name="repainting_cost" value={formData.repainting_cost} />
              <input type="hidden" name="engine_repair_cost" value={formData.engine_repair_cost} />
              <input type="hidden" name="suspension_repair_cost" value={formData.suspension_repair_cost} />
              <input type="hidden" name="autopart_cost" value={formData.autopart_cost} />
              <input type="hidden" name="battery_cost" value={formData.battery_cost} />
              <input type="hidden" name="tires_wheels_cost" value={formData.tires_wheels_cost} />

              {/* Hidden fields for commission costs - not shown in UI but needed for calculations */}
              <input type="hidden" name="commission_s" value={formData.commission_s} />
              <input type="hidden" name="commission_agent" value={formData.commission_agent} />
              <input type="hidden" name="commission_manager" value={formData.commission_manager} />

              {/* Hidden fields for dependent calculated values - not shown in UI but included in payload */}
              <input type="hidden" name="purchase_vat_percent" value={formData.purchase_vat_percent} />
              <input type="hidden" name="five_three_tax_percentage" value={formData.five_three_tax_percentage} />
              <input type="hidden" name="total_investment" value={formData.total_investment} />
              <input type="hidden" name="listed_price" value={formData.listed_price} />
            </div>
          </ScrollArea>

          <DialogFooter className="mt-4 px-0 pt-4 border-t flex justify-end gap-2 w-full">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-primary text-primary hover:bg-primary/10 min-w-[100px] thaifont"
            >
              ปิด
            </Button>
            <Button type="submit" className="bg-primary hover:bg-primary-dark text-white min-w-[100px] thaifont">
              บันทึก
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
