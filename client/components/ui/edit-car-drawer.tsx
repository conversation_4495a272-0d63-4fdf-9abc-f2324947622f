"use client"

import React from "react"

import type { ReactNode } from "react"
import { useState, useEffect, useRef, useCallback, useMemo } from "react"
import {
  Drawer,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  Drawer<PERSON>itle,
  DrawerDes<PERSON>,
  DrawerFooter,
} from "@/components/ui/drawer"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { CustomSelect } from "@/components/ui/custom-select"
import {
  Calendar,
  Car,
  DollarSign,
  PenToolIcon as Tool,
  FileText,
  ShoppingCart,
  ChevronDown,
  History,
  Plus,
  Truck,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import type { CarComplete } from "../../../types/models"
import { cn } from "@/lib/utils"
import { RepairHistoryModal } from "./repair-history-modal"
import { AddRepairHistoryModal } from "./add-repair-history-modal"
import { TransferHistoryModal } from "./transfer-history-modal"
import { AddTransferHistoryModal } from "./add-transfer-history-modal"
import { mockAuditData } from "@/data/mock-audit-data"
import { getTransferHistory } from "@/data/mock-transfer-data"
// import type { RepairHistoryEntry, TransferHistoryEntry } from "@/types/audit"
import type { RepairHistoryEntry, TransferHistoryEntry } from "../../../types/audit"
import { CarFilesManager, type CarFile } from "./car-files-manager"
// import { fileService } from "@/api/fileService"
import { Textarea } from "@/components/ui/textarea"
import { ThaiDatePickerWrapper } from "@/components/ui/thai-date-picker-wrapper"

// Mock fileService implementation since we don't have access to the actual implementation
const fileService = {
  uploadFiles: async (carId: string, files: File[]): Promise<CarFile[]> => {
    console.log('Mock uploadFiles called with carId:', carId, 'files:', files);
    return [];
  },
  deleteFile: async (carId: string, fileId: string): Promise<void> => {
    console.log('Mock deleteFile called with carId:', carId, 'fileId:', fileId);
  },
  getFiles: async (carId: string): Promise<CarFile[]> => {
    console.log('Mock getFiles called with carId:', carId);
    return [];
  }
};

// Utility functions for number formatting
const formatNumber = (value: string | number): string => {
  if (value === undefined || value === null || value === '') return '';

  // Convert to number and handle potential NaN
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return '';

  // Format with commas for thousands
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const parseFormattedNumber = (formattedValue: string): number => {
  // Remove commas and convert to number
  const cleanValue = formattedValue.replace(/,/g, '');
  const num = parseFloat(cleanValue);
  return isNaN(num) ? 0 : num;
};

// ControlledInput component
const ControlledInput = React.memo(
  ({
    id,
    label,
    type = "text",
    initialValue = "",
    onChange,
    disabled = false,
    error = "",
    className = "",
    suffix = "",
    formatAsNumber = type === "number", // Default to true for number fields
  }: {
    id: string
    label: string
    type?: string
    initialValue?: string
    onChange: (value: string) => void
    disabled?: boolean
    error?: string
    className?: string
    suffix?: string
    formatAsNumber?: boolean
  }) => {
    // Format the initial value if it's a number field
    const formattedInitialValue = useMemo(() => {
      if (formatAsNumber && initialValue) {
        return formatNumber(initialValue);
      }
      return initialValue;
    }, [formatAsNumber, initialValue]);

    // Use local state to manage the input value
    const [value, setValue] = useState(formattedInitialValue);
    const [isFocused, setIsFocused] = useState(false);

    // Update local state when initialValue changes
    useEffect(() => {
      if (!isFocused) {
        if (formatAsNumber && initialValue) {
          setValue(formatNumber(initialValue));
        } else {
          setValue(initialValue);
        }
      }
    }, [initialValue, formatAsNumber, isFocused]);

    // Handle input changes locally
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;

      if (formatAsNumber) {
        // Remove any existing commas first
        const cleanValue = newValue.replace(/,/g, '');

        // Allow typing numbers, decimal point, and backspace
        if (/^[0-9]*\.?[0-9]*$/.test(cleanValue) || cleanValue === '') {
          // For real-time formatting, we need to:
          // 1. Keep track of cursor position
          const cursorPosition = e.target.selectionStart || 0;
          const previousLength = e.target.value.length;

          // 2. Format the number for display
          if (cleanValue === '') {
            setValue('');
          } else {
            // Only format with commas if there's no decimal point being typed at the end
            if (cleanValue.endsWith('.')) {
              // If user just typed a decimal point, don't format yet
              setValue(cleanValue);
            } else {
              // Split by decimal point to format only the integer part
              const parts = cleanValue.split('.');
              const integerPart = parts[0];
              const decimalPart = parts.length > 1 ? parts[1] : '';

              // Format integer part with commas
              let formattedInteger = '';
              if (integerPart) {
                formattedInteger = Number(integerPart).toLocaleString('en-US');
              }

              // Combine with decimal part if it exists
              const formattedValue = decimalPart
                ? `${formattedInteger}.${decimalPart}`
                : formattedInteger;

              setValue(formattedValue);

              // 3. Adjust cursor position after formatting
              setTimeout(() => {
                if (e.target) {
                  // Calculate new cursor position based on how many commas were added/removed
                  const lengthDifference = formattedValue.length - previousLength;
                  const newPosition = cursorPosition + lengthDifference;
                  e.target.setSelectionRange(newPosition, newPosition);
                }
              }, 0);
            }
          }
        }
      } else {
        setValue(newValue);
      }
      // Only propagate changes to parent on blur to reduce re-renders
    };

    // Handle focus to maintain the current display
    const handleFocus = () => {
      setIsFocused(true);
      // We no longer need to remove commas on focus since we're formatting in real-time
      // and the cursor position is being handled in the handleChange function
    };

    // Propagate changes to parent on blur and ensure consistent formatting
    const handleBlur = () => {
      setIsFocused(false);

      if (formatAsNumber) {
        // Parse the value, send the raw number to parent
        const cleanValue = value.replace(/,/g, '');
        const parsedValue = parseFormattedNumber(cleanValue).toString();
        onChange(parsedValue);

        // Ensure consistent formatting with 2 decimal places
        if (value !== '') {
          setValue(formatNumber(parsedValue));
        }
      } else {
        onChange(value);
      }
    };

    return (
      <div className="space-y-2">
        <Label htmlFor={id} className={error ? "text-red-500" : disabled ? "text-gray-400" : ""}>
          {label} {suffix && <span className="text-gray-500">{suffix}</span>}
        </Label>
        <Input
          id={id}
          type={formatAsNumber ? "text" : type} // Use text type for formatted numbers
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          tabIndex={0}
          className={cn(className, error ? "border-red-500" : "", disabled ? "bg-gray-100" : "")}
        />
        {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
      </div>
    )
  },
)
ControlledInput.displayName = "ControlledInput"

// Add a new ControlledTextarea component for larger text inputs
const ControlledTextarea = React.memo(
  ({
    id,
    label,
    initialValue = "",
    onChange,
    disabled = false,
    error = "",
    className = "",
    rows = 4,
  }: {
    id: string
    label: string
    initialValue?: string
    onChange: (value: string) => void
    disabled?: boolean
    error?: string
    className?: string
    rows?: number
  }) => {
    // Use local state to manage the textarea value
    const [value, setValue] = useState(initialValue)

    // Update local state when initialValue changes
    useEffect(() => {
      setValue(initialValue)
    }, [initialValue])

    // Handle textarea changes locally
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value
      setValue(newValue)
    }

    // Propagate changes to parent on blur
    const handleBlur = () => {
      onChange(value)
    }

    return (
      <div className="space-y-2">
        <Label htmlFor={id} className={error ? "text-red-500" : disabled ? "text-gray-400" : ""}>
          {label}
        </Label>
        <Textarea
          id={id}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          disabled={disabled}
          rows={rows}
          tabIndex={0}
          className={cn(className, error ? "border-red-500" : "", disabled ? "bg-gray-100" : "")}
        />
        {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
      </div>
    )
  },
)
ControlledTextarea.displayName = "ControlledTextarea"

// Create a controlled Thai date picker component that manages its own state
const ControlledThaiDatePicker = React.memo(
  ({
    id,
    label,
    initialValue = "",
    onChange,
    disabled = false,
    error = "",
    className = "",
    required = false,
  }: {
    id: string
    label: string
    initialValue?: string
    onChange: (value: string) => void
    disabled?: boolean
    error?: string
    className?: string
    required?: boolean
  }) => {
    // Use local state to manage the date value
    const [value, setValue] = useState(initialValue)

    // Update local state when initialValue changes
    useEffect(() => {
      setValue(initialValue)
    }, [initialValue])

    // Handle date changes and propagate to parent
    const handleChange = (newValue: string) => {
      setValue(newValue)
      onChange(newValue)
    }

    return (
      <div className="space-y-2">
        <Label htmlFor={id} className={error ? "text-red-500" : disabled ? "text-gray-400" : ""}>
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
        <ThaiDatePickerWrapper
          id={id}
          value={value}
          onChange={handleChange}
          disabled={disabled}
          className={cn(className, error ? "border-red-500" : "", disabled ? "bg-gray-100" : "")}
          required={required}
        />
        {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
      </div>
    )
  },
)
ControlledThaiDatePicker.displayName = "ControlledThaiDatePicker"

// Create a controlled select component that manages its own state
const ControlledSelect = React.memo(
  ({
    id,
    label,
    options,
    initialValue = "",
    onChange,
    disabled = false,
    error = "",
    icon,
    triggerOption,
    triggerLabel,
  }: {
    id: string
    label: string
    options: Array<{ value: string; label: string }>
    initialValue?: string
    onChange: (value: string) => void
    disabled?: boolean
    error?: string
    icon?: ReactNode
    triggerOption?: string
    triggerLabel?: string
  }) => {
    // Use local state to manage the select value
    const [value, setValue] = useState(initialValue)

    // Update local state when initialValue changes
    useEffect(() => {
      setValue(initialValue)
    }, [initialValue])

    // Handle select changes locally and propagate to parent
    const handleChange = (newValue: string) => {
      setValue(newValue)
      onChange(newValue)
    }

    return (
      <div>
        <CustomSelect
          id={id}
          label={label}
          value={value}
          options={options}
          onChange={handleChange}
          disabled={disabled}
          icon={icon}
          triggerOption={triggerOption}
          triggerLabel={triggerLabel}
        />
        {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
      </div>
    )
  },
)
ControlledSelect.displayName = "ControlledSelect"

// Create a section header component for form grouping
const FormSectionHeader = React.memo(({ title }: { title: string }) => {
  return (
    <div className="col-span-full mb-2 mt-4 first:mt-0">
      <h3 className="text-sm font-semibold text-primary-dark border-b pb-1 thaifont">{title}</h3>
    </div>
  )
})
FormSectionHeader.displayName = "FormSectionHeader"

interface EditCarDrawerProps {
  isOpen: boolean
  onClose: () => void
  car?: CarComplete
  onSave: (data: any) => void
}

export function EditCarDrawer({ isOpen, onClose, car, onSave }: EditCarDrawerProps) {
  // State for accordion sections
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    general: true,
    purchase: false,
    repair: false,
    finance: false,
    sellout: false,
  })

  // State for form validation
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  // Main form data state - this will only be updated on blur or select change
  // to prevent re-renders during typing
  const [formData, setFormData] = useState<any>({})

  // Ref to store the current form data for calculations without re-renders
  const formDataRef = useRef<any>({})

  // State for repair history modals
  const [isRepairHistoryModalOpen, setIsRepairHistoryModalOpen] = useState(false)
  const [isAddRepairHistoryModalOpen, setIsAddRepairHistoryModalOpen] = useState(false)
  const [repairHistory, setRepairHistory] = useState<RepairHistoryEntry[]>([])

  // State for transfer history modals
  const [isTransferHistoryModalOpen, setIsTransferHistoryModalOpen] = useState(false)
  const [isAddTransferHistoryModalOpen, setIsAddTransferHistoryModalOpen] = useState(false)
  const [transferHistory, setTransferHistory] = useState<TransferHistoryEntry[]>([])

  // State for car files
  const [carFiles, setCarFiles] = useState<CarFile[]>([])

  // Add this near the top of the component with other state variables
  const [isEditMode, setIsEditMode] = useState(false)

  // Ref to maintain scroll position
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const scrollPositionRef = useRef<{ top: number; left: number }>({ top: 0, left: 0 })

  // Function to save scroll position
  const saveScrollPosition = useCallback(() => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (viewport) {
        scrollPositionRef.current = {
          top: viewport.scrollTop,
          left: viewport.scrollLeft
        };
      }
    }
  }, []);

  // Function to restore scroll position
  const restoreScrollPosition = useCallback(() => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (viewport) {
        setTimeout(() => {
          viewport.scrollTop = scrollPositionRef.current.top;
          viewport.scrollLeft = scrollPositionRef.current.left;
        }, 0);
      }
    }
  }, []);

  // Function to calculate purchase vat percent
  const calculatePurchaseVatPercent = useCallback((data: Record<string, any> = formDataRef.current) => {
    const purchasePrice = Number.parseFloat(data.purchase_price || "0")
    const vatPercent = Number.parseFloat(data.vat_percent || "0")
    // Calculate the full VAT amount without dividing by 100
    const purchaseVatPercent = purchasePrice * vatPercent

    saveScrollPosition();
    setFormData((prev: any) => ({
      ...prev,
      purchase_vat_percent: String(purchaseVatPercent.toFixed(2)),
    }))
    setTimeout(restoreScrollPosition, 0);

    formDataRef.current = {
      ...formDataRef.current,
      purchase_vat_percent: String(purchaseVatPercent.toFixed(2)),
    }
  }, [saveScrollPosition, restoreScrollPosition])

  // Function to calculate vat percent
  const calculateVatPercent = useCallback((data: Record<string, any> = formDataRef.current) => {
    const carType = String(data.is_auction_car || "").toLowerCase()

    let vatPercent = 0
    if (carType === "company car") {
      vatPercent = 0.07  // Changed from 7 to 0.07 for correct calculation
    }

    const vatString = vatPercent.toFixed(2)

    saveScrollPosition();
    setFormData((prev: any) => ({
      ...prev,
      vat_percent: vatString,
    }))
    setTimeout(restoreScrollPosition, 0);

    formDataRef.current = {
      ...formDataRef.current,
      vat_percent: vatString,
    }
  }, [saveScrollPosition, restoreScrollPosition])

  // Function to calculate total investment
  const calculateTotalInvestment = useCallback((data: Record<string, any> = formDataRef.current) => {
    const fields = [
      "transport_2_personal_payment",
      "transport_3_tl_payment",
      "registration_fee",
      "ems_registration",
      "qc3",
      "purchase_price",
      "operation_cost_incl_vat",
      "transport_1_auction_lot",
      "initial_check",
      "tax_insurance_cost_zero",
      "other_costs_seven",
      "qc1_auction_lot",
      "repainting_cost",
      "engine_repair_cost",
      "suspension_repair_cost",
      "autopart_cost",
      "battery_cost",
      "tires_wheels_cost",
      "commission_s",
      "commission_agent",
      "commission_manager",
    ]

    let total = 0

    // Sum all other fields
    fields.forEach((field) => {
      const value = Number.parseFloat(data[field] || "0")
      if (!isNaN(value)) {
        total += value
      }
    })

    // Add purchase_vat_percent
    const purchaseVatPercent = Number.parseFloat(data.purchase_vat_percent || "0")
    total += purchaseVatPercent

    // Add five_three_tax_percentage
    const fiveThreeTaxPercentage = Number.parseFloat(data.five_three_tax_percentage || "0")
    total += fiveThreeTaxPercentage

    // Update the total investment
    saveScrollPosition();
    setFormData((prev: any) => ({
      ...prev,
      total_investment: String(total.toFixed(2)),
    }))
    setTimeout(restoreScrollPosition, 0);

    formDataRef.current = {
      ...formDataRef.current,
      total_investment: String(total.toFixed(2)),
    }

    // Now calculate listed price (always total + 60000)
    const listedPrice = total + 60000

    saveScrollPosition();
    setFormData((prev: any) => ({
      ...prev,
      listed_price: String(listedPrice.toFixed(2)),
    }))
    setTimeout(restoreScrollPosition, 0);

    formDataRef.current = {
      ...formDataRef.current,
      listed_price: String(listedPrice.toFixed(2)),
    }
  }, [saveScrollPosition, restoreScrollPosition])

  // Function to calculate total purchase cost
  const calculateTotalPurchaseCost = useCallback(
    (data: Record<string, any> = formDataRef.current) => {
      // Calculate five_three_tax_percentage
      let fiveThreeTaxPercentage = 0
      if (data.is_auction_car !== "Personal car") {
        const sum =
          Number.parseFloat(data.operation_cost_incl_vat || "0") +
          Number.parseFloat(data.transport_1_auction_lot || "0") +
          Number.parseFloat(data.initial_check || "0") +
          Number.parseFloat(data.other_costs_seven || "0")

        fiveThreeTaxPercentage = sum * (100 / 107) * -0.03
      }

      // Update the values in both state and ref
      saveScrollPosition();
      setFormData((prev: any) => ({
        ...prev,
        five_three_tax_percentage: String(fiveThreeTaxPercentage.toFixed(2)),
      }))
      setTimeout(restoreScrollPosition, 0);

      formDataRef.current = {
        ...formDataRef.current,
        five_three_tax_percentage: String(fiveThreeTaxPercentage.toFixed(2)),
      }

      // Calculate total_purchase_cost
      const totalPurchaseCost =
        Number.parseFloat(data.purchase_price || "0") +
        Number.parseFloat(data.purchase_vat_percent || "0") +
        Number.parseFloat(data.operation_cost_incl_vat || "0") +
        Number.parseFloat(data.transport_1_auction_lot || "0") +
        Number.parseFloat(data.initial_check || "0") +
        Number.parseFloat(data.tax_insurance_cost_zero || "0") +
        Number.parseFloat(data.other_costs_seven || "0") +
        fiveThreeTaxPercentage

      // Update the total in both state and ref
      saveScrollPosition();
      setFormData((prev: any) => ({
        ...prev,
        total_purchase_cost: String(totalPurchaseCost.toFixed(2)),
      }))
      setTimeout(restoreScrollPosition, 0);

      formDataRef.current = {
        ...formDataRef.current,
        total_purchase_cost: String(totalPurchaseCost.toFixed(2)),
      }

      // Since total_purchase_cost has changed, we need to recalculate total_investment
      calculateTotalInvestment({
        ...formDataRef.current,
        total_purchase_cost: String(totalPurchaseCost.toFixed(2)),
      })
    },
    [calculateTotalInvestment, saveScrollPosition, restoreScrollPosition],
  )

  // Function to calculate finance-related values
  const calculateFinanceValues = useCallback((data: Record<string, any> = formDataRef.current) => {
    // 1. Calculate car_vat_amount = car_amount * vat_percent
    const carAmount = Number.parseFloat(data.car_amount || "0")
    const vatPercent = Number.parseFloat(data.vat_percent || "0")
    // Calculate the full VAT amount without dividing by 100
    const carVatAmount = carAmount * vatPercent

    // 2. Calculate input_vat_commission = car_commission_amount * vat_percent
    const carCommissionAmount = Number.parseFloat(data.car_commission_amount || "0")
    // Calculate the full VAT amount without dividing by 100
    const inputVatCommission = carCommissionAmount * vatPercent

    // 3. Calculate withholding_tax based on the complex rules
    let withholdingTax = 0

    if (carCommissionAmount > 0) {
      const bank = data.bank || ""

      if (vatPercent === 0.07) {
        // If VAT is 7%
        if (bank === "AY") {
          withholdingTax = -carCommissionAmount * 0.01 // Note the negative sign as per formula
        } else {
          withholdingTax = -carCommissionAmount * 0.03 // Note the negative sign as per formula
        }
      } else if (vatPercent === 0) {
        // If VAT is 0%
        if (bank === "TBANK" || bank === "TTB") {
          withholdingTax = -carCommissionAmount * 0.05 // Note the negative sign as per formula
        }
        // else withholdingTax remains 0
      }
    }

    // Update the values in both state and ref, but only if they're non-zero
    saveScrollPosition();

    // Create an object to hold the updates
    const updates: Record<string, string> = {};

    // Only include non-zero values
    if (carVatAmount !== 0) {
      updates.car_vat_amount = String(carVatAmount.toFixed(2));
    }

    if (inputVatCommission !== 0) {
      updates.input_vat_commission = String(inputVatCommission.toFixed(2));
    }

    if (withholdingTax !== 0) {
      updates.withholding_tax = String(withholdingTax.toFixed(2));
    }

    // Apply updates to state if there are any
    if (Object.keys(updates).length > 0) {
      setFormData((prev: any) => ({
        ...prev,
        ...updates
      }));

      // Also update the ref
      formDataRef.current = {
        ...formDataRef.current,
        ...updates
      };
    }

    setTimeout(restoreScrollPosition, 0);
  }, [saveScrollPosition, restoreScrollPosition])



  // Handle field change
  const handleFieldChange = useCallback(
    (field: string, value: string) => {
      // Save current scroll position before any state updates
      saveScrollPosition();

      // Set edit mode to true when a field is changed
      setIsEditMode(true)

      // Enhanced logging for finance_request_date
      if (field === "finance_request_date") {
        console.log(`=== FINANCE REQUEST DATE CHANGE ===`);
        console.log(`Setting finance_request_date to: ${value}`);
        console.log(`Previous value in formData: ${formData.finance_request_date || 'not set'}`);
        console.log(`Previous value in formDataRef: ${formDataRef.current.finance_request_date || 'not set'}`);
      }

      // Update the ref immediately to ensure calculations use latest values
      formDataRef.current = {
        ...formDataRef.current,
        [field]: value,
      }

      // Update the state (this will cause a re-render, but not during typing)
      setFormData((prev: any) => ({
        ...prev,
        [field]: value,
      }))

      // Confirm the update for finance_request_date
      if (field === "finance_request_date") {
        console.log(`After update - formDataRef.current.finance_request_date: ${formDataRef.current.finance_request_date || 'not set'}`);
      }

      // Clear error for this field if it exists
      if (formErrors[field]) {
        setFormErrors((prev: any) => {
          const newErrors = { ...prev }
          delete newErrors[field]
          return newErrors
        })
      }

      // Schedule restoration of scroll position after state updates
      setTimeout(restoreScrollPosition, 0);

      // Handle dependent calculations
      if (field === "is_auction_car") {
        calculateVatPercent(formDataRef.current)
        calculatePurchaseVatPercent(formDataRef.current)
        calculateTotalPurchaseCost(formDataRef.current)
        calculateFinanceValues(formDataRef.current)
      }

      if (field === "purchase_price" || field === "vat_percent") {
        calculatePurchaseVatPercent(formDataRef.current)
        calculateTotalPurchaseCost(formDataRef.current)
        calculateTotalInvestment(formDataRef.current)
        calculateFinanceValues(formDataRef.current)
      }

      // List of fields that affect the five_three_tax_percentage
      const fiveThreeTaxFields = [
        "operation_cost_incl_vat",
        "transport_1_auction_lot",
        "initial_check",
        "other_costs_seven",
        "vat_percent",
        "is_auction_car",
      ]

      // List of fields that affect the total purchase cost
      const purchaseFields = [
        "purchase_price",
        "operation_cost_incl_vat",
        "transport_1_auction_lot",
        "initial_check",
        "tax_insurance_cost_zero",
        "other_costs_seven",
        "vat_percent",
        "is_auction_car",
      ]

      // List of fields that affect the total investment
      const investmentFields = [
        "transport_2_personal_payment",
        "transport_3_tl_payment",
        "registration_fee",
        "ems_registration",
        "qc3",
        "purchase_price",
        "vat_percent", // For purchase VAT calculation
        "five_three_tax_percentage", // For 5.3% tax calculation
        "operation_cost_incl_vat",
        "transport_1_auction_lot",
        "initial_check",
        "tax_insurance_cost_zero",
        "other_costs_seven",
        "qc1_auction_lot",
        "repainting_cost",
        "engine_repair_cost",
        "suspension_repair_cost",
        "autopart_cost",
        "battery_cost",
        "tires_wheels_cost",
        "commission_s",
        "commission_agent",
        "commission_manager",
        "total_investment", // Add this to ensure listed_price updates when total_investment changes
        "is_auction_car", // Car type affects VAT calculations
      ]

      // List of fields that affect finance calculations
      const financeFields = ["car_amount", "car_commission_amount", "vat_percent", "bank"]

      // Recalculate totals if this is a field that affects the calculations
      if (fiveThreeTaxFields.includes(field) || purchaseFields.includes(field)) {
        calculateTotalPurchaseCost(formDataRef.current)
      } else if (investmentFields.includes(field)) {
        // Force update the calculated fields when their dependencies change
        calculateTotalInvestment(formDataRef.current)
      } else if (financeFields.includes(field)) {
        // Calculate finance-related values
        calculateFinanceValues(formDataRef.current)
      }
    },
    [
      formErrors,
      calculateVatPercent,
      calculatePurchaseVatPercent,
      calculateTotalPurchaseCost,
      calculateTotalInvestment,
      calculateFinanceValues,
      saveScrollPosition,
      restoreScrollPosition
    ],
  )

  // Load car data when available
  useEffect(() => {
    setIsEditMode(false)
    if (car) {
      const newFormData: Record<string, any> = {}

      // Load StockInfo data
      if (car.stockInfo) {
        Object.keys(car.stockInfo).forEach((key) => {
          const value = car.stockInfo[key as keyof typeof car.stockInfo]
          newFormData[key] = typeof value === "number" ? String(value) : value
        })
      }

      // Load BuyIn data
      if (car.buyin) {
        Object.keys(car.buyin).forEach((key) => {
          const value = car.buyin![key as keyof typeof car.buyin]
          newFormData[key] = typeof value === "number" ? String(value) : value
        })
      }

      // Handle the split of ems_registration_qc3 into separate fields
      if (car.buyin?.ems_registration_qc3 !== undefined && car.buyin.ems_registration_qc3 !== null) {
        // For simplicity, we'll split the value equally between the two fields
        const totalValue = car.buyin.ems_registration_qc3
        newFormData.ems_registration = String(totalValue)
        // newFormData.qc3 = String(totalValue / 2)
      }

      // Load Repair data
      if (car.repair) {
        Object.keys(car.repair).forEach((key) => {
          const value = car.repair![key as keyof typeof car.repair]
          newFormData[key] = typeof value === "number" ? String(value) : value
        })
      }

      // Load Finance data
      if (car.finance) {
        Object.keys(car.finance).forEach((key) => {
          const value = car.finance![key as keyof typeof car.finance]
          // For finance fields, only set non-zero numeric values
          if (typeof value === "number") {
            // Only add non-zero values for finance fields
            if (value !== 0) {
              newFormData[key] = String(value)
            }
          } else {
            // For non-numeric values, add them as is
            if (value !== null && value !== undefined) {
              newFormData[key] = value
            }
          }
        })
      }

      // Load Sellout data
      if (car.sellout) {
        Object.keys(car.sellout).forEach((key) => {
          const value = car.sellout![key as keyof typeof car.sellout]
          // For sellout fields, only set non-zero numeric values
          if (typeof value === "number") {
            // Only add non-zero values for sellout fields
            if (value !== 0) {
              newFormData[key] = String(value)
            }
          } else {
            // For non-numeric values, add them as is
            if (value !== null && value !== undefined) {
              newFormData[key] = value
            }
          }
        })
      }

      // Set default values for fields that might not be present
      if (!newFormData.vat_percent) {
        newFormData.vat_percent = newFormData.is_auction_car === "Personal car" ? "0.00" : "0.07"
      }

      // Normalize type_of_transport to lowercase for consistent handling
      if (newFormData.type_of_transport) {
        const transportType = String(newFormData.type_of_transport).toLowerCase();
        if (transportType === "personal" || transportType === "company") {
          newFormData.type_of_transport = transportType;
        }
      }

      setFormData(newFormData)
      formDataRef.current = newFormData

      calculateVatPercent(newFormData)
      calculatePurchaseVatPercent(newFormData)
      calculateTotalPurchaseCost(newFormData)
      calculateTotalInvestment(newFormData)
      calculateFinanceValues(newFormData)

      // Load repair history for this car
      if (car.stockInfo?.car_id) {
        const history = mockAuditData.getRepairHistory(car.stockInfo.car_id)
        setRepairHistory(history)

        // Calculate repair values from history when the form loads
        if (history.length > 0) {
          updateRepairValuesFromHistory(history)
        }

        // Load transfer history for this car
        const transfers = getTransferHistory(car.stockInfo.car_id)
        setTransferHistory(transfers)

        // Update transfer values if needed
        if (transfers.length > 0) {
          updateTransferValuesFromHistory(transfers)
        }
      }
    }
  }, [
    car,
    calculateVatPercent,
    calculatePurchaseVatPercent,
    calculateTotalPurchaseCost,
    calculateTotalInvestment,
    calculateFinanceValues,
  ])

  // Toggle accordion section
  const toggleSection = useCallback((section: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }, [])

  // Validate form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {}

    // Required fields validation
    if (!formDataRef.current.index_number) {
      newErrors.index_number = "Index number is required"
    }

    if (!formDataRef.current.brand) {
      newErrors.brand = "Brand is required"
    }

    if (!formDataRef.current.model) {
      newErrors.model = "Model is required"
    }

    // Add more validations as needed

    setFormErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [])

  // Handle form submission
  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault()

      if (validateForm()) {

        // Convert string values to numbers for submission
        const processedData = { ...formDataRef.current }
        // Log the payload being sent to the backend

        // List of fields that should be numbers
        const numberFields = [
          "total_investment",
          "listed_price",
          "qc1_auction_lot",
          "transport_3_tl_payment",
          "transport_2_personal_payment",
          "purchase_price",
          "operation_cost_incl_vat",
          "transport_1_auction_lot",
          "initial_check",
          "tax_insurance_cost_zero",
          "other_costs_seven",
          "book_deposit",
          "vat_percent",
          "purchase_vat_percent",
          "five_three_tax_percentage",
          "total_purchase_cost",
          "ems_registration",
          "qc3",
          "registration_fee",
          "repainting_cost",
          "engine_repair_cost",
          "suspension_repair_cost",
          "autopart_cost",
          "battery_cost",
          "tires_wheels_cost",
          "car_amount",
          "car_vat_amount",
          "car_commission_amount",
          "input_vat_commission",
          "withholding_tax",
          "promotion_customer",
          "bonus_insurance_car_life_engine",
          "down_payment",
          "loanprotectioninsurance",
          "accident_insurance",
          "car_insurance",
          "bank_documents",
          "actual_selling_price",
          "commission_s",
          "commission_agent",
          "commission_manager",
        ]

        // Convert string values to numbers only if they exist
        numberFields.forEach((field) => {
          if (processedData[field] !== undefined && processedData[field] !== null && processedData[field] !== "") {
            const value = Number.parseFloat(processedData[field])
            if (!isNaN(value)) {
              processedData[field] = value
            } else {
              // If the field exists but can't be parsed as a number, leave it undefined
              delete processedData[field]
            }
          } else {
            // If the field doesn't exist or is empty, don't include it in the payload
            delete processedData[field]
          }
        })

        // Convert year to number if it exists and is not empty
        if (processedData.year !== undefined && processedData.year !== null && processedData.year !== "") {
          const yearValue = Number.parseInt(processedData.year)
          if (!isNaN(yearValue)) {
            processedData.year = yearValue
          } else {
            // If year exists but can't be parsed as a number, remove it
            delete processedData.year
          }
        }

        // Combine ems_registration and qc3 back into ems_registration_qc3 for backward compatibility
        // Only add this field if either ems_registration or qc3 exists
        // if (processedData.ems_registration !== undefined || processedData.qc3 !== undefined) {
        //   const emsValue = processedData.ems_registration || 0;
        //   const qc3Value = processedData.qc3 || 0;
        //   // Only set the combined field if at least one value is non-zero
        //   if (emsValue !== 0 || qc3Value !== 0) {
        //     processedData.ems_registration_qc3 = emsValue + qc3Value;
        //   }
        // }

        // Log the payload being sent to the backend
        console.log("=== FORM SUBMISSION PAYLOAD ===");
        console.log("Complete payload:", processedData);

        // Define date fields
        const dateFields = [
          "registration_date",
          "registration_book_received_date",
          "purchase_date",
          "transfer_date",
          "repair_date",
          "finance_request_date",
          "finance_received_date",
          "car_tax_invoice_date",
          "commission_tax_invoice_date",
          "sale_date"
        ];

        // Special logging for finance_request_date before processing
        console.log("=== FINANCE REQUEST DATE BEFORE PROCESSING ===");
        console.log(`finance_request_date in formData: ${formData.finance_request_date || 'not set'}`);
        console.log(`finance_request_date in formDataRef: ${formDataRef.current.finance_request_date || 'not set'}`);

        // Ensure date fields are included in the payload and handle Buddhist year conversion
        dateFields.forEach(field => {
          // Always include date fields in the processed data, even if they're empty
          // This ensures they're sent to the backend and can be updated to null/empty
          let dateValue = formDataRef.current[field] || "";

          // Check if this is a date string with a year > 3000 (indicating double Buddhist year conversion)
          if (typeof dateValue === 'string' && dateValue.length >= 4) {
            const yearPart = parseInt(dateValue.substring(0, 4));
            if (yearPart > 3000) {
              // Correct the double Buddhist year conversion by subtracting 543
              const correctedYear = yearPart - 543;
              dateValue = correctedYear + dateValue.substring(4);
            }
          }

          // Ensure the field is included in the processed data
          processedData[field] = dateValue;
        });

        console.log("=== DATE FIELDS ===");
        dateFields.forEach(field => {
          console.log(`${field}: ${processedData[field] || 'not set'} (original: ${formDataRef.current[field] || 'not set'})`);
        });

        // Special logging for finance_request_date to help diagnose issues
        console.log("=== FINANCE REQUEST DATE DETAILS ===");
        console.log(`finance_request_date in formData: ${formData.finance_request_date || 'not set'}`);
        console.log(`finance_request_date in formDataRef: ${formDataRef.current.finance_request_date || 'not set'}`);
        console.log(`finance_request_date in processedData: ${processedData.finance_request_date || 'not set'}`);

        onSave(processedData)
        toast({
          title: "Vehicle Updated",
          description: "The vehicle information has been successfully updated.",
        })
        onClose()
      } else {
        toast({
          title: "Validation Error",
          description: "Please correct the errors in the form.",
          variant: "destructive",
        })

        // Find the first section with errors and open it
        const sectionsWithErrors = {
          general: [
            "index_number",
            "car_status",
            "old_license_plate",
            "new_license_plate",
            "registration_date",
            "registration_book_received_date",
            "listed_price",
            "total_investment",
            "notes",
          ],
          purchase: [
            "brand",
            "model",
            "color",
            "year",
            "tank_number",
            "engine_number",
            "purchase_date",
            "is_auction_car",
            "auction_order",
            "auction_name",
            "auction_provinced",
            "qc1_auction_lot",
            "auction_checker",
            "parking_location",
            "type_of_transport",
            "auction_transporter",
            "transport_3_tl_payment",
            "transport_2_personal_payment",
            "purchase_price",
            "operation_cost_incl_vat",
            "transport_1_auction_lot",
            "initial_check",
            "tax_insurance_cost_zero",
            "other_costs_seven",
            "total_purchase_cost",
            "transfer_date",
            "ems_registration",
            "qc3",
          ],
          repair: [
            "repainting_cost",
            "engine_repair_cost",
            "suspension_repair_cost",
            "autopart_cost",
            "battery_cost",
            "tires_wheels_cost",
            "repair_date",
          ],
          finance: [
            "finance_received_date",
            "car_tax_invoice_date",
            "car_tax_invoice_number",
            "car_amount",
            "car_vat_amount",
            "commission_tax_invoice_date",
            "commission_tax_invoice_number",
            "car_commission_amount",
            "input_vat_commission",
            "withholding_tax",
            "salesperson",
            "bank",
            "marketing_person",
            "promotion_customer",
            "bonus_insurance_car_life_engine",
            "customer_name",
            "customer_address_or_advance_payment",
            "down_payment",
            "loanprotectioninsurance",
            "accident_insurance",
            "car_insurance",
            "bank_documents",
            "finance_request_date",
          ],
          sellout: [
            "sale_date",
            "owner_name",
            "customer_address",
            "actual_selling_price",
            "sales_channel",
            "commission_s",
            "commission_agent",
            "commission_manager",
          ],
        }

        for (const [section, fields] of Object.entries(sectionsWithErrors)) {
          if (fields.some((field) => formErrors[field])) {
            setOpenSections((prev) => ({
              ...prev,
              [section]: true,
            }))
            break
          }
        }
      }
    },
    [validateForm, formErrors, onSave, onClose],
  )

  // Check if a field should be disabled based on conditions
  const isFieldDisabled = useCallback((fieldName: string): boolean => {
    if (fieldName === "transport_3_tl_payment") {
      return String(formDataRef.current.type_of_transport || "").toLowerCase() === "personal"
    }
    if (fieldName === "transport_2_personal_payment") {
      return formDataRef.current.type_of_transport !== "" &&
             String(formDataRef.current.type_of_transport || "").toLowerCase() !== "personal"
    }
    return false
  }, [])

  // Get status badge color
  const getStatusBadgeColor = useCallback((status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100"
      case "sold":
        return "bg-primary-light"
      case "in_repair":
      case "repair":
        return "bg-yellow-100"
      case "finance_request":
        return "bg-blue-100"
      case "finance_done":
        return "bg-indigo-100"
      case "reserved":
        return "bg-purple-100"
      case "transfer":
        return "bg-teal-100"
      case "purchase":
        return "bg-green-100"
      default:
        return "bg-gray-100"
    }
  }, [])

  // Handle adding a new repair history entry
  const handleAddRepairHistory = (entry: RepairHistoryEntry) => {
    // In a real app, you would send this to an API
    // For now, we'll just add it to our local state
    const updatedHistory = [entry, ...repairHistory]
    setRepairHistory(updatedHistory)

    // After adding a new repair history entry, update the main repair values
    updateRepairValuesFromHistory(updatedHistory)
  }

  // Function to update main repair values based on history records
  const updateRepairValuesFromHistory = (history: RepairHistoryEntry[]) => {
    // Define the repair fields we need to sum
    const repairFields = [
      "repainting_cost",
      "engine_repair_cost",
      "suspension_repair_cost",
      "autopart_cost",
      "battery_cost",
      "tires_wheels_cost",
    ]

    // Initialize sums for each field
    const sums: Record<string, number> = {}
    repairFields.forEach((field) => {
      sums[field] = 0
    })

    // Calculate the sum for each field across all history records
    history.forEach((entry) => {
      entry.details.forEach((detail) => {
        const fieldName = detail.column_name
        if (repairFields.includes(fieldName) && detail.new_value !== null) {
          sums[fieldName] += detail.new_value
        }
      })
    })

    // Update the form data with the calculated sums
    const updatedData = { ...formData }
    repairFields.forEach((field) => {
      updatedData[field] = String(sums[field])

      // Also update the formDataRef to ensure calculations use the latest values
      formDataRef.current = {
        ...formDataRef.current,
        [field]: String(sums[field]),
      }
    })

    setFormData(updatedData)

    // We'll only recalculate total investment if we're editing
    // This prevents automatic recalculation when just viewing
    if (isEditMode) {
      calculateTotalInvestment(formDataRef.current)
    }
  }

  // Handle adding a new transfer history entry
  const handleAddTransferHistory = (entry: TransferHistoryEntry) => {
    // In a real app, you would send this to an API
    // For now, we'll just add it to our local state
    const updatedHistory = [entry, ...transferHistory]
    setTransferHistory(updatedHistory)

    // After adding a new transfer history entry, update the main transfer values
    updateTransferValuesFromHistory(updatedHistory)
  }

  // Function to update main transfer values based on history records
  const updateTransferValuesFromHistory = (history: TransferHistoryEntry[]) => {
    if (history.length === 0) return

    // Get the most recent transfer entry
    const latestEntry = history[0]

    // Update the form data with the latest values
    const updatedData = { ...formData }

    latestEntry.details.forEach((detail) => {
      const fieldName = detail.column_name
      if (detail.new_value !== null) {
        updatedData[fieldName] = String(detail.new_value)

        // Also update the formDataRef
        formDataRef.current = {
          ...formDataRef.current,
          [fieldName]: String(detail.new_value),
        }
      }
    })

    setFormData(updatedData)

    // We'll only recalculate total investment if we're editing
    // This prevents automatic recalculation when just viewing
    if (isEditMode) {
      calculateTotalInvestment(formDataRef.current)
    }
  }

  // Get current repair values for the add history modal
  const getCurrentRepairValues = () => {
    const repairFields = [
      "repainting_cost",
      "engine_repair_cost",
      "suspension_repair_cost",
      "autopart_cost",
      "battery_cost",
      "tires_wheels_cost",
    ]

    const values: Record<string, string> = {}
    repairFields.forEach((field) => {
      values[field] = formData[field] || "0"
    })

    return values
  }

  // Get current transfer values for the add transfer history modal
  const getCurrentTransferValues = () => {
    const transferFields = [
      "parking_location",
      "type_of_transport",
      "auction_transporter",
      "transport_personal",
      "transport_company",
      "transport_2_personal_payment",
      "transport_3_tl_payment",
    ]

    const values: Record<string, string> = {}
    transferFields.forEach((field) => {
      values[field] = formData[field] || ""
    })

    return values
  }

  // Status options for the car with Thai translations and colors
  const statusOptions = [
    { value: "purchase", label: "ซื้อเข้า", color: "bg-green-100 text-gray-800" },
    { value: "transfer", label: "ขนย้าย", color: "bg-green-100 text-blue-800" },
    { value: "repair", label: "กำลังซ่อม", color: "bg-yellow-100 text-yellow-800" },
    { value: "available", label: "พร้อมขาย", color: "bg-green-100 text-green-800" },
    { value: "reserved", label: "จอง", color: "bg-purple-100 text-purple-800" },
    { value: "finance_request", label: "ขอไฟแนนซ์", color: "bg-blue-100 text-blue-800" },
    { value: "finance_done", label: "ไฟแนนซ์เสร็จ", color: "bg-indigo-100 text-indigo-800" },
    { value: "sold", label: "ขายแล้ว", color: "bg-primary-light text-white" },
  ]

  // Define options for select components
  const brandsOptions = [
    { value: "Toyota", label: "Toyota" },
    { value: "Honda", label: "Honda" },
    { value: "Nissan", label: "Nissan" },
    { value: "Mazda", label: "Mazda" },
    { value: "Mitsubishi", label: "Mitsubishi" },
    { value: "Isuzu", label: "Isuzu" },
    { value: "Ford", label: "Ford" },
    { value: "Chevrolet", label: "Chevrolet" },
  ]

  const gearsOptions = [
    { value: "Automatic", label: "Automatic" },
    { value: "Manual", label: "Manual" },
  ]

  const locationsOptions = [
    { value: "Lamphun-1" , label: "LP1"},
    { value: "Lamphun-2" , label: "LP2"},
    { value: "Chiang mai" , label: "CNX"},
    { value: "Bangkok" , label: "BKK"}
  ]

  const auctionNamesOptions = [
    { value: "ST.", label: "สท." },
    { value: "SIA", label: "SIA" },
    { value: "MOTTO", label: "MOTTO" },
    { value: "SH", label: "สห" },
    { value: "APPLE", label: "APPLE" },
    { value: "IA", label: "IA" },
    { value: "PIA", label: "PIA" },
    { value: "SCB", label: "SCB" },
    { value: "AAA", label: "AAA" },
    { value: "INTER", label: "สากล" },
    { value: "APP", label: "APP" },
    { value: "AX", label: "AX" }
  ];

  const transportTypesOptions = [
    { value: "personal", label: "พนักงานนำส่ง" },
    { value: "company", label: "บริษัทนำส่ง" },
  ]

  const carTypesOptions = [
    { value: "Personal car", label: "รถบุคคลธรรมดา" },
    { value: "Company car", label: "รถนิติบุคคล" },
  ]

  const auctionCheckersOptions = [
    { value: "P", label: "P" },
    { value: "NOK", label: "NOK" },
    { value: "HOM", label: "HOM" },
    { value: "NAT", label: "NAT" },
    { value: "TOP", label: "TOP" },
    { value: "MAT", label: "MAT" },
    { value: "J", label: "J" },
    { value: "RUNG", label: "RUNG" },
    { value: "S-BO", label: "S-BO" },
    { value: "NON", label: "NON" },
    { value: "S-JACK", label: "S-JACK" },
    { value: "MAY", label: "MAY" },
    { value: "TONG", label: "TONG" }
  ];

  const provincesOptions = [
    { value: "Bangkok", label: "กรุงเทพ" },
    { value: "Chiang Mai", label: "เชียงใหม่" },
    { value: "Phuket", label: "ภูเก็ต" },
    { value: "Chonburi", label: "ชลบุรี" },
    { value: "Khon Kaen", label: "ขอนแก่น" },
    { value: "Songkhla", label: "สงขลา" },
  ]

  const banksOptions = [
    { value: "TTB", label: "TTB" },
    { value: "AY", label: "AY" },
    { value: "TNS", label: "ธนช" },
    { value: "CIMB", label: "CIMB" },
    { value: "KK", label: "KK" },
    { value: "KBANK", label: "KBANK" },
    { value: "ORICO", label: "ORICO" },
    { value: "TISCO", label: "TISCO" }
  ];

  const salesChannelsOptions = [
    { value: "fb", label: "Facebook" },
    { value: "onsite", label: "หน้าร้าน" },
  ]
  const salespersonOptions = [
    { value: "MAY", label: "MAY" },
    { value: "SMIT", label: "SMIT" },
    { value: "EAW", label: "EAW" },
    { value: "RAM", label: "RAM" },
    { value: "KRIT", label: "KRIT" },
    { value: "AUN", label: "AUN" },
    { value: "NOK", label: "NOK" }
  ];

  const marketingPersonOptions = [
    { value: "TTB-CNX", label: "TTB-CNX" },
    { value: "AY-CNX", label: "AY-CNX" },
    { value: "CIMB-CNX", label: "CIMB-CNX" },
    { value: "AY-BK", label: "AY-BK" },
    { value: "KK-BK", label: "KK-BK" },
    { value: "KK-CNX", label: "KK-CNX" },
    { value: "KB-BK", label: "KB-BK" },
    { value: "ORI-CNX", label: "ORI-CNX" },
    { value: "CIMB-BK", label: "CIMB-BK" },
    { value: "KB-CNX", label: "KB-CNX" },
    { value: "TTB-BK", label: "TTB-BK" },
    { value: "TISCO-CNX", label: "TISCO-CNX" }
  ];
  // Generate years for the year selector (current year down to 30 years ago)
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 30 }, (_, i) => ({
    value: (currentYear - i).toString(),
    label: (currentYear - i).toString(),
  }))

  // Add a function to count empty fields in a section
  const countEmptyFields = useCallback((sectionFields: string[]) => {
    let count = 0
    sectionFields.forEach((field) => {
      const value = formDataRef.current[field]
      if (value === undefined || value === null || value === "") {
        count++
      }
    })
    return count
  }, [])

  // Define the fields for each section
  const sectionFields = {
    general: [
      "index_number",
      "old_license_plate",
      "new_license_plate",
      "registration_date",
      "registration_book_received_date",
      "notes",
    ],
    purchase: [
      "purchase_date",
      "brand",
      "model",
      "color",
      "year",
      "tank_number",
      "engine_number",
      "auction_order",
      "auction_name",
      "auction_provinced",
      "auction_checker",
      "parking_location",
      "type_of_transport",
      "auction_transporter",
      "transport_personal",
      "transport_company",
    ],
    repair: ["repair_date"],
    finance: [
      "finance_request_date",
      "finance_received_date",
      "car_tax_invoice_date",
      "car_tax_invoice_number",
      "car_amount",
      "car_vat_amount",
      "commission_tax_invoice_date",
      "commission_tax_invoice_number",
      "car_commission_amount",
      "salesperson",
      "bank",
      "marketing_person",
      "customer_name",
      "customer_address_or_advance_payment",
    ],
    sellout: ["sale_date", "owner_name", "customer_address", "actual_selling_price", "sales_channel"],
  }

  // Add state to track empty fields count
  const [emptyFieldsCounts, setEmptyFieldsCounts] = useState<Record<string, number>>({
    general: 0,
    purchase: 0,
    repair: 0,
    finance: 0,
    sellout: 0,
  })

  // Update the empty fields count when form data changes
  useEffect(() => {
    const counts = {
      general: countEmptyFields(sectionFields.general),
      purchase: countEmptyFields(sectionFields.purchase),
      repair: countEmptyFields(sectionFields.repair),
      finance: countEmptyFields(sectionFields.finance),
      sellout: countEmptyFields(sectionFields.sellout),
    }
    setEmptyFieldsCounts(counts)
  }, [formData, countEmptyFields])

  // Update the AccordionSection component to use Thai labels for section titles
  const AccordionSection = React.memo(
    ({
      id,
      title,
      icon,
      children,
      actions,
    }: {
      id: string
      title: string
      icon: ReactNode
      children: ReactNode
      actions?: ReactNode
    }) => {
      const isOpen = openSections[id]
      const emptyFieldsCount = emptyFieldsCounts[id]

      return (
        <div className="border-b last:border-b-0">
          <div className="flex w-full items-center justify-between py-3 px-1">
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation() // Stop event propagation
                toggleSection(id)
              }}
              className="flex items-center flex-1 text-left font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
              aria-expanded={isOpen}
              aria-controls={`section-${id}-content`}
            >
              <div className="flex items-center gap-2">
                {icon}
                <span className="text-sm font-semibold tracking-wide thaifont">{title}</span>
                {emptyFieldsCount > 0 && (
                  <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200 text-xs">
                    {emptyFieldsCount}
                  </Badge>
                )}
              </div>
            </button>
            <div className="flex items-center gap-2">
              {actions && <div className="mr-2">{actions}</div>}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation() // Stop event propagation
                  toggleSection(id)
                }}
                className="focus:outline-none"
              >
                <ChevronDown
                  className={cn("h-5 w-5 text-primary transition-transform duration-200", isOpen && "rotate-180")}
                />
              </button>
            </div>
          </div>
          <div
            id={`section-${id}-content`}
            role="region"
            aria-labelledby={`section-${id}-header`}
            className={cn(
              "overflow-hidden transition-all duration-300",
              isOpen ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0",
            )}
          >
            {isOpen && children}
          </div>
        </div>
      )
    },
  )
  AccordionSection.displayName = "AccordionSection"

  // Add a handler for saving repair history
  const handleSaveRepairHistory = useCallback(() => {
    // In a real app, you would send the updated history to an API
    toast({
      title: "Repair History Saved",
      description: "The repair history has been successfully saved.",
    })
  }, [])

  // Add a handler for saving transfer history
  const handleSaveTransferHistory = useCallback(() => {
    // In a real app, you would send the updated history to an API
    toast({
      title: "Transfer History Saved",
      description: "The transfer history has been successfully saved.",
    })
  }, [])

  // Handle file upload
  const handleFileUpload = async (files: File[]) => {
    const uploadedFiles = await fileService.uploadFiles(car?.stockInfo.car_id || "", files)
    setCarFiles((prev) => [...uploadedFiles, ...prev])
  }

  // Handle file deletion
  const handleFileDelete = async (fileId: string) => {
    await fileService.deleteFile(car?.stockInfo.car_id || "", fileId)
    setCarFiles((prev) => prev.filter((file) => file.id !== fileId))
  }

  // Load car files when car changes
  useEffect(() => {
    const loadCarFiles = async () => {
      if (car?.stockInfo?.car_id) {
        const files = await fileService.getFiles(car.stockInfo.car_id)
        setCarFiles(files)
      }
    }

    loadCarFiles()
  }, [car])

  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerContent className="h-full max-h-screen overflow-hidden thaifont">
        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <DrawerHeader className="px-6 pt-6 pb-2 flex-shrink-0 border-b">
            <div className="flex items-center justify-between">
              <div>
                <DrawerTitle className="text-xl font-bold text-primary-dark">
                  Edit Vehicle {formData.index_number}
                </DrawerTitle>
                <DrawerDescription>
                  {formData.brand} {formData.model} {formData.year} - {formData.tank_number}
                </DrawerDescription>
              </div>
              <div className="flex items-center gap-3">
                <CarFilesManager
                  carId={car?.stockInfo.car_id || ""}
                  files={carFiles}
                  onUpload={handleFileUpload}
                  onDelete={handleFileDelete}
                />
                <Badge variant="outline" className={`text-xs px-2 py-1 ${getStatusBadgeColor(formData.car_status)}`}>
                  {formData.car_status?.replace(/_/g, " ")}
                </Badge>
              </div>
            </div>
          </DrawerHeader>

          <div className="flex-grow overflow-hidden">
            <ScrollArea ref={scrollAreaRef} className="h-[calc(100vh-180px)]">
              <div className="px-6 py-4">
                {/* Update the form fields in the render function to use Thai labels
               // Replace the General Information section */}
                <AccordionSection id="general" title="ข้อมูลทั่วไป" icon={<Car className="h-5 w-5 text-primary" />}>
                  <div className="py-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <CustomSelect
                        id="car_status"
                        label="สถานะรถ"
                        value={formData.car_status || ""}
                        options={statusOptions.map((option) => ({
                          ...option,
                          className: option.color,
                        }))}
                        onChange={(value: string) => handleFieldChange("car_status", value)}
                      />

                      <ControlledInput
                        id="old_license_plate"
                        label="ทะเบียนเดิม"
                        initialValue={formData.old_license_plate}
                        onChange={(value) => handleFieldChange("old_license_plate", value)}
                        error={formErrors.old_license_plate}
                      />

                      <ControlledInput
                        id="new_license_plate"
                        label="ทะเบียนใหม่"
                        initialValue={formData.new_license_plate}
                        onChange={(value) => handleFieldChange("new_license_plate", value)}
                        error={formErrors.new_license_plate}
                      />

                      <ControlledThaiDatePicker
                        id="registration_date"
                        label="วันจดทะเบียน"
                        initialValue={formData.registration_date}
                        onChange={(value) => handleFieldChange("registration_date", value)}
                        error={formErrors.registration_date}
                      />

                      <ControlledThaiDatePicker
                        id="registration_book_received_date"
                        label="วันที่รับเล่มทะเบียน"
                        initialValue={formData.registration_book_received_date}
                        onChange={(value) => handleFieldChange("registration_book_received_date", value)}
                        error={formErrors.registration_book_received_date}
                      />

                      <ControlledInput
                        id="total_investment"
                        label="ทุนรวมทั้งหมด"
                        type="number"
                        initialValue={formData.total_investment}
                        onChange={(value) => handleFieldChange("total_investment", value)}
                        error={formErrors.total_investment}
                        className="font-medium bg-gray-100"
                        disabled={true}
                        formatAsNumber={true}
                      />

                      <ControlledInput
                        id="listed_price"
                        label="ราคาตั้งขาย"
                        type="number"
                        initialValue={formData.listed_price}
                        onChange={(value) => handleFieldChange("listed_price", value)}
                        error={formErrors.listed_price}
                        className="font-medium bg-gray-100"
                        disabled={true}
                        formatAsNumber={true}
                      />

                      <div className="md:col-span-2">
                        <ControlledTextarea
                          id="notes"
                          label="หมายเหตุ"
                          initialValue={formData.notes}
                          onChange={(value) => handleFieldChange("notes", value)}
                          error={formErrors.notes}
                          rows={6}
                        />
                      </div>
                    </div>
                  </div>
                </AccordionSection>

                {/* Replace the Purchase Information section with grouped fields */}
                <AccordionSection
                  id="purchase"
                  title="ข้อมูลการซื้อ"
                  icon={<ShoppingCart className="h-5 w-5 text-primary" />}
                  actions={
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        className="flex items-center text-xs h-8"
                        onClick={(e) => {
                          e.stopPropagation() // Stop event propagation
                          setIsAddTransferHistoryModalOpen(true)
                        }}
                      >
                        <Plus className="h-3.5 w-3.5 mr-1" />
                        เพิ่มการขนส่ง
                      </Button>
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        className="flex items-center text-xs h-8"
                        onClick={(e) => {
                          e.stopPropagation() // Stop event propagation
                          setIsTransferHistoryModalOpen(true)
                        }}
                      >
                        <Truck className="h-3.5 w-3.5 mr-1" />
                        แสดงประวัติขนส่ง
                        {transferHistory.length > 0 && (
                          <span className="ml-1 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                            {transferHistory.length}
                          </span>
                        )}
                      </Button>
                    </div>
                  }
                >
                  <div className="py-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                      {/* Group 1: Purchase Information */}
                      <FormSectionHeader title="ข้อมูลสำคัญการซื้อเข้า" />

                      <ControlledThaiDatePicker
                        id="purchase_date"
                        label="วันที่ซื้อ"
                        initialValue={formData.purchase_date}
                        onChange={(value) => handleFieldChange("purchase_date", value)}
                        error={formErrors.purchase_date}
                      />

                      <ControlledSelect
                        id="is_auction_car"
                        label="ประเภทรถ"
                        options={carTypesOptions}
                        initialValue={formData.is_auction_car}
                        onChange={(value) => handleFieldChange("is_auction_car", value)}
                        error={formErrors.is_auction_car}
                      />

                      <ControlledInput
                        id="vat_percent"
                        label="VAT(%)"
                        suffix="%"
                        type="text"
                        initialValue={
                          formData.vat_percent ?
                          (Number(formData.vat_percent) * 100).toFixed(2) :
                          (formData.is_auction_car === "Personal car" ? "0.00" : "7.00")
                        }
                        onChange={(value) => {
                          // Convert percentage back to decimal for internal calculations
                          const decimalValue = (Number(value) / 100).toFixed(2);
                          handleFieldChange("vat_percent", decimalValue);
                        }}
                        error={formErrors.vat_percent}
                        className="font-medium"
                      />

                      <ControlledInput
                        id="purchase_price"
                        label="ราคาซื้อเข้า"
                        type="number"
                        initialValue={formData.purchase_price}
                        onChange={(value) => handleFieldChange("purchase_price", value)}
                        error={formErrors.purchase_price}
                        formatAsNumber={true}
                      />

                      <ControlledInput
                        id="total_purchase_cost"
                        label="รวมซื้อเข้า"
                        type="number"
                        initialValue={formData.total_purchase_cost || "0"}
                        onChange={(value) => handleFieldChange("total_purchase_cost", value)}
                        disabled={true}
                        className="bg-gray-100 font-medium text-primary-dark"
                        formatAsNumber={true}
                      />

                      {/* Group 2: Vehicle Information */}
                      <FormSectionHeader title="ข้อมูลสำคัญรถยนต์" />

                      <ControlledSelect
                        id="brand"
                        label="ยี่ห้อ"
                        options={brandsOptions}
                        initialValue={formData.brand}
                        onChange={(value) => handleFieldChange("brand", value)}
                        error={formErrors.brand}
                      />

                      <ControlledInput
                        id="model"
                        label="รุ่นรถ"
                        initialValue={formData.model}
                        onChange={(value) => handleFieldChange("model", value)}
                        error={formErrors.model}
                      />

                      <ControlledInput
                        id="color"
                        label="สี"
                        initialValue={formData.color}
                        onChange={(value) => handleFieldChange("color", value)}
                        error={formErrors.color}
                      />

                      <ControlledSelect
                        id="gear"
                        label="เกียร์"
                        options={gearsOptions}
                        initialValue={formData.gear}
                        onChange={(value) => handleFieldChange("gear", value)}
                        error={formErrors.gear}
                      />

                      <ControlledSelect
                        id="year"
                        label="ปี"
                        options={years}
                        initialValue={formData.year?.toString()}
                        onChange={(value) => handleFieldChange("year", value)}
                        error={formErrors.year}
                        icon={<Calendar className="h-4 w-4 text-gray-500" />}
                      />

                      <ControlledInput
                        id="tank_number"
                        label="เลขตัวถัง"
                        initialValue={formData.tank_number}
                        onChange={(value) => handleFieldChange("tank_number", value)}
                        error={formErrors.tank_number}
                      />

                      <ControlledInput
                        id="engine_number"
                        label="เลขเครื่องยนต์"
                        initialValue={formData.engine_number}
                        onChange={(value) => handleFieldChange("engine_number", value)}
                        error={formErrors.engine_number}
                      />

                      {/* Group 3: Auction Information */}
                      <FormSectionHeader title="ข้อมูลลานประมูล" />

                      <ControlledInput
                        id="auction_order"
                        label="ลำดับลานประมูล"
                        initialValue={formData.auction_order}
                        onChange={(value) => handleFieldChange("auction_order", value)}
                        error={formErrors.auction_order}
                      />

                      <ControlledSelect
                        id="auction_location"
                        label="ชื่อลานประมูล"
                        options={auctionNamesOptions}
                        initialValue={formData.auction_location}
                        onChange={(value) => handleFieldChange("auction_location", value)}
                        error={formErrors.auction_location}
                      />

                      <ControlledSelect
                        id="auction_provinced"
                        label="จังหวัดลานประมูล"
                        options={provincesOptions}
                        initialValue={formData.auction_provinced}
                        onChange={(value) => handleFieldChange("auction_provinced", value)}
                        error={formErrors.auction_provinced}
                      />

                      <ControlledInput
                        id="qc1_auction_lot"
                        label="ค่า QC-1-ลานประมูล"
                        type="number"
                        initialValue={formData.qc1_auction_lot}
                        onChange={(value) => handleFieldChange("qc1_auction_lot", value)}
                        error={formErrors.qc1_auction_lot}
                      />

                      <ControlledSelect
                        id="auction_checker"
                        label="พนักงาน QC1"
                        options={auctionCheckersOptions}
                        initialValue={formData.auction_checker}
                        onChange={(value) => handleFieldChange("auction_checker", value)}
                        error={formErrors.auction_checker}
                      />

                      <ControlledInput
                        id="transport_1_auction_lot"
                        label="TRAN-1-ขนย้าย-ลานประมูล"
                        type="number"
                        initialValue={formData.transport_1_auction_lot}
                        onChange={(value) => handleFieldChange("transport_1_auction_lot", value)}
                        error={formErrors.transport_1_auction_lot}
                      />

                      {/* Group 4: Transfer Information */}
                      <FormSectionHeader title="ข้อมูลการขนย้าย" />

                      {/* <ControlledInput
                        id="transfer_date"
                        label="วันที่ขนส่ง"
                        type="date"
                        initialValue={formData.transfer_date}
                        onChange={(value) => handleFieldChange("transfer_date", value)}
                        error={formErrors.transfer_date}
                      /> */}

                      <ControlledSelect
                        id="parking_location"
                        label="สถานที่จอดรถ"
                        options={locationsOptions}
                        initialValue={formData.parking_location}
                        onChange={(value) => handleFieldChange("parking_location", value)}
                        error={formErrors.parking_location}
                      />

                      <ControlledSelect
                        id="type_of_transport"
                        label="ประเภทการขนส่ง"
                        options={transportTypesOptions}
                        initialValue={formData.type_of_transport}
                        onChange={(value) => {
                          // Ensure the value is always lowercase for consistent handling
                          handleFieldChange("type_of_transport", value.toLowerCase());
                        }}
                        error={formErrors.type_of_transport}
                      />

                      <ControlledInput
                        id="transport_personal"
                        label="รายชื่อ TR-บุคคล"
                        initialValue={formData.transport_personal}
                        onChange={(value) => handleFieldChange("transport_personal", value)}
                        error={formErrors.transport_personal}
                      />

                      <ControlledInput
                        id="transport_company"
                        label="รายชื่อ TR-บจก"
                        initialValue={formData.transport_company}
                        onChange={(value) => handleFieldChange("transport_company", value)}
                        error={formErrors.transport_company}
                      />

                      <ControlledInput
                        id="transport_3_tl_payment"
                        label="TRAN 3-TL"
                        type="number"
                        initialValue={formData.transport_3_tl_payment}
                        onChange={(value) => handleFieldChange("transport_3_tl_payment", value)}
                        disabled={isFieldDisabled("transport_3_tl_payment")}
                        error={formErrors.transport_3_tl_payment}
                      />

                      <ControlledInput
                        id="transport_2_personal_payment"
                        label="TRAN 2 -บุคคล"
                        type="number"
                        initialValue={formData.transport_2_personal_payment}
                        onChange={(value) => handleFieldChange("transport_2_personal_payment", value)}
                        disabled={isFieldDisabled("transport_2_personal_payment")}
                        error={formErrors.transport_2_personal_payment}
                      />

                      {/* Group 5: Important Expenses */}
                      <FormSectionHeader title="ข้อมูลค่าใช้จ่ายสำคัญ" />

                      <ControlledInput
                        id="operation_cost_incl_vat"
                        label="ค่าดำเนินการรวมVAT"
                        type="number"
                        initialValue={formData.operation_cost_incl_vat}
                        onChange={(value) => handleFieldChange("operation_cost_incl_vat", value)}
                        error={formErrors.operation_cost_incl_vat}
                        formatAsNumber={true}
                      />

                      <ControlledInput
                        id="initial_check"
                        label="เช็คต้น"
                        type="number"
                        initialValue={formData.initial_check}
                        onChange={(value) => handleFieldChange("initial_check", value)}
                        error={formErrors.initial_check}
                      />

                      <ControlledInput
                        id="tax_insurance_cost_zero"
                        label="ภาษี-พรบ.+คชจ(0%)"
                        type="number"
                        initialValue={formData.tax_insurance_cost_zero}
                        onChange={(value) => handleFieldChange("tax_insurance_cost_zero", value)}
                        error={formErrors.tax_insurance_cost_zero}
                      />

                      <ControlledInput
                        id="other_costs_seven"
                        label="คชจ.อื่นๆ(7%)"
                        type="number"
                        initialValue={formData.other_costs_seven}
                        onChange={(value) => handleFieldChange("other_costs_seven", value)}
                        error={formErrors.other_costs_seven}
                      />

                      <ControlledInput
                        id="purchase_vat_percent"
                        label="VAT ซื้อ"
                        type="number"
                        initialValue={formData.purchase_vat_percent}
                        onChange={(value) => handleFieldChange("purchase_vat_percent", value)}
                        error={formErrors.purchase_vat_percent}
                        formatAsNumber={true}
                      />

                      <ControlledInput
                        id="five_three_tax_percentage"
                        label="ภงด.53"
                        suffix="%"
                        type="text"
                        initialValue={formData.five_three_tax_percentage}
                        onChange={(value) => handleFieldChange("five_three_tax_percentage", value)}
                        error={formErrors.five_three_tax_percentage}
                      />

                      <ControlledInput
                        id="registration_fee"
                        label="ค่าจ้างงานทะเบียน"
                        type="number"
                        initialValue={formData.registration_fee}
                        onChange={(value) => handleFieldChange("registration_fee", value)}
                        error={formErrors.registration_fee}
                      />

                      <ControlledInput
                        id="ems_registration"
                        label="EMS-ทะเบียน"
                        type="number"
                        initialValue={formData.ems_registration}
                        onChange={(value) => handleFieldChange("ems_registration", value)}
                        error={formErrors.ems_registration}
                      />

                      <ControlledInput
                        id="qc3"
                        label="QC-3"
                        type="number"
                        initialValue={formData.qc3}
                        onChange={(value) => handleFieldChange("qc3", value)}
                        error={formErrors.qc3}
                      />
                    </div>
                  </div>
                </AccordionSection>

                {/* Replace the Repair Information section */}
                <AccordionSection
                  id="repair"
                  title="ข้อมูลการซ่อม"
                  icon={<Tool className="h-5 w-5 text-primary" />}
                  actions={
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        className="flex items-center text-xs h-8"
                        onClick={(e) => {
                          e.stopPropagation() // Stop event propagation
                          setIsAddRepairHistoryModalOpen(true)
                        }}
                      >
                        <Plus className="h-3.5 w-3.5 mr-1" />
                        เพิ่มประวัติ
                      </Button>
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        className="flex items-center text-xs h-8"
                        onClick={(e) => {
                          e.stopPropagation() // Stop event propagation
                          setIsRepairHistoryModalOpen(true)
                        }}
                      >
                        <History className="h-3.5 w-3.5 mr-1" />
                        แสดงประวัติ
                        {repairHistory.length > 0 && (
                          <span className="ml-1 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                            {repairHistory.length}
                          </span>
                        )}
                      </Button>
                    </div>
                  }
                >
                  <div className="py-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      {/* Add new repair_date field */}
                      {/* <ControlledInput
                        id="repair_date"
                        label="วันที่ซ่อม"
                        type="date"
                        initialValue={formData.repair_date}
                        onChange={(value) => handleFieldChange("repair_date", value)}
                        error={formErrors.repair_date}
                      /> */}

                      <ControlledInput
                        id="repainting_cost"
                        label="ทำสี"
                        type="number"
                        initialValue={formData.repainting_cost}
                        onChange={(value) => handleFieldChange("repainting_cost", value)}
                        error={formErrors.repainting_cost}
                      />

                      <ControlledInput
                        id="engine_repair_cost"
                        label="ซ่อมเครื่องยนต์"
                        type="number"
                        initialValue={formData.engine_repair_cost}
                        onChange={(value) => handleFieldChange("engine_repair_cost", value)}
                        error={formErrors.engine_repair_cost}
                      />

                      <ControlledInput
                        id="suspension_repair_cost"
                        label="ซ่อมช่วงล่าง"
                        type="number"
                        initialValue={formData.suspension_repair_cost}
                        onChange={(value) => handleFieldChange("suspension_repair_cost", value)}
                        error={formErrors.suspension_repair_cost}
                      />

                      <ControlledInput
                        id="autopart_cost"
                        label="ประดับยนต์"
                        type="number"
                        initialValue={formData.autopart_cost}
                        onChange={(value) => handleFieldChange("autopart_cost", value)}
                        error={formErrors.autopart_cost}
                      />

                      <ControlledInput
                        id="battery_cost"
                        label="Battery"
                        type="number"
                        initialValue={formData.battery_cost}
                        onChange={(value) => handleFieldChange("battery_cost", value)}
                        error={formErrors.battery_cost}
                      />

                      <ControlledInput
                        id="tires_wheels_cost"
                        label="ยางรถยนต์/แม็ก"
                        type="number"
                        initialValue={formData.tires_wheels_cost}
                        onChange={(value) => handleFieldChange("tires_wheels_cost", value)}
                        error={formErrors.tires_wheels_cost}
                      />
                    </div>
                  </div>
                </AccordionSection>

                {/* Replace the Finance Information section */}
                <AccordionSection
                  id="finance"
                  title="ข้อมูลการเงิน"
                  icon={<DollarSign className="h-5 w-5 text-primary" />}
                >
                  <div className="py-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      {/* Finance Request Section */}
                      <FormSectionHeader title="ยื่นไฟแนนซ์" />

                      <ControlledThaiDatePicker
                        id="finance_request_date"
                        label="วันที่ขอไฟแนนซ์"
                        initialValue={formData.finance_request_date}
                        onChange={(value) => handleFieldChange("finance_request_date", value)}
                        error={formErrors.finance_request_date}
                      />

                      <ControlledSelect
                        id="salesperson"
                        label="Sale"
                        options={salespersonOptions}
                        initialValue={formData.salesperson}
                        onChange={(value) => handleFieldChange("salesperson", value)}
                        error={formErrors.salesperson}
                      />

                      <ControlledSelect
                        id="bank"
                        label="BANK"
                        options={banksOptions}
                        initialValue={formData.bank}
                        onChange={(value) => handleFieldChange("bank", value)}
                        error={formErrors.bank}
                      />

                      <ControlledSelect
                        id="marketing_person"
                        label="MKT."
                        options={marketingPersonOptions}
                        initialValue={formData.marketing_person}
                        onChange={(value) => handleFieldChange("marketing_person", value)}
                        error={formErrors.marketing_person}
                      />

                      <ControlledInput
                        id="customer_name"
                        label="ลูกค้า"
                        initialValue={formData.customer_name}
                        onChange={(value) => handleFieldChange("customer_name", value)}
                        error={formErrors.customer_name}
                      />

                      {/* Finance Done Section */}
                      <FormSectionHeader title="ไฟแนนซ์แล้วเสร็จ" />

                      <ControlledThaiDatePicker
                        id="finance_received_date"
                        label="วันที่รับเงินFinance"
                        initialValue={formData.finance_received_date}
                        onChange={(value) => handleFieldChange("finance_received_date", value)}
                        error={formErrors.finance_received_date}
                      />

                      <ControlledThaiDatePicker
                        id="car_tax_invoice_date"
                        label="วันออกภาษี-ขาย-Car"
                        initialValue={formData.car_tax_invoice_date}
                        onChange={(value) => handleFieldChange("car_tax_invoice_date", value)}
                        error={formErrors.car_tax_invoice_date}
                      />

                      <ControlledInput
                        id="car_tax_invoice_number"
                        label="เล่มที่ /เลขที่ออกภาษี-Car"
                        initialValue={formData.car_tax_invoice_number}
                        onChange={(value) => handleFieldChange("car_tax_invoice_number", value)}
                        error={formErrors.car_tax_invoice_number}
                      />

                      <ControlledInput
                        id="car_amount"
                        label="CAR AMOUNT"
                        type="number"
                        initialValue={formData.car_amount}
                        onChange={(value) => handleFieldChange("car_amount", value)}
                        error={formErrors.car_amount}
                      />

                      <ControlledInput
                        id="car_vat_amount"
                        label="VAT ON CAR AMOUNT"
                        type="number"
                        initialValue={formData.car_vat_amount}
                        onChange={(value) => handleFieldChange("car_vat_amount", value)}
                        error={formErrors.car_vat_amount}
                        disabled={true}
                        className="font-medium bg-gray-100"
                      />

                      <ControlledThaiDatePicker
                        id="commission_tax_invoice_date"
                        label="วันออกภาษี-ขาย-Com"
                        initialValue={formData.commission_tax_invoice_date}
                        onChange={(value) => handleFieldChange("commission_tax_invoice_date", value)}
                        error={formErrors.commission_tax_invoice_date}
                      />

                      <ControlledInput
                        id="commission_tax_invoice_number"
                        label="เล่มที่ /เลขที่ออกภาษี-Comm"
                        initialValue={formData.commission_tax_invoice_number}
                        onChange={(value) => handleFieldChange("commission_tax_invoice_number", value)}
                        error={formErrors.commission_tax_invoice_number}
                      />

                      <ControlledInput
                        id="car_commission_amount"
                        label="COMMISSION OF CAR"
                        type="number"
                        initialValue={formData.car_commission_amount}
                        onChange={(value) => handleFieldChange("car_commission_amount", value)}
                        error={formErrors.car_commission_amount}
                      />

                      <ControlledInput
                        id="input_vat_commission"
                        label="Input-VAT-comm."
                        type="number"
                        initialValue={formData.input_vat_commission}
                        onChange={(value) => handleFieldChange("input_vat_commission", value)}
                        error={formErrors.input_vat_commission}
                        disabled={true}
                        className="font-medium bg-gray-100"
                      />

                      <ControlledInput
                        id="withholding_tax"
                        label="WITHOLDING TAX"
                        type="number"
                        initialValue={formData.withholding_tax}
                        onChange={(value) => handleFieldChange("withholding_tax", value)}
                        error={formErrors.withholding_tax}
                        disabled={true}
                        className="font-medium bg-gray-100"
                      />

                      <ControlledInput
                        id="promotion_customer"
                        label="Promotion-ให้ลค."
                        type="number"
                        initialValue={formData.promotion_customer}
                        onChange={(value) => handleFieldChange("promotion_customer", value)}
                        error={formErrors.promotion_customer}
                      />

                      <ControlledInput
                        id="bonus_insurance_car_life_engine"
                        label="ประกันแถม(รถ+ชีวิต+เครื่อง)"
                        type="number"
                        initialValue={formData.bonus_insurance_car_life_engine}
                        onChange={(value) => handleFieldChange("bonus_insurance_car_life_engine", value)}
                        error={formErrors.bonus_insurance_car_life_engine}
                      />

                      <div className="md:col-span-2">
                        <ControlledInput
                          id="customer_address_or_advance_payment"
                          label="ที่อยู่/ค่างวดล่วงหน้า"
                          initialValue={formData.customer_address_or_advance_payment}
                          onChange={(value) => handleFieldChange("customer_address_or_advance_payment", value)}
                          error={formErrors.customer_address_or_advance_payment}
                        />
                      </div>

                      <ControlledInput
                        id="down_payment"
                        label="เงินดาวน์(บาท)"
                        type="number"
                        initialValue={formData.down_payment}
                        onChange={(value) => handleFieldChange("down_payment", value)}
                        error={formErrors.down_payment}
                      />

                      <ControlledInput
                        id="loanprotectioninsurance"
                        label="ประกันคุ้มครองสินเชื่อ"
                        type="number"
                        initialValue={formData.loanprotectioninsurance}
                        onChange={(value) => handleFieldChange("loanprotectioninsurance", value)}
                        error={formErrors.loanprotectioninsurance}
                      />

                      <ControlledInput
                        id="accident_insurance"
                        label="ประกันอุบัติเหตุ"
                        type="number"
                        initialValue={formData.accident_insurance}
                        onChange={(value) => handleFieldChange("accident_insurance", value)}
                        error={formErrors.accident_insurance}
                      />

                      <ControlledInput
                        id="car_insurance"
                        label="ประกันรถยนต์"
                        type="number"
                        initialValue={formData.car_insurance}
                        onChange={(value) => handleFieldChange("car_insurance", value)}
                        error={formErrors.car_insurance}
                      />

                      <ControlledInput
                        id="bank_documents"
                        label="จัดเอกสาร-Bank"
                        type="number"
                        initialValue={formData.bank_documents}
                        onChange={(value) => handleFieldChange("bank_documents", value)}
                        error={formErrors.bank_documents}
                      />
                    </div>
                  </div>
                </AccordionSection>

                {/* Replace the Sellout Information section */}
                <AccordionSection id="sellout" title="ข้อมูลการขาย" icon={<FileText className="h-5 w-5 text-primary" />}>
                  <div className="py-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <ControlledThaiDatePicker
                        id="sale_date"
                        label="วันที่ขาย"
                        initialValue={formData.sale_date}
                        onChange={(value) => handleFieldChange("sale_date", value)}
                        error={formErrors.sale_date}
                      />

                      <ControlledInput
                        id="owner_name"
                        label="เจ้าของ"
                        initialValue={formData.owner_name}
                        onChange={(value) => handleFieldChange("owner_name", value)}
                        error={formErrors.owner_name}
                      />

                      <div className="md:col-span-2">
                        <ControlledInput
                          id="customer_address"
                          label="ที่อยู่"
                          initialValue={formData.customer_address}
                          onChange={(value) => handleFieldChange("customer_address", value)}
                          error={formErrors.customer_address}
                        />
                      </div>

                      <ControlledInput
                        id="actual_selling_price"
                        label="ราคาขายจริง"
                        type="number"
                        initialValue={formData.actual_selling_price}
                        onChange={(value) => handleFieldChange("actual_selling_price", value)}
                        error={formErrors.actual_selling_price}
                      />

                      <ControlledSelect
                        id="sales_channel"
                        label="ช่องทาง"
                        options={salesChannelsOptions}
                        initialValue={formData.sales_channel}
                        onChange={(value) => handleFieldChange("sales_channel", value)}
                        error={formErrors.sales_channel}
                      />

                      <ControlledInput
                        id="commission_s"
                        label="Comm.-S"
                        type="number"
                        initialValue={formData.commission_s}
                        onChange={(value) => handleFieldChange("commission_s", value)}
                        error={formErrors.commission_s}
                      />

                      <ControlledInput
                        id="commission_agent"
                        label="Comm-นายหน้า"
                        type="number"
                        initialValue={formData.commission_agent}
                        onChange={(value) => handleFieldChange("commission_agent", value)}
                        error={formErrors.commission_agent}
                      />

                      <ControlledInput
                        id="commission_manager"
                        label="Comm-ผจก"
                        type="number"
                        initialValue={formData.commission_manager}
                        onChange={(value) => handleFieldChange("commission_manager", value)}
                        error={formErrors.commission_manager}
                      />
                    </div>
                  </div>
                </AccordionSection>
              </div>
            </ScrollArea>
          </div>

          {/* Update the drawer footer buttons */}
          <DrawerFooter className="px-6 py-4 border-t mt-auto flex-shrink-0">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-primary text-primary hover:bg-primary/10"
            >
              ยกเลิก
            </Button>
            <Button type="submit" className="bg-primary hover:bg-primary-dark text-white">
              บันทึกการเปลี่ยนแปลง
            </Button>
          </DrawerFooter>
        </form>
      </DrawerContent>

      {/* Repair History Modal */}
      <RepairHistoryModal
        isOpen={isRepairHistoryModalOpen}
        onClose={() => setIsRepairHistoryModalOpen(false)}
        onSave={handleSaveRepairHistory}
        history={repairHistory}
      />

      {/* Add Repair History Modal */}
      <AddRepairHistoryModal
        isOpen={isAddRepairHistoryModalOpen}
        onClose={() => setIsAddRepairHistoryModalOpen(false)}
        carId={car?.stockInfo.car_id || ""}
        currentRepairValues={getCurrentRepairValues()}
        onSave={handleAddRepairHistory}
      />

      {/* Transfer History Modal */}
      <TransferHistoryModal
        isOpen={isTransferHistoryModalOpen}
        onClose={() => setIsTransferHistoryModalOpen(false)}
        onSave={handleSaveTransferHistory}
        history={transferHistory}
      />

      {/* Add Transfer History Modal */}
      <AddTransferHistoryModal
        isOpen={isAddTransferHistoryModalOpen}
        onClose={() => setIsAddTransferHistoryModalOpen(false)}
        carId={car?.stockInfo.car_id || ""}
        currentTransferValues={getCurrentTransferValues()}
        onSave={handleAddTransferHistory}
      />
    </Drawer>
  )
}
