"use client"

import type React from "react"

import { useState, useRef } from "react"
import { Popover, <PERSON>over<PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { But<PERSON> } from "@/components/ui/button"
import {
  FileIcon,
  FileTextIcon,
  FileImageIcon,
  FileIcon as FilePdfIcon,
  FileArchiveIcon,
  FileSpreadsheetIcon,
  FileX,
  Upload,
  Eye,
  Trash2,
} from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"

export interface CarFile {
  id: string
  name: string
  size: number
  type: string
  url: string
  uploadDate: string
}

interface CarFilesManagerProps {
  carId: string
  files: CarFile[]
  onUpload: (files: File[]) => Promise<void>
  onDelete: (fileId: string) => Promise<void>
}

export function CarFilesManager({ carId, files, onUpload, onDelete }: CarFilesManagerProps) {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [previewFile, setPreviewFile] = useState<CarFile | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  // Handle file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return

    try {
      setIsUploading(true)
      const filesArray = Array.from(e.target.files)
      await onUpload(filesArray)
      toast({
        title: "Files uploaded",
        description: `Successfully uploaded ${filesArray.length} file(s)`,
      })
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "There was an error uploading your files",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  // Handle file deletion
  const handleDeleteFile = async (fileId: string, e: React.MouseEvent) => {
    e.stopPropagation() // Prevent opening the preview

    try {
      await onDelete(fileId)
      toast({
        title: "File deleted",
        description: "The file has been successfully deleted",
      })
    } catch (error) {
      toast({
        title: "Deletion failed",
        description: "There was an error deleting the file",
        variant: "destructive",
      })
    }
  }

  // Get appropriate icon based on file type
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith("image/")) {
      return <FileImageIcon className="h-4 w-4 text-blue-500" />
    } else if (fileType.includes("pdf")) {
      return <FilePdfIcon className="h-4 w-4 text-red-500" />
    } else if (fileType.includes("spreadsheet") || fileType.includes("excel") || fileType.includes("csv")) {
      return <FileSpreadsheetIcon className="h-4 w-4 text-green-500" />
    } else if (fileType.includes("zip") || fileType.includes("rar") || fileType.includes("tar")) {
      return <FileArchiveIcon className="h-4 w-4 text-yellow-500" />
    } else if (fileType.includes("text") || fileType.includes("doc")) {
      return <FileTextIcon className="h-4 w-4 text-gray-500" />
    } else {
      return <FileIcon className="h-4 w-4 text-gray-500" />
    }
  }

  // Preview file
  const handlePreviewFile = (file: CarFile) => {
    setPreviewFile(file)
  }

  // Close preview
  const handleClosePreview = () => {
    setPreviewFile(null)
  }

  // Render file preview content
  const renderPreviewContent = () => {
    if (!previewFile) return null

    if (previewFile.type.startsWith("image/")) {
      return (
        <div className="flex items-center justify-center h-full">
          <img
            src={previewFile.url || "/placeholder.svg"}
            alt={previewFile.name}
            className="max-w-full max-h-[70vh] object-contain"
          />
        </div>
      )
    } else if (previewFile.type.includes("pdf")) {
      return <iframe src={previewFile.url} className="w-full h-[70vh]" title={previewFile.name} />
    } else {
      return (
        <div className="flex flex-col items-center justify-center h-[50vh]">
          <div className="text-4xl mb-4">{getFileIcon(previewFile.type)}</div>
          <p className="text-lg font-medium">{previewFile.name}</p>
          <p className="text-sm text-gray-500 mt-2">{formatFileSize(previewFile.size)}</p>
        </div>
      )
    }
  }

  return (
    <>
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="h-8 px-3 text-xs font-medium">
            Documents
            {files.length > 0 && (
              <span className="ml-1 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                {files.length}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="end" alignOffset={-5} sideOffset={10}>
          <div className="flex flex-col">
            <div className="p-3 border-b">
              <h3 className="font-medium text-sm">Car Documents</h3>
              <p className="text-xs text-gray-500 mt-1">Manage documents related to this vehicle</p>
            </div>

            <ScrollArea className="h-[250px] p-3">
              {files.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[200px] text-center">
                  <FileX className="h-10 w-10 text-gray-300 mb-2" />
                  <p className="text-sm text-gray-500">No documents uploaded yet</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {files.map((file) => (
                    <div
                      key={file.id}
                      className="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer group"
                      onClick={() => handlePreviewFile(file)}
                    >
                      <div className="mr-2">{getFileIcon(file.type)}</div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => {
                            e.stopPropagation()
                            handlePreviewFile(file)
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => handleDeleteFile(file.id, e)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>

            <div className="p-3 border-t">
              <input type="file" ref={fileInputRef} onChange={handleFileUpload} className="hidden" multiple />
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                {isUploading ? (
                  <span className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Uploading...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Files
                  </span>
                )}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* File Preview Dialog */}
      <Dialog open={previewFile !== null} onOpenChange={handleClosePreview}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              {previewFile && getFileIcon(previewFile.type)}
              <span className="ml-2">{previewFile?.name}</span>
            </DialogTitle>
          </DialogHeader>
          {renderPreviewContent()}
          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={() => window.open(previewFile?.url, "_blank")} disabled={!previewFile}>
              Download
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (previewFile) {
                  handleDeleteFile(previewFile.id, new MouseEvent("click") as any)
                  handleClosePreview()
                }
              }}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Delete File
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
