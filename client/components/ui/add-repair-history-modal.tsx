"use client"

import React, { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import type { RepairHistoryEntry } from "@/types/audit"

interface AddRepairHistoryModalProps {
  isOpen: boolean
  onClose: () => void
  carId: string
  currentRepairValues: Record<string, string>
  onSave: (entry: RepairHistoryEntry) => void
}

export function AddRepairHistoryModal({
  isOpen,
  onClose,
  carId,
  currentRepairValues,
  onSave,
}: AddRepairHistoryModalProps) {
  const [description, setDescription] = useState("")
  const [repairValues, setRepairValues] = useState<Record<string, string>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Initialize repair values when modal opens
  React.useEffect(() => {
    if (isOpen) {
      setRepairValues({ ...currentRepairValues })
      setDescription("")
      setErrors({})
    }
  }, [isOpen, currentRepairValues])

  // Handle input change
  const handleInputChange = (field: string, value: string) => {
    setRepairValues((prev) => ({
      ...prev,
      [field]: value,
    }))

    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Check if at least one repair value has changed
    let hasChanges = false
    Object.keys(repairValues).forEach((key) => {
      const currentValue = Number.parseFloat(currentRepairValues[key] || "0")
      const newValue = Number.parseFloat(repairValues[key] || "0")

      if (!isNaN(newValue) && currentValue !== newValue) {
        hasChanges = true
      }
    })

    if (!hasChanges) {
      newErrors.general = "At least one repair value must be changed"
    }

    if (!description.trim()) {
      newErrors.description = "Description is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (validateForm()) {
      // Create audit details
      const details = Object.keys(repairValues)
        .map((columnName) => {
          const oldValue = Number.parseFloat(currentRepairValues[columnName] || "0")
          const newValue = Number.parseFloat(repairValues[columnName] || "0")

          return {
            column_name: columnName,
            old_value: oldValue,
            new_value: newValue,
          }
        })
        .filter((detail) => detail.old_value !== detail.new_value) // Only include changed values

      // Create new repair history entry
      const newEntry: RepairHistoryEntry = {
        audit_id: Date.now(), // Use timestamp as temporary ID
        car_id: carId,
        timestamp: new Date().toISOString(),
        description,
        details,
      }

      onSave(newEntry)
      toast({
        title: "Repair History Added",
        description: "The repair history has been successfully added.",
      })
      onClose()
    } else {
      toast({
        title: "Validation Error",
        description: "Please correct the errors in the form.",
        variant: "destructive",
      })
    }
  }

  // Format column name for display
  const formatColumnName = (columnName: string) => {
    return columnName
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl p-6">
        <form onSubmit={handleSubmit}>
          <DialogHeader className="mb-6">
            <DialogTitle className="text-xl font-bold text-primary-dark">Add Repair History</DialogTitle>
            <DialogDescription className="mt-2">Record a new repair service for this vehicle</DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <div>
              <Label htmlFor="description" className={errors.description ? "text-red-500" : ""}>
                Description
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter a description for this repair service"
                className={`${errors.description ? "border-red-500" : ""} mt-2`}
              />
              {errors.description && <p className="text-xs text-red-500 mt-1">{errors.description}</p>}
            </div>

            <div>
              <h3 className="text-sm font-medium mb-3">Repair Costs</h3>
              {errors.general && <p className="text-xs text-red-500 mb-3">{errors.general}</p>}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.keys(currentRepairValues).map((field) => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{formatColumnName(field)}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={repairValues[field] || ""}
                      onChange={(e) => handleInputChange(field, e.target.value)}
                      className={errors[field] ? "border-red-500" : ""}
                    />
                    {errors[field] && <p className="text-xs text-red-500 mt-1">{errors[field]}</p>}
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter className="mt-8 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-primary hover:bg-primary-dark text-white">
              Save Repair History
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
