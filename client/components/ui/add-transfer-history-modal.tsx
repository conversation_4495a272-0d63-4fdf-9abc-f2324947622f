"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import type { TransferHistoryEntry } from "@/types/audit"
import { CustomSelect } from "@/components/ui/custom-select"
import { toast } from "@/components/ui/use-toast"

interface AddTransferHistoryModalProps {
  isOpen: boolean
  onClose: () => void
  carId: string
  currentTransferValues: Record<string, string>
  onSave: (entry: TransferHistoryEntry) => void
}

export function AddTransferHistoryModal({
  isOpen,
  onClose,
  carId,
  currentTransferValues,
  onSave,
}: AddTransferHistoryModalProps) {
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [description, setDescription] = useState("")

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData(currentTransferValues)
      setDescription("")
    }
  }, [isOpen, currentTransferValues])

  // Handle form submission
  const handleSubmit = () => {
    // Create a new transfer history entry
    const newEntry: TransferHistoryEntry = {
      transfer_id: Date.now(), // Use timestamp as a temporary ID
      car_id: carId,
      timestamp: new Date().toISOString(),
      description: description || `Transfer on ${new Date().toLocaleDateString()}`,
      details: [],
    }

    // Add details for each field
    Object.entries(formData).forEach(([key, value]) => {
      const oldValue = currentTransferValues[key] || "0"
      const newValue = value || "0"

      // Only add to details if the value has changed
      if (oldValue !== newValue) {
        newEntry.details.push({
          column_name: key,
          old_value: isNaN(Number(oldValue)) ? oldValue : Number(oldValue),
          new_value: isNaN(Number(newValue)) ? newValue : Number(newValue),
        })
      }
    })

    // Only save if there are changes
    if (newEntry.details.length > 0) {
      onSave(newEntry)
      toast({
        title: "Transfer History Added",
        description: "The transfer history has been successfully added.",
      })
      onClose()
    } else {
      toast({
        title: "No Changes Detected",
        description: "Please make changes before saving.",
        variant: "destructive",
      })
    }
  }

  // Handle field change
  const handleFieldChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // Location options
  const locationOptions = [
    { value: "Bangkok", label: "Bangkok" },
    { value: "Chiang Mai", label: "Chiang Mai" },
    { value: "Phuket", label: "Phuket" },
    { value: "Pattaya", label: "Pattaya" },
    { value: "Khon Kaen", label: "Khon Kaen" },
    { value: "Hat Yai", label: "Hat Yai" },
  ]

  // Transport type options
  const transportTypeOptions = [
    { value: "Personal", label: "Personal" },
    { value: "Company", label: "Company" },
    { value: "Third-party", label: "Third-party" },
  ]

  // Transporter options
  const transporterOptions = [
    { value: "Transporter A", label: "Transporter A" },
    { value: "Transporter B", label: "Transporter B" },
    { value: "Transporter C", label: "Transporter C" },
    { value: "Transporter D", label: "Transporter D" },
  ]

  // Check if a field should be disabled based on conditions
  const isFieldDisabled = (fieldName: string): boolean => {
    if (fieldName === "transport_3_tl_payment") {
      return formData.type_of_transport === "Personal"
    }
    if (fieldName === "transport_2_personal_payment") {
      return formData.type_of_transport !== "" && formData.type_of_transport !== "Personal"
    }
    return false
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col p-0">
        <DialogHeader className="px-6 pt-6 pb-2 flex-shrink-0 border-b">
          <DialogTitle className="text-xl font-bold text-primary-dark">Add Transfer History</DialogTitle>
        </DialogHeader>

        <div className="flex-grow overflow-hidden">
          <ScrollArea className="h-full max-h-[calc(80vh-130px)]">
            <div className="px-6 py-4 space-y-6">
              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  placeholder="Enter a description for this transfer"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                {/* Parking Location */}
                <div className="space-y-2">
                  <Label htmlFor="parking_location">Parking Location</Label>
                  <CustomSelect
                    id="parking_location"
                    label=""
                    value={formData.parking_location || ""}
                    options={locationOptions}
                    onChange={(value) => handleFieldChange("parking_location", value)}
                    placeholder="Select parking location"
                  />
                </div>

                {/* Type of Transport */}
                <div className="space-y-2">
                  <Label htmlFor="type_of_transport">Type of Transport</Label>
                  <CustomSelect
                    id="type_of_transport"
                    label=""
                    value={formData.type_of_transport || ""}
                    options={transportTypeOptions}
                    onChange={(value) => handleFieldChange("type_of_transport", value)}
                    placeholder="Select transport type"
                  />
                </div>

                {/* Auction Transporter */}
                <div className="space-y-2">
                  <Label htmlFor="auction_transporter">Auction Transporter</Label>
                  <CustomSelect
                    id="auction_transporter"
                    label=""
                    value={formData.auction_transporter || ""}
                    options={transporterOptions}
                    onChange={(value) => handleFieldChange("auction_transporter", value)}
                    placeholder="Select auction transporter"
                  />
                </div>

                {/* Transport Personal */}
                <div className="space-y-2">
                  <Label htmlFor="transport_personal">Transport Personal</Label>
                  <Input
                    id="transport_personal"
                    value={formData.transport_personal || ""}
                    onChange={(e) => handleFieldChange("transport_personal", e.target.value)}
                  />
                </div>

                {/* Transport Company */}
                <div className="space-y-2">
                  <Label htmlFor="transport_company">Transport Company</Label>
                  <Input
                    id="transport_company"
                    value={formData.transport_company || ""}
                    onChange={(e) => handleFieldChange("transport_company", e.target.value)}
                  />
                </div>

                {/* Transport 2 Personal Payment */}
                <div className="space-y-2">
                  <Label
                    htmlFor="transport_2_personal_payment"
                    className={isFieldDisabled("transport_2_personal_payment") ? "text-gray-400" : ""}
                  >
                    Transport 2 Personal Payment
                  </Label>
                  <Input
                    id="transport_2_personal_payment"
                    type="number"
                    value={formData.transport_2_personal_payment || "0"}
                    onChange={(e) => handleFieldChange("transport_2_personal_payment", e.target.value)}
                    disabled={isFieldDisabled("transport_2_personal_payment")}
                    className={isFieldDisabled("transport_2_personal_payment") ? "bg-gray-100" : ""}
                  />
                </div>

                {/* Transport 3 TL Payment */}
                <div className="space-y-2">
                  <Label
                    htmlFor="transport_3_tl_payment"
                    className={isFieldDisabled("transport_3_tl_payment") ? "text-gray-400" : ""}
                  >
                    Transport 3 TL Payment
                  </Label>
                  <Input
                    id="transport_3_tl_payment"
                    type="number"
                    value={formData.transport_3_tl_payment || "0"}
                    onChange={(e) => handleFieldChange("transport_3_tl_payment", e.target.value)}
                    disabled={isFieldDisabled("transport_3_tl_payment")}
                    className={isFieldDisabled("transport_3_tl_payment") ? "bg-gray-100" : ""}
                  />
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>

        <DialogFooter className="px-6 py-4 border-t bg-gray-50 mt-auto">
          <div className="flex gap-3 ml-auto">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-primary text-primary hover:bg-primary/10"
            >
              Cancel
            </Button>
            <Button onClick={handleSubmit} className="bg-primary hover:bg-primary-dark text-white px-6">
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
