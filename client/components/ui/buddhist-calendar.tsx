"use client"

import type * as React from "react"
import { useState } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker } from "react-day-picker"
import { th } from "date-fns/locale"
import { format, getYear, getMonth, setYear, setMonth } from "date-fns"
import type { Locale } from "date-fns"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export type BuddhistCalendarProps = React.ComponentProps<typeof DayPicker>

function BuddhistCalendar({ className, classNames, showOutsideDays = true, ...props }: BuddhistCalendarProps) {
    const [currentDate, setCurrentDate] = useState(props.selected || new Date())

    // Custom formatter to display Buddhist Era years (BE)
    const formatCaption = (date: Date, options: { locale?: Locale }) => {
        // Get month in Thai
        const month = format(date, "LLLL", { locale: th })

        // Get year and convert to Buddhist Era (BE) by adding 543 years
        const gregorianYear = getYear(date)
        const buddhistYear = gregorianYear + 543

        return (
            <div className="flex items-center justify-center gap-2">
                <Select
                    value={String(getMonth(date))}
                    onValueChange={(month) => {
                        setCurrentDate(setMonth(date, Number(month)))
                    }}
                >
                    <SelectTrigger className="w-[120px] h-7 text-xs thaifont">
                        <SelectValue placeholder={month} />
                    </SelectTrigger>
                    <SelectContent>
                        {Array.from({ length: 12 }, (_, i) => {
                            const monthDate = setMonth(date, i)
                            return (
                                <SelectItem key={i} value={String(i)} className="text-xs thaifont">
                                    {format(monthDate, "MMMM", { locale: th })}
                                </SelectItem>
                            )
                        })}
                    </SelectContent>
                </Select>

                <Select
                    value={String(getYear(date))}
                    onValueChange={(year) => {
                        setCurrentDate(setYear(date, Number(year)))
                    }}
                >
                    <SelectTrigger className="w-[80px] h-7 text-xs thaifont">
                        <SelectValue placeholder={String(buddhistYear)} />
                    </SelectTrigger>
                    <SelectContent>
                        {Array.from({ length: 20 }, (_, i) => {
                            const yearValue = getYear(date) + 543 - i
                            return (
                                <SelectItem key={yearValue} value={String(getYear(date) - i)} className="text-xs thaifont">
                                    {yearValue}
                                </SelectItem>
                            )
                        })}
                    </SelectContent>
                </Select>
            </div>
        )
    }

    return (
        <DayPicker
            showOutsideDays={showOutsideDays}
            className={cn("p-3", className)}
            locale={th}
            formatters={{ formatCaption }}
            month={currentDate}
            onMonthChange={setCurrentDate}
            classNames={{
                months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                month: "space-y-4",
                caption: "flex justify-center pt-1 relative items-center px-10", // Added px-10 for more space
                caption_label: "text-sm font-medium text-center flex-1",
                nav: "space-x-1 flex items-center",
                nav_button: cn(
                    buttonVariants({ variant: "outline" }),
                    "h-8 w-8 bg-transparent p-0 opacity-50 hover:opacity-100", // Increased size from h-7 w-7 to h-8 w-8
                ),
                nav_button_previous: "absolute left-1",
                nav_button_next: "absolute right-1",
                table: "w-full border-collapse space-y-1",
                head_row: "flex",
                head_cell: "text-muted-foreground rounded-md w-10 font-normal text-[0.8rem] py-1.5", // Increased width from w-9 to w-10 and added py-1.5
                row: "flex w-full mt-2",
                cell: "h-10 w-10 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20", // Increased from h-9 w-9 to h-10 w-10
                day: cn(buttonVariants({ variant: "ghost" }), "h-10 w-10 p-0 font-normal aria-selected:opacity-100"), // Increased from h-9 w-9 to h-10 w-10
                day_range_end: "day-range-end",
                day_selected:
                    "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                day_today: "bg-accent text-accent-foreground",
                day_outside:
                    "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
                day_disabled: "text-muted-foreground opacity-50",
                day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                day_hidden: "invisible",
                ...classNames,
            }}
            components={{
                IconLeft: ({ ...props }) => <ChevronLeft className="h-5 w-5" />, // Increased from h-4 w-4 to h-5 w-5
                IconRight: ({ ...props }) => <ChevronRight className="h-5 w-5" />, // Increased from h-4 w-4 to h-5 w-5
            }}
            {...props}
        />
    )
}
BuddhistCalendar.displayName = "BuddhistCalendar"

export { BuddhistCalendar }
