"use client"

import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { Car, FileText, Home, LogOut, Users } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
// import { useAuth } from "@/lib/auth-s/ervice"

export function Sidebar() {
  const pathname = usePathname()
  // const { logout } = useAuth()
  const router = useRouter()

  const navigation = [
    { name: "Home", href: "/", icon: Home },
    { name: "Items", href: "/items", icon: Car },
    { name: "Papers", href: "/papers", icon: FileText },
    { name: "Owners", href: "/owners", icon: Users },
  ]
  const handleLogout = () => {
    localStorage.removeItem("auth_token")
    localStorage.removeItem("user")
    localStorage.removeItem("isLoggedIn")
    router.push("/login")
  }

  return (
    <div className="flex h-screen w-64 flex-col border-r bg-primary-dark text-white overflow-hidden">
      <div className="flex h-12 items-center border-b border-primary px-6">
        <Link href="/" className="flex items-center gap-2 font-semibold text-white">
          <Car className="h-5 w-5" />
          <span className="text-base">SP Autocar</span>
        </Link>
      </div>
      <div className="flex-1 py-4 overflow-y-auto no-scrollbar">
        <nav className="grid items-start px-4 text-base font-medium gap-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center gap-3 rounded-md px-3 py-2 transition-all text-md",
                  isActive ? "bg-primary text-white" : "text-gray-200 hover:bg-primary/80 hover:text-white",
                )}
              >
                <item.icon className="h-4 w-4" />
                {item.name}
              </Link>
            )
          })}
        </nav>
      </div>
      <div className="mt-auto p-4 border-t border-primary">
        <Button
          variant="ghost"
          className="w-full justify-start text-md py-2 text-gray-200 hover:bg-transparent hover:text-red-500 transition-colors"
          onClick={handleLogout}
        >
          <LogOut className="mr-3 h-4 w-4" />
          Log out
        </Button>
      </div>
    </div>
  )
}
