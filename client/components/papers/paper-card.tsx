import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import type { ReactNode } from "react"

interface PaperCardProps {
  title: string
  description: string
  icon: ReactNode
  buttonText: string
  href: string
  iconBgColor?: string
  buttonColor?: string
}

export function PaperCard({
  title,
  description,
  icon,
  buttonText,
  href,
  iconBgColor = "bg-primary/10",
  buttonColor = "bg-primary-light hover:bg-primary text-white",
}: PaperCardProps) {
  return (
    <Card className="flex flex-col h-full transition-all hover:shadow-md">
      <CardHeader>
        <CardTitle className="text-xl">{title}</CardTitle>
        <p className="text-sm text-muted-foreground">{description}</p>
      </CardHeader>
      <CardContent className="flex-grow flex items-center justify-center py-6">
        <div className={cn("rounded-full p-6 inline-flex", iconBgColor)}>{icon}</div>
      </CardContent>
      <CardFooter>
        <Button asChild className={cn("w-full", buttonColor)}>
          <Link href={href}>{buttonText}</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
