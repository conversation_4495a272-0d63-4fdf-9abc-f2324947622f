"use client"

import React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"
import { ChevronRight } from "lucide-react"
import { REPORTS_API } from "@/app/services/apiServices"
import { AuctionItem, AuctionLocation } from "@/types/reports"

interface DailyAuctionSummaryProps {
  selectedDate: string
}

export function DailyAuctionSummary({ selectedDate }: DailyAuctionSummaryProps) {
  const [auctionData, setAuctionData] = useState<AuctionLocation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeLocation, setActiveLocation] = useState<string | null>(null)
  const [activeProvince, setActiveProvince] = useState<string | null>(null)

  // Add this instead to track expanded state for each location:
  const [expandedLocations, setExpandedLocations] = useState<Record<string, boolean>>({})

  // Format date for display (convert from YYYY-MM-DD to DD-MM-YYYY)
  const formatDisplayDate = (dateString: string) => {
    const [year, month, day] = dateString.split("-")
    // Display Buddhist Era year directly (no conversion needed)
    const beYear = Number.parseInt(year)
    return `${day}-${month}-${beYear}`
  }

  // Fetch auction data for the selected date
  useEffect(() => {
    setIsLoading(true)

    const fetchAuctionData = async () => {
      try {
        // Get auth token from localStorage
        const authToken = localStorage.getItem("token")
        if (!authToken) {
          console.error("No auth token found")
          setIsLoading(false)
          return
        }

        // Fetch data from API
        const response = await REPORTS_API.GET_DAILY_AUCTION(selectedDate, authToken)

        // Set the auction data
        setAuctionData(response.locations)

        // Set the first location and province as active if there is data
        if (response.locations.length > 0) {
          setActiveLocation(response.locations[0].location)
          if (response.locations[0].provinces.length > 0) {
            setActiveProvince(response.locations[0].provinces[0].province)
          }
        }
      } catch (error) {
        console.error("Error fetching auction data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAuctionData()
  }, [selectedDate])

  // Calculate financial values
  const calculateFinancials = (priceBeforeVat: number) => {
    const vat = priceBeforeVat * 0.07
    const total = priceBeforeVat + vat
    const taxDeduction = priceBeforeVat * 0.03

    return {
      priceBeforeVat,
      vat,
      total,
      taxDeduction,
    }
  }

  // Calculate totals for a group of items
  const calculateGroupTotals = (items: AuctionItem[]) => {
    let totalPriceBeforeVat = 0
    let totalVat = 0
    let totalAmount = 0
    let totalTaxDeduction = 0

    items.forEach((item) => {
      // Calculate for the vehicle
      const vehicleFinancials = calculateFinancials(item.purchasePrice)
      totalPriceBeforeVat += vehicleFinancials.priceBeforeVat
      totalVat += vehicleFinancials.vat
      totalAmount += vehicleFinancials.total
      totalTaxDeduction += vehicleFinancials.taxDeduction

      // Calculate for operation cost
      const operationFinancials = calculateFinancials(item.operationCost)
      totalPriceBeforeVat += operationFinancials.priceBeforeVat
      totalVat += operationFinancials.vat
      totalAmount += operationFinancials.total
      totalTaxDeduction += operationFinancials.taxDeduction

      // Calculate for transport cost
      const transportFinancials = calculateFinancials(item.transportCost)
      totalPriceBeforeVat += transportFinancials.priceBeforeVat
      totalVat += transportFinancials.vat
      totalAmount += transportFinancials.total
      totalTaxDeduction += transportFinancials.taxDeduction

      // Calculate for other costs
      const otherFinancials = calculateFinancials(item.otherCosts)
      totalPriceBeforeVat += otherFinancials.priceBeforeVat
      totalVat += otherFinancials.vat
      totalAmount += otherFinancials.total
      totalTaxDeduction += otherFinancials.taxDeduction
    })

    return {
      totalPriceBeforeVat,
      totalVat,
      totalAmount,
      totalTaxDeduction,
    }
  }

  // Calculate totals for a specific location
  const calculateLocationTotals = (location: AuctionLocation) => {
    let locationTotalPriceBeforeVat = 0
    let locationTotalVat = 0
    let locationTotalAmount = 0
    let locationTotalTaxDeduction = 0

    location.provinces.forEach((province) => {
      const totals = calculateGroupTotals(province.items)
      locationTotalPriceBeforeVat += totals.totalPriceBeforeVat
      locationTotalVat += totals.totalVat
      locationTotalAmount += totals.totalAmount
      locationTotalTaxDeduction += totals.totalTaxDeduction
    })

    return {
      locationTotalPriceBeforeVat,
      locationTotalVat,
      locationTotalAmount,
      locationTotalTaxDeduction,
    }
  }

  // Update the handleLocationClick function to automatically expand the clicked location
  const handleLocationClick = (location: string) => {
    setActiveLocation(location)

    // Find the location data
    const locationData = auctionData.find((loc) => loc.location === location)

    // Set the first province as active if there are provinces
    if (locationData && locationData.provinces.length > 0) {
      setActiveProvince(locationData.provinces[0].province)

      // Automatically expand this location
      setExpandedLocations((prev) => ({
        ...prev,
        [location]: true,
      }))
    } else {
      setActiveProvince(null)
    }
  }

  // Add this function instead:
  const toggleLocation = (location: string, e: React.MouseEvent) => {
    e.stopPropagation() // Prevent triggering the location selection
    setExpandedLocations((prev) => ({
      ...prev,
      [location]: !prev[location],
    }))
  }

  // Function to get the active province data
  const getActiveProvinceData = () => {
    if (!activeLocation || !activeProvince) return null

    const locationData = auctionData.find((loc) => loc.location === activeLocation)
    if (!locationData) return null

    return locationData.provinces.find((province) => province.province === activeProvince) || null
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-pulse text-muted-foreground">Loading auction data...</div>
      </div>
    )
  }

  if (auctionData.length === 0) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="text-muted-foreground">No auction data available for the selected date.</div>
      </div>
    )
  }

  const activeProvinceData = getActiveProvinceData()

  return (
    <div className="space-y-6">
      {/* Header information */}
      <div className="grid grid-cols-2 gap-8">
        <div>
          <div className="flex">
            <div className="w-32 font-medium">Auction Date</div>
            <div>: {formatDisplayDate(selectedDate)}</div>
          </div>
        </div>
      </div>

      {/* Main content with vertical tabs */}
      <div className="flex gap-6">
        {/* Vertical tabs for locations and provinces */}
        <div className="w-40 flex-shrink-0 border rounded-md overflow-hidden">
          {/* Static header */}
          <div className="p-1.5 text-xs border-b">Auction Locations</div>

          <div className="overflow-y-auto max-h-[600px]">
            {auctionData.map((location) => (
              <div key={location.location} className="border-b last:border-b-0">
                {/* Location header with toggle */}
                <button
                  className={`w-full text-left px-2 py-1.5 flex items-center justify-between text-xs
            ${activeLocation === location.location ? "bg-gray-100" : ""}
            hover:bg-gray-50 transition-colors`}
                  onClick={() => handleLocationClick(location.location)}
                >
                  <span>{location.location}</span>
                  <ChevronRight
                    className={`h-3 w-3 text-gray-400 transition-transform ${
                      expandedLocations[location.location] ? "rotate-90" : ""
                    }`}
                    onClick={(e) => toggleLocation(location.location, e)}
                  />
                </button>

                {/* Province list under location */}
                {activeLocation === location.location && expandedLocations[location.location] && (
                  <div>
                    {location.provinces.map((province) => (
                      <button
                        key={province.province}
                        className={`w-full text-left pl-4 pr-2 py-1 text-xs
                  ${activeProvince === province.province ? "bg-gray-100" : ""}
                  hover:bg-gray-50 transition-colors`}
                        onClick={() => setActiveProvince(province.province)}
                      >
                        {province.province}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content area */}
        <div className="flex-1">
          {activeProvinceData ? (
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="bg-primary-dark text-white p-2 flex justify-between items-center">
                  <h4 className="font-medium">
                    {activeLocation} - {activeProvince}
                  </h4>
                  <span className="text-sm">
                    {activeProvinceData.items.length} {activeProvinceData.items.length === 1 ? "vehicle" : "vehicles"}
                  </span>
                </div>

                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border px-2 py-1 text-left text-xs w-[15%]">Auction Order/Index</th>
                      <th className="border px-2 py-1 text-left text-xs w-[35%]">Vehicle Details</th>
                      <th className="border px-2 py-1 text-center text-xs w-[5%]">Qty</th>
                      <th className="border px-2 py-1 text-right text-xs w-[10%]">Price before VAT</th>
                      <th className="border px-2 py-1 text-right text-xs w-[10%]">VAT</th>
                      <th className="border px-2 py-1 text-right text-xs w-[10%]">Total</th>
                      <th className="border px-2 py-1 text-right text-xs w-[10%]">Tax Deduction</th>
                    </tr>
                  </thead>
                  <tbody>
                    {activeProvinceData.items.map((item, itemIndex) => {
                      const vehicleFinancials = calculateFinancials(item.purchasePrice)
                      const operationFinancials = calculateFinancials(item.operationCost)
                      const transportFinancials = calculateFinancials(item.transportCost)
                      const otherFinancials = calculateFinancials(item.otherCosts)

                      return (
                        <React.Fragment key={item.id}>
                          {/* Vehicle row */}
                          <tr>
                            <td className="border px-2 py-1 text-xs align-top" rowSpan={3}>
                              {item.auctionOrder}
                              <br />
                              {itemIndex + 1}/{activeProvinceData.items.length}
                            </td>
                            <td className="border px-2 py-1 text-xs">
                              <div>
                                License: {item.indexNumber} Brand: {item.brand} Model: {item.model} Color: {item.color}{" "}
                                Year: {item.year}
                              </div>
                              <div>
                                Tank #: {item.tankNumber} Engine #: {item.engineNumber}
                              </div>
                            </td>
                            <td className="border px-2 py-1 text-xs text-center">1</td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(vehicleFinancials.priceBeforeVat)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(vehicleFinancials.vat)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(vehicleFinancials.total)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(vehicleFinancials.taxDeduction)}
                            </td>
                          </tr>

                          {/* Operation cost row */}
                          <tr>
                            <td className="border px-2 py-1 text-xs">Operation Cost</td>
                            <td className="border px-2 py-1 text-xs text-center">1</td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(operationFinancials.priceBeforeVat)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(operationFinancials.vat)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(operationFinancials.total)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(operationFinancials.taxDeduction)}
                            </td>
                          </tr>

                          {/* Transport cost row */}
                          <tr>
                            <td className="border px-2 py-1 text-xs">Transport Cost</td>
                            <td className="border px-2 py-1 text-xs text-center">1</td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(transportFinancials.priceBeforeVat)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(transportFinancials.vat)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(transportFinancials.total)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(transportFinancials.taxDeduction)}
                            </td>
                          </tr>

                          {/* Other costs row */}
                          <tr>
                            <td className="border px-2 py-1 text-xs"></td>
                            <td className="border px-2 py-1 text-xs">Other Services</td>
                            <td className="border px-2 py-1 text-xs text-center">1</td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(otherFinancials.priceBeforeVat)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(otherFinancials.vat)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(otherFinancials.total)}
                            </td>
                            <td className="border px-2 py-1 text-xs text-right">
                              {formatCurrency(otherFinancials.taxDeduction)}
                            </td>
                          </tr>

                          {/* Add a separator between items */}
                          {itemIndex < activeProvinceData.items.length - 1 && (
                            <tr className="border-t border-gray-300">
                              <td colSpan={7} className="p-0"></td>
                            </tr>
                          )}
                        </React.Fragment>
                      )
                    })}

                    {/* Province subtotal row */}
                    <tr className="bg-gray-100 font-medium">
                      <td colSpan={3} className="border px-2 py-1 text-xs text-right">
                        Total for {activeProvince}
                      </td>
                      <td className="border px-2 py-1 text-xs text-right">
                        {formatCurrency(calculateGroupTotals(activeProvinceData.items).totalPriceBeforeVat)}
                      </td>
                      <td className="border px-2 py-1 text-xs text-right">
                        {formatCurrency(calculateGroupTotals(activeProvinceData.items).totalVat)}
                      </td>
                      <td className="border px-2 py-1 text-xs text-right">
                        {formatCurrency(calculateGroupTotals(activeProvinceData.items).totalAmount)}
                      </td>
                      <td className="border px-2 py-1 text-xs text-right">
                        {formatCurrency(calculateGroupTotals(activeProvinceData.items).totalTaxDeduction)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </CardContent>
            </Card>
          ) : (
            <div className="flex items-center justify-center h-[400px] border rounded-md">
              <div className="text-muted-foreground">Select a province to view details</div>
            </div>
          )}

          {/* Selected location grand total */}
          <div className="mt-4">
            {activeLocation && (
              <div className="bg-gray-800 text-white p-2 rounded-md">
                <div className="grid grid-cols-5 gap-4 text-right">
                  <div className="col-span-1 text-left text-sm font-bold">
                    {activeLocation} Grand Total
                  </div>
                  {(() => {
                    // Find the selected location and calculate its totals
                    const selectedLocation = auctionData.find(loc => loc.location === activeLocation);
                    if (!selectedLocation) return null;

                    const locationTotals = calculateLocationTotals(selectedLocation);

                    return (
                      <>
                        <div>
                          <div className="text-[10px] mb-1 text-gray-200">Price Before VAT</div>
                          <div className="text-sm font-bold">{formatCurrency(locationTotals.locationTotalPriceBeforeVat)}</div>
                        </div>
                        <div>
                          <div className="text-[10px] mb-1 text-gray-200">VAT</div>
                          <div className="text-sm font-bold">{formatCurrency(locationTotals.locationTotalVat)}</div>
                        </div>
                        <div>
                          <div className="text-[10px] mb-1 text-gray-200">Total</div>
                          <div className="text-sm font-bold">{formatCurrency(locationTotals.locationTotalAmount)}</div>
                        </div>
                        <div>
                          <div className="text-[10px] mb-1 text-gray-200">Tax Deduction</div>
                          <div className="text-sm font-bold">{formatCurrency(locationTotals.locationTotalTaxDeduction)}</div>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
