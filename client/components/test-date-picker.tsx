"use client"

import { useState } from "react"
import { ThaiDatePickerWrapper } from "@/components/ui/thai-date-picker-wrapper"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function TestDatePicker() {
  const [date, setDate] = useState<string>("")

  const handleDateChange = (newDate: string) => {
    console.log("Date changed:", newDate)
    setDate(newDate)
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center">Thai Date Picker Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <ThaiDatePickerWrapper
          value={date}
          onChange={handleDateChange}
          label="Select a date"
          placeholder="dd/mm/yyyy"
          required
        />

        <div className="pt-4">
          <p className="text-sm font-medium">Selected date:</p>
          <p className="text-sm">{date || "No date selected"}</p>
        </div>

        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={() => setDate("")}
            className="mr-2"
          >
            Clear
          </Button>
          <Button
            onClick={() => setDate("2567-05-15")} // Example Buddhist date (2024-05-15)
          >
            Set to 15/05/2567
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
