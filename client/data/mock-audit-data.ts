import type { <PERSON>tHeader, AuditDetail, RepairHistoryEntry } from "@/types/audit"

// Generate random date within the last year
const randomDate = () => {
  const now = new Date()
  const pastDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
  const randomTime = pastDate.getTime() + Math.random() * (now.getTime() - pastDate.getTime())
  return new Date(randomTime).toISOString()
}

// Generate mock audit headers
export const generateMockAuditHeaders = (carIds: string[]): AuditHeader[] => {
  const headers: AuditHeader[] = []
  let auditId = 1

  carIds.forEach((carId) => {
    // Generate 0-5 audit entries per car
    const entryCount = Math.floor(Math.random() * 6)

    for (let i = 0; i < entryCount; i++) {
      headers.push({
        audit_id: auditId++,
        car_id: carId,
        timestamp: randomDate(),
        description: `Repair service #${i + 1} for ${carId}`,
      })
    }
  })

  return headers.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
}

// Generate mock audit details
export const generateMockAuditDetails = (headers: AuditHeader[]): AuditDetail[] => {
  const details: AuditDetail[] = []
  let detailId = 1

  const repairColumns = [
    "repainting_cost",
    "engine_repair_cost",
    "suspension_repair_cost",
    "autopart_cost",
    "battery_cost",
    "tires_wheels_cost",
  ]

  headers.forEach((header) => {
    // Generate 1-4 detail entries per audit header
    const detailCount = 1 + Math.floor(Math.random() * 4)

    // Randomly select columns to update without duplicates
    const selectedColumns = [...repairColumns].sort(() => 0.5 - Math.random()).slice(0, detailCount)

    selectedColumns.forEach((column) => {
      const oldValue = Math.floor(Math.random() * 10000)
      const newValue = oldValue + Math.floor(Math.random() * 5000)

      details.push({
        audit_detail_id: detailId++,
        audit_id: header.audit_id,
        column_name: column,
        old_value: oldValue,
        new_value: newValue,
      })
    })
  })

  return details
}

// Combine headers and details into a single repair history structure
export const getRepairHistory = (
  carId: string,
  headers: AuditHeader[],
  details: AuditDetail[],
): RepairHistoryEntry[] => {
  const carHeaders = headers.filter((h) => h.car_id === carId)

  return carHeaders.map((header) => {
    const headerDetails = details.filter((d) => d.audit_id === header.audit_id)

    return {
      audit_id: header.audit_id,
      car_id: header.car_id,
      timestamp: header.timestamp,
      description: header.description,
      details: headerDetails.map((detail) => ({
        column_name: detail.column_name,
        old_value: detail.old_value,
        new_value: detail.new_value,
      })),
    }
  })
}

// Generate mock data for all cars
export const generateMockAuditData = (carIds: string[]) => {
  const headers = generateMockAuditHeaders(carIds)
  const details = generateMockAuditDetails(headers)

  return {
    headers,
    details,
    getRepairHistory: (carId: string) => getRepairHistory(carId, headers, details),
  }
}

// Extract car IDs from mock data
const extractCarIds = () => {
  try {
    // This is a placeholder - in a real app, you'd import the actual car IDs
    return Array.from({ length: 50 }, (_, i) => `car_${i + 1}`)
  } catch (error) {
    console.error("Failed to extract car IDs:", error)
    return []
  }
}

// Export the mock audit data
export const mockAuditData = generateMockAuditData(extractCarIds())
