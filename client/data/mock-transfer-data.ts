import type { TransferHistoryEntry } from "@/types/audit"

// Generate random date within the last year
const randomDate = () => {
  const now = new Date()
  const pastDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
  const randomTime = pastDate.getTime() + Math.random() * (now.getTime() - pastDate.getTime())
  return new Date(randomTime).toISOString()
}

// Generate mock transfer history
export const generateMockTransferHistory = (carIds: string[]): Record<string, TransferHistoryEntry[]> => {
  const transferHistory: Record<string, TransferHistoryEntry[]> = {}
  let transferId = 1

  const locationOptions = ["Bangkok", "Chiang Mai", "Phuket", "Pattaya", "Khon Kaen", "Hat Yai"]
  const transportTypeOptions = ["Personal", "Company", "Third-party"]
  const transporterOptions = ["Transporter A", "Transporter B", "Transporter C", "Transporter D"]
  const personalOptions = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Davis"]
  const companyOptions = ["Transport Co. Ltd.", "Fast Delivery Inc.", "Speedy Logistics", "Safe Transit Co."]

  carIds.forEach((carId) => {
    // Generate 0-3 transfer entries per car
    const entryCount = Math.floor(Math.random() * 4)
    const entries: TransferHistoryEntry[] = []

    for (let i = 0; i < entryCount; i++) {
      const transportType = transportTypeOptions[Math.floor(Math.random() * transportTypeOptions.length)]

      const entry: TransferHistoryEntry = {
        transfer_id: transferId++,
        car_id: carId,
        timestamp: randomDate(),
        description: `Transfer #${i + 1} for ${carId}`,
        details: [],
      }

      // Add parking location
      entry.details.push({
        column_name: "parking_location",
        old_value: locationOptions[Math.floor(Math.random() * locationOptions.length)],
        new_value: locationOptions[Math.floor(Math.random() * locationOptions.length)],
      })

      // Add type of transport
      entry.details.push({
        column_name: "type_of_transport",
        old_value: transportTypeOptions[Math.floor(Math.random() * transportTypeOptions.length)],
        new_value: transportType,
      })

      // Add auction transporter
      entry.details.push({
        column_name: "auction_transporter",
        old_value: transporterOptions[Math.floor(Math.random() * transporterOptions.length)],
        new_value: transporterOptions[Math.floor(Math.random() * transporterOptions.length)],
      })

      // Add transport personal
      entry.details.push({
        column_name: "transport_personal",
        old_value: personalOptions[Math.floor(Math.random() * personalOptions.length)],
        new_value: personalOptions[Math.floor(Math.random() * personalOptions.length)],
      })

      // Add transport company
      entry.details.push({
        column_name: "transport_company",
        old_value: companyOptions[Math.floor(Math.random() * companyOptions.length)],
        new_value: companyOptions[Math.floor(Math.random() * companyOptions.length)],
      })

      // Add transport payments based on transport type
      if (transportType === "Personal") {
        entry.details.push({
          column_name: "transport_2_personal_payment",
          old_value: Math.floor(Math.random() * 5000),
          new_value: Math.floor(Math.random() * 5000),
        })
        entry.details.push({
          column_name: "transport_3_tl_payment",
          old_value: 0,
          new_value: 0,
        })
      } else {
        entry.details.push({
          column_name: "transport_2_personal_payment",
          old_value: 0,
          new_value: 0,
        })
        entry.details.push({
          column_name: "transport_3_tl_payment",
          old_value: Math.floor(Math.random() * 5000),
          new_value: Math.floor(Math.random() * 5000),
        })
      }

      entries.push(entry)
    }

    transferHistory[carId] = entries.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  })

  return transferHistory
}

// Extract car IDs from mock data
const extractCarIds = () => {
  try {
    // This is a placeholder - in a real app, you'd import the actual car IDs
    return Array.from({ length: 50 }, (_, i) => `car_${i + 1}`)
  } catch (error) {
    console.error("Failed to extract car IDs:", error)
    return []
  }
}

// Export the mock transfer data
export const mockTransferData = generateMockTransferHistory(extractCarIds())

// Function to get transfer history for a specific car
export const getTransferHistory = (carId: string): TransferHistoryEntry[] => {
  return mockTransferData[carId] || []
}
