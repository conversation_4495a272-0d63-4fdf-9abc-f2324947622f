/**
 * API Examples
 *
 * This file provides examples of how to use the API service
 * in different components and scenarios.
 */

import { apiService } from "./api-service"
import type {
  CreateVehicleRequest,
  UpdateVehicleRequest,
  UpdateVehicleStatusRequest,
  AddRepairHistoryRequest,
  AddTransferHistoryRequest,
  UpdateFinanceInfoRequest,
  UpdateSelloutInfoRequest,
  GeneratePaymentDocumentRequest,
} from "./api-formats"

/**
 * ===================================
 * VEHICLES EXAMPLES
 * ===================================
 */

// Example: Fetch vehicles with filtering
export const fetchVehiclesExample = async () => {
  try {
    // Get all available vehicles
    const availableVehicles = await apiService.getVehicles({
      status: "available",
      sortBy: "stockInfo.index_number",
      sortOrder: "asc",
    })
    console.log("Available vehicles:", availableVehicles)

    // Search for vehicles by tank number
    const searchResults = await apiService.getVehicles({
      searchField: "buyin.tank_number",
      search: "TK12345",
    })
    console.log("Search results:", searchResults)

    return { availableVehicles, searchResults }
  } catch (error) {
    console.error("Error fetching vehicles:", error)
    throw error
  }
}

// Example: Create a new vehicle
export const createVehicleExample = async () => {
  try {
    const newVehicleData: CreateVehicleRequest = {
      stockInfo: {
        is_auction_car: true,
        index_number: "SP1234",
        registration_book_received_date: "2023-05-15",
        old_license_plate: "กข 1234",
        new_license_plate: "",
        registration_date: "2023-05-20",
        total_investment: 450000,
        listed_price: 510000,
        car_status: "purchase",
      },
      buyin: {
        purchase_date: "2023-05-10",
        parking_location: "Bangkok",
        transport_2_personal_payment: 1500,
        transport_3_tl_payment: 0,
        qc1_auction_lot: 1000,
        ems_registration: 500,
        qc3: 500,
        registration_fee: 2000,
        brand: "Toyota",
        model: "Camry",
        color: "White",
        year: 2020,
        vat_percent: 7,
        purchase_price: 400000,
        purchase_vat_percent: 7,
        operation_cost_incl_vat: 10000,
        transport_1_auction_lot: 2000,
        initial_check: 1000,
        tax_insurance_cost_zero: 5000,
        other_costs_seven: 2000,
        five_three_tax_percentage: 5.3,
        total_purchase_cost: 450000,
        auction_name: "Auto Auction A",
        auction_provinced: "Bangkok",
        auction_order: "AO12345",
        auction_checker: "John Smith",
        auction_transporter: "Transporter A",
        transport_personal: "Driver A",
        transport_company: "Transport Co. A",
        tank_number: "TK12345",
        engine_number: "EN12345",
        book_deposit: 5000,
      },
    }

    const createdVehicle = await apiService.createVehicle(newVehicleData)
    console.log("Created vehicle:", createdVehicle)

    return createdVehicle
  } catch (error) {
    console.error("Error creating vehicle:", error)
    throw error
  }
}

// Example: Update a vehicle
export const updateVehicleExample = async (vehicleId: string) => {
  try {
    const updateData: UpdateVehicleRequest = {
      stockInfo: {
        old_license_plate: "กข 5678",
        new_license_plate: "ขค 1234",
        car_status: "available",
      },
      buyin: {
        color: "Black",
      },
    }

    const updatedVehicle = await apiService.updateVehicle(vehicleId, updateData)
    console.log("Updated vehicle:", updatedVehicle)

    return updatedVehicle
  } catch (error) {
    console.error("Error updating vehicle:", error)
    throw error
  }
}

// Example: Update vehicle status
export const updateVehicleStatusExample = async (vehicleId: string) => {
  try {
    const statusData: UpdateVehicleStatusRequest = {
      status: "repair",
      statusDate: "2023-06-01",
      notes: "Vehicle sent for engine repair",
    }

    const updatedStatus = await apiService.updateVehicleStatus(vehicleId, statusData)
    console.log("Updated status:", updatedStatus)

    return updatedStatus
  } catch (error) {
    console.error("Error updating vehicle status:", error)
    throw error
  }
}

/**
 * ===================================
 * REPAIR HISTORY EXAMPLES
 * ===================================
 */

// Example: Add a repair history record
export const addRepairHistoryExample = async (vehicleId: string) => {
  try {
    const repairData: AddRepairHistoryRequest = {
      description: "Engine repair service",
      details: [
        {
          column_name: "engine_repair_cost",
          old_value: 0,
          new_value: 15000,
        },
        {
          column_name: "suspension_repair_cost",
          old_value: 0,
          new_value: 5000,
        },
      ],
    }

    const addedRepair = await apiService.addRepairRecord(vehicleId, repairData)
    console.log("Added repair record:", addedRepair)

    return addedRepair
  } catch (error) {
    console.error("Error adding repair record:", error)
    throw error
  }
}

/**
 * ===================================
 * TRANSFER HISTORY EXAMPLES
 * ===================================
 */

// Example: Add a transfer history record
export const addTransferHistoryExample = async (vehicleId: string) => {
  try {
    const transferData: AddTransferHistoryRequest = {
      description: "Vehicle transferred to Bangkok location",
      details: [
        {
          column_name: "parking_location",
          old_value: "Chiang Mai",
          new_value: "Bangkok",
        },
        {
          column_name: "type_of_transport",
          old_value: "Company",
          new_value: "Personal",
        },
        {
          column_name: "transport_2_personal_payment",
          old_value: 0,
          new_value: 2000,
        },
      ],
    }

    const addedTransfer = await apiService.addTransferRecord(vehicleId, transferData)
    console.log("Added transfer record:", addedTransfer)

    return addedTransfer
  } catch (error) {
    console.error("Error adding transfer record:", error)
    throw error
  }
}

/**
 * ===================================
 * FINANCE EXAMPLES
 * ===================================
 */

// Example: Update finance information
export const updateFinanceInfoExample = async (vehicleId: string) => {
  try {
    const financeData: UpdateFinanceInfoRequest = {
      finance_received_date: "2023-07-15",
      car_tax_invoice_date: "2023-07-10",
      car_tax_invoice_number: "INV12345",
      car_amount: 450000,
      car_vat_amount: 31500,
      commission_tax_invoice_date: "2023-07-10",
      commission_tax_invoice_number: "COM12345",
      car_commission_amount: 15000,
      input_vat_commission: 1050,
      withholding_tax: 450,
      salesperson: "John Smith",
      bank: "Bangkok Bank",
      marketing_person: "Mark Wilson",
      promotion_customer: 5000,
      bonus_insurance_car_life_engine: 3000,
      customer_name: "Jane Doe",
      customer_address_or_advance_payment: "123 Sample St, Bangkok",
      down_payment: 100000,
      loanprotectioninsurance: 5000,
      accident_insurance: 8000,
      car_insurance: 12000,
      bank_documents: 1000,
    }

    const updatedFinance = await apiService.updateFinanceInfo(vehicleId, financeData)
    console.log("Updated finance info:", updatedFinance)

    return updatedFinance
  } catch (error) {
    console.error("Error updating finance info:", error)
    throw error
  }
}

/**
 * ===================================
 * SELLOUT EXAMPLES
 * ===================================
 */

// Example: Update sellout information
export const updateSelloutInfoExample = async (vehicleId: string) => {
  try {
    const selloutData: UpdateSelloutInfoRequest = {
      sale_date: "2023-08-01",
      owner_name: "Jane Doe",
      customer_address: "123 Sample St, Bangkok",
      actual_selling_price: 510000,
      commission_s: 5000,
      commission_agent: 10000,
      commission_manager: 2500,
      sales_channel: "Direct",
    }

    const updatedSellout = await apiService.updateSelloutInfo(vehicleId, selloutData)
    console.log("Updated sellout info:", updatedSellout)

    return updatedSellout
  } catch (error) {
    console.error("Error updating sellout info:", error)
    throw error
  }
}

/**
 * ===================================
 * DOCUMENTS EXAMPLES
 * ===================================
 */

// Example: Upload documents
export const uploadDocumentsExample = async (vehicleId: string, files: File[]) => {
  try {
    const uploadedDocs = await apiService.uploadDocuments(vehicleId, files)
    console.log("Uploaded documents:", uploadedDocs)

    return uploadedDocs
  } catch (error) {
    console.error("Error uploading documents:", error)
    throw error
  }
}

// Example: Generate payment document
export const generatePaymentDocumentExample = async () => {
  try {
    const paymentData: GeneratePaymentDocumentRequest = {
      documentType: "qc",
      qcName: "John Smith",
      purchaseDate: "2023-06-15",
      paymentAmount: 5000,
      selectedVehicles: [
        {
          id: "car_1",
          indexNumber: "SP1234",
          brand: "Toyota",
          model: "Camry",
          tankNumber: "TK12345",
          cost: 1000,
        },
        {
          id: "car_2",
          indexNumber: "SP1235",
          brand: "Honda",
          model: "Civic",
          tankNumber: "TK12346",
          cost: 1000,
        },
      ],
    }

    const generatedDoc = await apiService.generatePaymentDocument(paymentData)
    console.log("Generated payment document:", generatedDoc)

    return generatedDoc
  } catch (error) {
    console.error("Error generating payment document:", error)
    throw error
  }
}

/**
 * ===================================
 * REPORTS EXAMPLES
 * ===================================
 */

// Example: Get monthly summary report
export const getMonthlySummaryExample = async () => {
  try {
    const monthlySummary = await apiService.getMonthlySummary({
      month: 7,
      year: 2023,
    })
    console.log("Monthly summary:", monthlySummary)

    return monthlySummary
  } catch (error) {
    console.error("Error getting monthly summary:", error)
    throw error
  }
}

// Example: Get sales commission report
export const getSalesCommissionExample = async () => {
  try {
    const salesCommission = await apiService.getSalesCommission({
      month: 7,
      year: 2023,
      salesperson: "John Smith",
    })
    console.log("Sales commission report:", salesCommission)

    return salesCommission
  } catch (error) {
    console.error("Error getting sales commission report:", error)
    throw error
  }
}

/**
 * ===================================
 * AUTHENTICATION EXAMPLES
 * ===================================
 */

// Example: Login
export const loginExample = async () => {
  try {
    const loginData = {
      username: "admin",
      password: "password123",
      rememberMe: true,
    }

    const loginResult = await apiService.login(loginData)
    console.log("Login successful:", loginResult)

    return loginResult
  } catch (error) {
    console.error("Login failed:", error)
    throw error
  }
}

// Example: Get current user
export const getCurrentUserExample = async () => {
  try {
    const currentUser = await apiService.getCurrentUser()
    console.log("Current user:", currentUser)

    return currentUser
  } catch (error) {
    console.error("Error getting current user:", error)
    throw error
  }
}
