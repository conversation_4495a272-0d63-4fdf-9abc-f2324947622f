/**
 * API Service
 *
 * This file provides a service layer for making API requests.
 * It includes example implementations for each endpoint.
 */

import axios from "axios"
import { API_BASE_URL } from "./api-endpoints"

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
})

// Add request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error),
)

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle common errors (401, 403, 500, etc.)
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // Redirect to login or refresh token
          localStorage.removeItem("token")
          window.location.href = "/login"
          break
        case 403:
          // Handle forbidden
          console.error("Access forbidden")
          break
        case 500:
          // Handle server error
          console.error("Server error")
          break
      }
    }
    return Promise.reject(error)
  },
)

/**
 * Example API service implementation
 *
 * This is a draft of how the API service could be implemented.
 * In a real application, you would implement all the methods
 * for each endpoint defined in api-endpoints.ts.
 */
export const apiService = {
  // ===== VEHICLES =====

  // Get all vehicles with optional filtering
  getVehicles: async (params = {}) => {
    const response = await apiClient.get("/vehicles", { params })
    return response.data
  },

  // Get a single vehicle by ID
  getVehicleById: async (id) => {
    const response = await apiClient.get(`/vehicles/${id}`)
    return response.data
  },

  // Create a new vehicle
  createVehicle: async (vehicleData) => {
    const response = await apiClient.post("/vehicles", vehicleData)
    return response.data
  },

  // Update a vehicle
  updateVehicle: async (id, vehicleData) => {
    const response = await apiClient.put(`/vehicles/${id}`, vehicleData)
    return response.data
  },

  // Delete a vehicle
  deleteVehicle: async (id) => {
    const response = await apiClient.delete(`/vehicles/${id}`)
    return response.data
  },

  // Update vehicle status
  updateVehicleStatus: async (id, statusData) => {
    const response = await apiClient.patch(`/vehicles/${id}/status`, statusData)
    return response.data
  },

  // ===== REPAIR HISTORY =====

  // Get repair history for a vehicle
  getRepairHistory: async (vehicleId) => {
    const response = await apiClient.get(`/vehicles/${vehicleId}/repairs`)
    return response.data
  },

  // Add a repair record
  addRepairRecord: async (vehicleId, repairData) => {
    const response = await apiClient.post(`/vehicles/${vehicleId}/repairs`, repairData)
    return response.data
  },

  // ===== TRANSFER HISTORY =====

  // Get transfer history for a vehicle
  getTransferHistory: async (vehicleId) => {
    const response = await apiClient.get(`/vehicles/${vehicleId}/transfers`)
    return response.data
  },

  // Add a transfer record
  addTransferRecord: async (vehicleId, transferData) => {
    const response = await apiClient.post(`/vehicles/${vehicleId}/transfers`, transferData)
    return response.data
  },

  // ===== FINANCE =====

  // Get finance information for a vehicle
  getFinanceInfo: async (vehicleId) => {
    const response = await apiClient.get(`/vehicles/${vehicleId}/finance`)
    return response.data
  },

  // Update finance information
  updateFinanceInfo: async (vehicleId, financeData) => {
    const response = await apiClient.put(`/vehicles/${vehicleId}/finance`, financeData)
    return response.data
  },

  // ===== SELLOUT =====

  // Get sellout information for a vehicle
  getSelloutInfo: async (vehicleId) => {
    const response = await apiClient.get(`/vehicles/${vehicleId}/sellout`)
    return response.data
  },

  // Update sellout information
  updateSelloutInfo: async (vehicleId, selloutData) => {
    const response = await apiClient.put(`/vehicles/${vehicleId}/sellout`, selloutData)
    return response.data
  },

  // ===== DOCUMENTS =====

  // Get all documents for a vehicle
  getDocuments: async (vehicleId) => {
    const response = await apiClient.get(`/vehicles/${vehicleId}/documents`)
    return response.data
  },

  // Upload document(s)
  uploadDocuments: async (vehicleId, files) => {
    const formData = new FormData()
    files.forEach((file) => {
      formData.append("files", file)
    })

    const response = await apiClient.post(`/vehicles/${vehicleId}/documents`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })

    return response.data
  },

  // Delete a document
  deleteDocument: async (vehicleId, documentId) => {
    const response = await apiClient.delete(`/vehicles/${vehicleId}/documents/${documentId}`)
    return response.data
  },

  // Generate payment document
  generatePaymentDocument: async (paymentData) => {
    const response = await apiClient.post("/documents/payment", paymentData)
    return response.data
  },

  // Generate tax document
  generateTaxDocument: async (taxData) => {
    const response = await apiClient.post("/documents/tax", taxData)
    return response.data
  },

  // ===== REPORTS =====

  // Get monthly summary
  getMonthlySummary: async (params) => {
    const response = await apiClient.get("/reports/monthly-summary", { params })
    return response.data
  },

  // Get sales commission report
  getSalesCommission: async (params) => {
    const response = await apiClient.get("/reports/sales-commission", { params })
    return response.data
  },

  // Get monthly tax report
  getMonthlyTax: async (params) => {
    const response = await apiClient.get("/reports/monthly-tax", { params })
    return response.data
  },

  // Get daily auction summary
  getDailyAuction: async (params) => {
    const response = await apiClient.get("/reports/daily-auction", { params })
    return response.data
  },

  // Get performance analytics
  getPerformance: async (params) => {
    const response = await apiClient.get("/reports/performance", { params })
    return response.data
  },

  // ===== AUTHENTICATION =====

  // Login
  login: async (credentials) => {
    const response = await apiClient.post("/auth/login", credentials)
    // Store token in localStorage
    if (response.data.token) {
      localStorage.setItem("token", response.data.token)
    }
    return response.data
  },

  // Logout
  logout: async () => {
    const response = await apiClient.post("/auth/logout")
    // Remove token from localStorage
    localStorage.removeItem("token")
    return response.data
  },

  // Get current user
  getCurrentUser: async () => {
    const response = await apiClient.get("/auth/me")
    return response.data
  },
}
