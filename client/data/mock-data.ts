// Update the mock data to include the new statuses
import type { CarStockInfo, CarBuyin, CarRepair, CarFinance, CarSellout, CarComplete } from "../types/models"

// Helper function to generate random dates within a range
const randomDate = (start: Date, end: Date): string => {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  return date.toISOString().split("T")[0]
}

// Generate mock data for CarBuyin
const generateMockCarBuyin = (car_id: string): CarBuyin => {
  const brands = ["Toyota", "Honda", "Nissan", "Mazda", "Mitsubishi", "Isuzu", "Ford", "Chevrolet"]
  const models = ["Camry", "Civic", "Altima", "3", "Lancer", "D-Max", "Ranger", "Cruze"]
  const colors = ["White", "Black", "Silver", "Red", "Blue", "Gray"]
  const locations = ["Bangkok", "Chiang Mai", "Phuket", "Pat<PERSON><PERSON>", "Khon Kaen", "Hat Yai"]
  const auction_names = ["Auto Auction A", "Car Auction B", "Vehicle Auction C", "Motor Auction D"]
  const provinces = ["Bangkok", "Chiang Mai", "Phuket", "Chonburi", "Khon Kaen", "Songkhla"]

  return {
    car_id,
    created_at: randomDate(new Date(2022, 0, 1), new Date(2023, 0, 1)),
    updated_at: randomDate(new Date(2023, 0, 1), new Date()),
    purchase_date: randomDate(new Date(2022, 0, 1), new Date()),
    parking_location: locations[Math.floor(Math.random() * locations.length)],
    transport_2_personal_payment: Math.floor(Math.random() * 5000),
    transport_3_tl_payment: Math.floor(Math.random() * 3000),
    qc1_auction_lot: Math.floor(Math.random() * 2000),
    ems_registration_qc3: Math.floor(Math.random() * 1000),
    registration_fee: Math.floor(Math.random() * 2000),
    brand: brands[Math.floor(Math.random() * brands.length)],
    model: models[Math.floor(Math.random() * models.length)],
    color: colors[Math.floor(Math.random() * colors.length)],
    year: 2015 + Math.floor(Math.random() * 8),
    vat_percent: 7,
    purchase_price: 300000 + Math.floor(Math.random() * 700000),
    purchase_vat_percent: 7,
    operation_cost_incl_vat: Math.floor(Math.random() * 50000),
    transport_1_auction_lot: Math.floor(Math.random() * 5000),
    initial_check: Math.floor(Math.random() * 2000),
    tax_insurance_cost_zero: Math.floor(Math.random() * 10000),
    other_costs_seven: Math.floor(Math.random() * 5000),
    five_three_tax_percentage: 5.3,
    total_purchase_cost: 0, // Will be calculated
    auction_name: auction_names[Math.floor(Math.random() * auction_names.length)],
    auction_provinced: provinces[Math.floor(Math.random() * provinces.length)],
    auction_order: `AO${Math.floor(10000 + Math.random() * 90000)}`,
    auction_checker: `Person ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
    auction_transporter: `Transporter ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
    transport_personal: `Driver ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
    transport_company: `Transport Co. ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
    tank_number: `TK${Math.floor(10000 + Math.random() * 90000)}`,
    engine_number: `EN${Math.floor(10000 + Math.random() * 90000)}`,
    book_deposit: Math.floor(Math.random() * 10000),
  }
}

// Generate mock data for CarStockInfo
const generateMockCarStockInfo = (car_id: string, buyin: CarBuyin): CarStockInfo => {
  // Define all possible statuses with their weights
  const allStatuses: (
    | "purchase"
    | "transfer"
    | "repair"
    | "available"
    | "finance_request"
    | "finance_done"
    | "sold"
    | "reserved"
  )[] = ["purchase", "transfer", "repair", "available", "finance_request", "finance_done", "sold", "reserved"]

  // Create a weighted distribution for more realistic data
  const weightedStatuses = [
    ...Array(3).fill("purchase"),
    ...Array(4).fill("transfer"),
    ...Array(5).fill("repair"),
    ...Array(15).fill("available"),
    ...Array(6).fill("finance_request"),
    ...Array(4).fill("finance_done"),
    ...Array(10).fill("sold"),
    ...Array(3).fill("reserved"),
  ]

  const total_investment =
    buyin.purchase_price +
    buyin.operation_cost_incl_vat +
    buyin.transport_1_auction_lot +
    buyin.transport_2_personal_payment +
    buyin.transport_3_tl_payment

  return {
    car_id,
    created_at: buyin.created_at,
    updated_at: buyin.updated_at,
    is_auction_car: Math.random() > 0.5,
    index_number: `SP${Math.floor(1000 + Math.random() * 9000)}`,
    registration_book_received_date: randomDate(new Date(2022, 0, 1), new Date()),
    old_license_plate: `${Math.floor(Math.random() * 10)}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(1000 + Math.random() * 9000)}`,
    new_license_plate: `${Math.floor(Math.random() * 10)}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(1000 + Math.random() * 9000)}`,
    registration_date: randomDate(new Date(2022, 0, 1), new Date()),
    total_investment,
    listed_price: total_investment * (1 + Math.random() * 0.3), // 0-30% markup
    car_status: weightedStatuses[Math.floor(Math.random() * weightedStatuses.length)] as any,
    notes: Math.random() > 0.7 ? `Note for car ${car_id}` : undefined,
  }
}

// Generate mock data for CarRepair
const generateMockCarRepair = (car_id: string): CarRepair => {
  return {
    car_id,
    created_at: randomDate(new Date(2022, 0, 1), new Date(2023, 0, 1)),
    updated_at: randomDate(new Date(2023, 0, 1), new Date()),
    repainting_cost: Math.floor(Math.random() * 20000),
    engine_repair_cost: Math.floor(Math.random() * 30000),
    suspension_repair_cost: Math.floor(Math.random() * 15000),
    autopart_cost: Math.floor(Math.random() * 25000),
    battery_cost: Math.floor(Math.random() * 5000),
    tires_wheels_cost: Math.floor(Math.random() * 12000),
  }
}

// Generate mock data for CarFinance
const generateMockCarFinance = (car_id: string, stockInfo: CarStockInfo): CarFinance => {
  const banks = ["Bangkok Bank", "Kasikorn Bank", "Siam Commercial Bank", "Krung Thai Bank", "TMB Bank"]
  const names = ["John Doe", "Jane Smith", "Robert Johnson", "Emily Brown", "Michael Davis"]

  return {
    car_id,
    created_at: randomDate(new Date(2022, 0, 1), new Date(2023, 0, 1)),
    updated_at: randomDate(new Date(2023, 0, 1), new Date()),
    finance_received_date: randomDate(new Date(2022, 0, 1), new Date()),
    car_tax_invoice_date: randomDate(new Date(2022, 0, 1), new Date()),
    car_tax_invoice_number: `INV${Math.floor(10000 + Math.random() * 90000)}`,
    car_amount: stockInfo.listed_price,
    car_vat_amount: stockInfo.listed_price * 0.07,
    commission_tax_invoice_date: randomDate(new Date(2022, 0, 1), new Date()),
    commission_tax_invoice_number: `COM${Math.floor(10000 + Math.random() * 90000)}`,
    car_commission_amount: Math.floor(stockInfo.listed_price * 0.03),
    input_vat_commission: Math.floor(stockInfo.listed_price * 0.03 * 0.07),
    withholding_tax: Math.floor(stockInfo.listed_price * 0.01),
    salesperson: `Salesperson ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
    bank: banks[Math.floor(Math.random() * banks.length)],
    marketing_person: `Marketer ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
    promotion_customer: Math.floor(Math.random() * 10000),
    bonus_insurance_car_life_engine: Math.floor(Math.random() * 5000),
    customer_name: names[Math.floor(Math.random() * names.length)],
    customer_address_or_advance_payment:
      Math.random() > 0.5
        ? `123 Sample St, District ${Math.floor(Math.random() * 10)}, Bangkok`
        : `Advance Payment: ${Math.floor(Math.random() * 100000)}`,
    down_payment: Math.floor(stockInfo.listed_price * 0.2),
    loanprotectioninsurance: Math.floor(Math.random() * 10000),
    accident_insurance: Math.floor(Math.random() * 15000),
    car_insurance: Math.floor(Math.random() * 20000),
    bank_documents: Math.floor(Math.random() * 2000),
  }
}

// Generate mock data for CarSellout
const generateMockCarSellout = (car_id: string, stockInfo: CarStockInfo): CarSellout => {
  const channels = ["Direct", "Dealer", "Online Platform", "Referral", "Exhibition"]
  const names = ["John Doe", "Jane Smith", "Robert Johnson", "Emily Brown", "Michael Davis"]

  return {
    car_id,
    created_at: randomDate(new Date(2022, 0, 1), new Date(2023, 0, 1)),
    updated_at: randomDate(new Date(2023, 0, 1), new Date()),
    sale_date: randomDate(new Date(2022, 0, 1), new Date()),
    owner_name: names[Math.floor(Math.random() * names.length)],
    customer_address: `${Math.floor(Math.random() * 1000)} Sample St, District ${Math.floor(Math.random() * 10)}, Bangkok`,
    actual_selling_price: stockInfo.listed_price * (0.9 + Math.random() * 0.2), // 90-110% of listed price
    commission_s: Math.floor(stockInfo.listed_price * 0.01),
    commission_agent: Math.floor(stockInfo.listed_price * 0.02),
    commission_manager: Math.floor(stockInfo.listed_price * 0.005),
    sales_channel: channels[Math.floor(Math.random() * channels.length)],
  }
}

// Generate a complete set of mock data
export const generateMockCars = (count: number): CarComplete[] => {
  const cars: CarComplete[] = []

  for (let i = 0; i < count; i++) {
    const car_id = `car_${i + 1}`
    const buyin = generateMockCarBuyin(car_id)
    const stockInfo = generateMockCarStockInfo(car_id, buyin)
    const repair = generateMockCarRepair(car_id)

    // Only generate finance and sellout data for certain statuses
    let finance
    let sellout

    if (stockInfo.car_status === "sold" || stockInfo.car_status === "finance_done") {
      finance = generateMockCarFinance(car_id, stockInfo)
    }

    if (stockInfo.car_status === "sold") {
      sellout = generateMockCarSellout(car_id, stockInfo)
    }

    cars.push({
      stockInfo,
      buyin,
      repair,
      finance,
      sellout,
    })
  }

  return cars
}

// Export 50 mock cars
export const mockCars = generateMockCars(50)
