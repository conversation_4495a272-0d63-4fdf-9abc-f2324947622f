/**
 * API Endpoints and Data Formats
 *
 * This file defines all the API endpoints and data formats required for
 * frontend-backend communication in the Autocar Application.
 *
 * Note: This is a draft and does not contain actual implementation.
 */

// Base API URL - should be configured based on environment
export const API_BASE_URL = "/api"

/**
 * ===================================
 * VEHICLES API
 * ===================================
 */
export const VEHICLES_API = {
  // Get all vehicles with optional filtering
  GET_ALL: `${API_BASE_URL}/vehicles`,
  // Get a single vehicle by ID
  GET_BY_ID: (id: string) => `${API_BASE_URL}/vehicles/${id}`,
  // Create a new vehicle
  CREATE: `${API_BASE_URL}/vehicles`,
  // Update a vehicle
  UPDATE: (id: string) => `${API_BASE_URL}/vehicles/${id}`,
  // Delete a vehicle
  DELETE: (id: string) => `${API_BASE_URL}/vehicles/${id}`,
  // Update vehicle status
  UPDATE_STATUS: (id: string) => `${API_BASE_URL}/vehicles/${id}/status`,
}

/**
 * ===================================
 * REPAIR HISTORY API
 * ===================================
 */
export const REPAIR_API = {
  // Get repair history for a vehicle
  GET_HISTORY: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/repairs`,
  // Add a repair record
  ADD_RECORD: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/repairs`,
  // Update a repair record
  UPDATE_RECORD: (vehicleId: string, repairId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/repairs/${repairId}`,
  // Delete a repair record
  DELETE_RECORD: (vehicleId: string, repairId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/repairs/${repairId}`,
}

/**
 * ===================================
 * TRANSFER HISTORY API
 * ===================================
 */
export const TRANSFER_API = {
  // Get transfer history for a vehicle
  GET_HISTORY: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/transfers`,
  // Add a transfer record
  ADD_RECORD: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/transfers`,
  // Update a transfer record
  UPDATE_RECORD: (vehicleId: string, transferId: string) =>
    `${API_BASE_URL}/vehicles/${vehicleId}/transfers/${transferId}`,
  // Delete a transfer record
  DELETE_RECORD: (vehicleId: string, transferId: string) =>
    `${API_BASE_URL}/vehicles/${vehicleId}/transfers/${transferId}`,
}

/**
 * ===================================
 * FINANCE API
 * ===================================
 */
export const FINANCE_API = {
  // Get finance information for a vehicle
  GET_INFO: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/finance`,
  // Create/update finance information
  UPDATE: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/finance`,
  // Get list of banks
  GET_BANKS: `${API_BASE_URL}/finance/banks`,
  // Get finance request status
  GET_REQUEST_STATUS: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/finance/status`,
}

/**
 * ===================================
 * SELLOUT API
 * ===================================
 */
export const SELLOUT_API = {
  // Get sellout information for a vehicle
  GET_INFO: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/sellout`,
  // Create/update sellout information
  UPDATE: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/sellout`,
  // Get sales channels
  GET_SALES_CHANNELS: `${API_BASE_URL}/sellout/channels`,
}

/**
 * ===================================
 * DOCUMENTS API
 * ===================================
 */
export const DOCUMENTS_API = {
  // Get all documents for a vehicle
  GET_ALL: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/documents`,
  // Upload document(s)
  UPLOAD: (vehicleId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/documents`,
  // Delete a document
  DELETE: (vehicleId: string, documentId: string) => `${API_BASE_URL}/vehicles/${vehicleId}/documents/${documentId}`,
  // Generate payment document
  GENERATE_PAYMENT: `${API_BASE_URL}/documents/payment`,
  // Generate tax document
  GENERATE_TAX: `${API_BASE_URL}/documents/tax`,
}

/**
 * ===================================
 * REPORTS API
 * ===================================
 */
export const REPORTS_API = {
  // Get monthly summary
  MONTHLY_SUMMARY: `${API_BASE_URL}/reports/monthly-summary`,
  // Get sales commission report
  SALES_COMMISSION: `${API_BASE_URL}/reports/sales-commission`,
  // Get monthly tax report
  MONTHLY_TAX: `${API_BASE_URL}/reports/monthly-tax`,
  // Get daily auction summary
  DAILY_AUCTION: `${API_BASE_URL}/reports/daily-auction`,
  // Get performance analytics
  PERFORMANCE: `${API_BASE_URL}/reports/performance`,
}

/**
 * ===================================
 * REFERENCE DATA API
 * ===================================
 */
export const REFERENCE_API = {
  // Get car brands
  GET_BRANDS: `${API_BASE_URL}/reference/brands`,
  // Get car models for a brand
  GET_MODELS: (brand: string) => `${API_BASE_URL}/reference/brands/${brand}/models`,
  // Get locations
  GET_LOCATIONS: `${API_BASE_URL}/reference/locations`,
  // Get auction names
  GET_AUCTION_NAMES: `${API_BASE_URL}/reference/auctions`,
  // Get provinces
  GET_PROVINCES: `${API_BASE_URL}/reference/provinces`,
  // Get QC checkers
  GET_QC_CHECKERS: `${API_BASE_URL}/reference/qc-checkers`,
  // Get transporters
  GET_TRANSPORTERS: `${API_BASE_URL}/reference/transporters`,
  // Get salespersons
  GET_SALESPERSONS: `${API_BASE_URL}/reference/salespersons`,
}

/**
 * ===================================
 * AUTHENTICATION API
 * ===================================
 */
export const AUTH_API = {
  // Login
  LOGIN: `${API_BASE_URL}/auth/login`,
  // Logout
  LOGOUT: `${API_BASE_URL}/auth/logout`,
  // Get current user
  GET_CURRENT_USER: `${API_BASE_URL}/auth/me`,
}
