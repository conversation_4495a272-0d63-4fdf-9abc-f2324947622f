// import { type MonthlySummary, monthlySummaryCalculations } from "@/types/models"
import  { type MonthlySummary, monthlySummaryCalculations } from "@types/models"
import { v4 as uuidv4 } from "uuid"

// Mock brands and models
const carBrands = ["Toyota", "Honda", "Nissan", "Mazda", "Mitsubishi", "Isuzu", "Ford", "Chevrolet"]
const bankNames = ["Kasikorn", "Bangkok Bank", "Siam Commercial", "Krungsri", "TMB", "UOB", "CIMB"]
const salespeople = ["Somchai", "Somsak", "Somying", "Nattapong", "Siriporn", "Rattana", "Wichai"]

// Generate a random date within a range
function randomDate(start: Date, end: Date): string {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString()
}

// Generate a random number within a range
function randomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1) + min)
}

// Generate a random tank number
function generateTankNumber(): string {
  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
  const letter1 = letters[Math.floor(Math.random() * letters.length)]
  const letter2 = letters[Math.floor(Math.random() * letters.length)]
  const numbers = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, "0")
  return `${letter1}${letter2}${numbers}`
}

// Generate mock monthly summary data
export async function getMonthlySummaryData(month: number, year: number): Promise<MonthlySummary[]> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  // Generate between 1 and 8 cars for the month
  const numCars = randomNumber(1, 8)
  const result: MonthlySummary[] = []

  // Start and end dates for the selected month
  const startDate = new Date(year, month - 1, 1)
  const endDate = new Date(year, month, 0)

  for (let i = 0; i < numCars; i++) {
    // Generate purchase date (1-12 months before sale date)
    const saleDate = randomDate(startDate, endDate)
    const purchaseDate = new Date(
      new Date(saleDate).setMonth(new Date(saleDate).getMonth() - randomNumber(1, 12)),
    ).toISOString()

    // Calculate time in stock
    const timeInStock = monthlySummaryCalculations.calculateTimeInStock(purchaseDate, saleDate)

    // Generate base financial values
    const purchasePrice = randomNumber(300000, 800000)
    const actualSellingPrice = purchasePrice * (1 + randomNumber(5, 30) / 100) // 5-30% markup
    const vatPercent = 7

    // Calculate derived values
    const carAmountFinance = monthlySummaryCalculations.calculateCarAmountFinance(actualSellingPrice)
    const vatOnCarAmount = monthlySummaryCalculations.calculateVatOnCarAmount(carAmountFinance, vatPercent)
    const carCommissionAmount = randomNumber(5000, 20000)

    // Investment costs
    const purchaseVatPercent = purchasePrice * (vatPercent / 100)
    const operationCostInclVat = randomNumber(5000, 15000)
    const transport1AuctionLot = randomNumber(1000, 3000)
    const initialCheck = randomNumber(500, 1500)
    const taxInsuranceCostZero = randomNumber(2000, 5000)
    const otherCostsSeven = randomNumber(1000, 3000)
    const fiveThreeTaxPercentage = purchasePrice * 0.053
    const qc1AuctionLot = randomNumber(500, 1500)
    const transport2PersonalPayment = randomNumber(1000, 2000)
    const transport3TlPayment = randomNumber(500, 1500)

    // Repair costs
    const repaintingCost = randomNumber(0, 10000)
    const engineRepairCost = randomNumber(0, 15000)
    const suspensionRepairCost = randomNumber(0, 8000)
    const autopartCost = randomNumber(0, 12000)
    const batteryCost = randomNumber(0, 3000)
    const tiresWheelsCost = randomNumber(0, 8000)

    // Total investment
    const totalInvestment =
      purchasePrice +
      purchaseVatPercent +
      operationCostInclVat +
      transport1AuctionLot +
      initialCheck +
      taxInsuranceCostZero +
      otherCostsSeven +
      fiveThreeTaxPercentage +
      qc1AuctionLot +
      transport2PersonalPayment +
      transport3TlPayment +
      repaintingCost +
      engineRepairCost +
      suspensionRepairCost +
      autopartCost +
      batteryCost +
      tiresWheelsCost

    // Commission and fees
    const commissionS = randomNumber(3000, 8000)
    const commissionAgent = randomNumber(2000, 5000)
    const commissionManager = randomNumber(1000, 3000)
    const qcSiriporn = randomNumber(0, 1) === 1 ? randomNumber(1000, 3000) : 0
    const miscellaneousSiriporn = 1000 // Fixed rate
    const marketingBakCost = 2000 // Fixed rate
    const financePaymentProcess = monthlySummaryCalculations.calculateFinancePaymentProcess(actualSellingPrice)
    const emsRegistrationQc3 = randomNumber(500, 1500)
    const registrationFee = randomNumber(1000, 3000)
    const rushTransport = 600 // Fixed rate
    const socialsAds = 3000 // Fixed rate
    const promotionCustomer = randomNumber(0, 5000)
    const bonusInsuranceCarLifeEngine = randomNumber(0, 3000)

    // Maintenance costs
    const fuelCost = monthlySummaryCalculations.calculateTimeBasedCost(timeInStock)
    const parkingCost = monthlySummaryCalculations.calculateTimeBasedCost(timeInStock)
    const liquidorCost = 500 // Fixed rate

    // Aggregated costs
    const repairAndManagementCost =
      commissionS +
      commissionAgent +
      commissionManager +
      emsRegistrationQc3 +
      registrationFee +
      promotionCustomer +
      bonusInsuranceCarLifeEngine +
      liquidorCost +
      engineRepairCost +
      suspensionRepairCost +
      autopartCost +
      batteryCost +
      tiresWheelsCost

    // Revenue
    const totalRevenue = actualSellingPrice + carCommissionAmount

    // Profit calculations
    const paymentFee = 50 // Fixed rate
    const initProfit = totalRevenue - totalInvestment - repairAndManagementCost - paymentFee
    const deductTransferTwelve = monthlySummaryCalculations.calculateDeductTransfer(totalInvestment, timeInStock)
    const profitAndLoss = initProfit - deductTransferTwelve
    const profitAndLossPercent = (profitAndLoss / totalInvestment) * 100

    // Additional information
    const brand = carBrands[Math.floor(Math.random() * carBrands.length)]
    const bank = bankNames[Math.floor(Math.random() * bankNames.length)]
    const salesperson = salespeople[Math.floor(Math.random() * salespeople.length)]

    // Create the monthly summary object
    const summary: MonthlySummary = {
      summary_id: uuidv4(),
      car_id: uuidv4(),
      car_number: i + 1,
      purchase_date: purchaseDate,
      sale_date: saleDate,
      tank_number: generateTankNumber(),
      brand,
      vat_percent: vatPercent,
      time_in_stock: timeInStock,
      actual_selling_price: actualSellingPrice,
      car_amount_finance: carAmountFinance,
      vat_on_car_amount: vatOnCarAmount,
      car_commission_amount: carCommissionAmount,
      total_revenue: totalRevenue,
      purchase_price: purchasePrice,
      purchase_vat_percent: purchaseVatPercent,
      operation_cost_incl_vat: operationCostInclVat,
      transport_1_auction_lot: transport1AuctionLot,
      initial_check: initialCheck,
      tax_insurance_cost_zero: taxInsuranceCostZero,
      other_costs_seven: otherCostsSeven,
      five_three_tax_percentage: fiveThreeTaxPercentage,
      qc1_auction_lot: qc1AuctionLot,
      transport_2_personal_payment: transport2PersonalPayment,
      transport_3_tl_payment: transport3TlPayment,
      repainting_cost: repaintingCost,
      total_investment: totalInvestment,
      thirty_document: vatOnCarAmount - purchaseVatPercent,
      commission_s: commissionS,
      commission_agent: commissionAgent,
      commission_manager: commissionManager,
      qc_siriporn: qcSiriporn,
      miscellaneous_siriporn: miscellaneousSiriporn,
      marketing_bak_cost: marketingBakCost,
      finance_payment_process: financePaymentProcess,
      ems_registration_qc3: emsRegistrationQc3,
      registration_fee: registrationFee,
      rush_transport: rushTransport,
      socials_ads: socialsAds,
      promotion_customer: promotionCustomer,
      bonus_insurance_car_life_engine: bonusInsuranceCarLifeEngine,
      fuel_cost: fuelCost,
      parking_cost: parkingCost,
      liquidor_cost: liquidorCost,
      engine_repair_cost: engineRepairCost,
      suspension_repair_cost: suspensionRepairCost,
      autopart_cost: autopartCost,
      battery_cost: batteryCost,
      tires_wheels_cost: tiresWheelsCost,
      repair_and_management_cost: repairAndManagementCost,
      payment_fee: paymentFee,
      init_profit: initProfit,
      deduct_transfer_twelve: deductTransferTwelve,
      profit_and_loss: profitAndLoss,
      profit_and_loss_percent: profitAndLossPercent,
      bank,
      salesperson,
    }

    result.push(summary)
  }

  return result
}
