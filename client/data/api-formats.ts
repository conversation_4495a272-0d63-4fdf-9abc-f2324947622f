/**
 * API Data Formats
 *
 * This file defines the data formats for requests and responses
 * when interacting with the backend API.
 */

import type { CarComplete, CarStockInfo, CarBuyin, CarRepair, CarFinance, CarSellout } from "@/types/models"
import type { RepairHistoryEntry, TransferHistoryEntry } from "@/types/audit"

/**
 * ===================================
 * VEHICLES API FORMATS
 * ===================================
 */

// GET /api/vehicles - Query parameters
export interface GetVehiclesParams {
  status?: string
  brand?: string
  model?: string
  year?: number
  search?: string
  searchField?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

// GET /api/vehicles - Response
export interface GetVehiclesResponse {
  data: CarComplete[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// GET /api/vehicles/:id - Response
export type GetVehicleByIdResponse = CarComplete

// POST /api/vehicles - Request body
export interface CreateVehicleRequest {
  stockInfo: Omit<CarStockInfo, "car_id" | "created_at" | "updated_at">
  buyin: Omit<CarBuyin, "car_id" | "created_at" | "updated_at">
  repair?: Omit<CarRepair, "car_id" | "created_at" | "updated_at">
}

// Example of a create vehicle request
export const createVehicleExample: CreateVehicleRequest = {
  stockInfo: {
    is_auction_car: true,
    index_number: "SP1234",
    registration_book_received_date: "2023-05-15",
    old_license_plate: "กข 1234",
    new_license_plate: "",
    registration_date: "2023-05-20",
    total_investment: 450000,
    listed_price: 510000,
    car_status: "purchase",
  },
  buyin: {
    purchase_date: "2023-05-10",
    parking_location: "Bangkok",
    transport_2_personal_payment: 1500,
    transport_3_tl_payment: 0,
    qc1_auction_lot: 1000,
    ems_registration: 500,
    qc3: 500,
    registration_fee: 2000,
    brand: "Toyota",
    model: "Camry",
    color: "White",
    year: 2020,
    vat_percent: 7,
    purchase_price: 400000,
    purchase_vat_percent: 7,
    operation_cost_incl_vat: 10000,
    transport_1_auction_lot: 2000,
    initial_check: 1000,
    tax_insurance_cost_zero: 5000,
    other_costs_seven: 2000,
    five_three_tax_percentage: 5.3,
    total_purchase_cost: 450000,
    auction_name: "Auto Auction A",
    auction_provinced: "Bangkok",
    auction_order: "AO12345",
    auction_checker: "John Smith",
    auction_transporter: "Transporter A",
    transport_personal: "Driver A",
    transport_company: "Transport Co. A",
    tank_number: "TK12345",
    engine_number: "EN12345",
    book_deposit: 5000,
  },
}

// PUT /api/vehicles/:id - Request body
export type UpdateVehicleRequest = Partial<CreateVehicleRequest>

// Example of an update vehicle request
export const updateVehicleExample: UpdateVehicleRequest = {
  stockInfo: {
    old_license_plate: "กข 5678",
    new_license_plate: "ขค 1234",
    car_status: "available",
  },
  buyin: {
    color: "Black",
  },
}

// PATCH /api/vehicles/:id/status - Request body
export interface UpdateVehicleStatusRequest {
  status: string
  statusDate?: string
  notes?: string
}

// Example of a status update request
export const updateStatusExample: UpdateVehicleStatusRequest = {
  status: "repair",
  statusDate: "2023-06-01",
  notes: "Vehicle sent for engine repair",
}

/**
 * ===================================
 * REPAIR HISTORY API FORMATS
 * ===================================
 */

// GET /api/vehicles/:vehicleId/repairs - Response
export type GetRepairHistoryResponse = RepairHistoryEntry[]

// POST /api/vehicles/:vehicleId/repairs - Request body
export interface AddRepairHistoryRequest {
  description: string
  details: {
    column_name: string
    old_value: number | null
    new_value: number | null
  }[]
}

// Example of adding a repair history record
export const addRepairHistoryExample: AddRepairHistoryRequest = {
  description: "Engine repair service",
  details: [
    {
      column_name: "engine_repair_cost",
      old_value: 0,
      new_value: 15000,
    },
    {
      column_name: "suspension_repair_cost",
      old_value: 0,
      new_value: 5000,
    },
  ],
}

/**
 * ===================================
 * TRANSFER HISTORY API FORMATS
 * ===================================
 */

// GET /api/vehicles/:vehicleId/transfers - Response
export type GetTransferHistoryResponse = TransferHistoryEntry[]

// POST /api/vehicles/:vehicleId/transfers - Request body
export interface AddTransferHistoryRequest {
  description: string
  details: {
    column_name: string
    old_value: string | number | null
    new_value: string | number | null
  }[]
}

// Example of adding a transfer history record
export const addTransferHistoryExample: AddTransferHistoryRequest = {
  description: "Vehicle transferred to Bangkok location",
  details: [
    {
      column_name: "parking_location",
      old_value: "Chiang Mai",
      new_value: "Bangkok",
    },
    {
      column_name: "type_of_transport",
      old_value: "Company",
      new_value: "Personal",
    },
    {
      column_name: "transport_2_personal_payment",
      old_value: 0,
      new_value: 2000,
    },
  ],
}

/**
 * ===================================
 * FINANCE API FORMATS
 * ===================================
 */

// GET /api/vehicles/:vehicleId/finance - Response
export type GetFinanceInfoResponse = CarFinance

// PUT /api/vehicles/:vehicleId/finance - Request body
export type UpdateFinanceInfoRequest = Omit<CarFinance, "car_id" | "created_at" | "updated_at">

// Example of updating finance information
export const updateFinanceInfoExample: UpdateFinanceInfoRequest = {
  finance_received_date: "2023-07-15",
  car_tax_invoice_date: "2023-07-10",
  car_tax_invoice_number: "INV12345",
  car_amount: 450000,
  car_vat_amount: 31500,
  commission_tax_invoice_date: "2023-07-10",
  commission_tax_invoice_number: "COM12345",
  car_commission_amount: 15000,
  input_vat_commission: 1050,
  withholding_tax: 450,
  salesperson: "John Smith",
  bank: "Bangkok Bank",
  marketing_person: "Mark Wilson",
  promotion_customer: 5000,
  bonus_insurance_car_life_engine: 3000,
  customer_name: "Jane Doe",
  customer_address_or_advance_payment: "123 Sample St, Bangkok",
  down_payment: 100000,
  loanprotectioninsurance: 5000,
  accident_insurance: 8000,
  car_insurance: 12000,
  bank_documents: 1000,
}

/**
 * ===================================
 * SELLOUT API FORMATS
 * ===================================
 */

// GET /api/vehicles/:vehicleId/sellout - Response
export type GetSelloutInfoResponse = CarSellout

// PUT /api/vehicles/:vehicleId/sellout - Request body
export type UpdateSelloutInfoRequest = Omit<CarSellout, "car_id" | "created_at" | "updated_at">

// Example of updating sellout information
export const updateSelloutInfoExample: UpdateSelloutInfoRequest = {
  sale_date: "2023-08-01",
  owner_name: "Jane Doe",
  customer_address: "123 Sample St, Bangkok",
  actual_selling_price: 510000,
  commission_s: 5000,
  commission_agent: 10000,
  commission_manager: 2500,
  sales_channel: "Direct",
}

/**
 * ===================================
 * DOCUMENTS API FORMATS
 * ===================================
 */

// GET /api/vehicles/:vehicleId/documents - Response
export interface GetDocumentsResponse {
  documents: {
    id: string
    name: string
    size: number
    type: string
    url: string
    uploadDate: string
  }[]
}

// POST /api/vehicles/:vehicleId/documents - Request body (multipart/form-data)
// This will be handled by FormData in the frontend

// POST /api/documents/payment - Request body
export interface GeneratePaymentDocumentRequest {
  documentType: "qc" | "transfer"
  qcName?: string
  transportType?: "personal" | "company"
  transporterName?: string
  purchaseDate: string
  paymentAmount: number
  selectedVehicles: {
    id: string
    indexNumber: string
    brand: string
    model: string
    tankNumber: string
    cost: number
  }[]
}

// Example of generating a payment document
export const generatePaymentDocumentExample: GeneratePaymentDocumentRequest = {
  documentType: "qc",
  qcName: "John Smith",
  purchaseDate: "2023-06-15",
  paymentAmount: 5000,
  selectedVehicles: [
    {
      id: "car_1",
      indexNumber: "SP1234",
      brand: "Toyota",
      model: "Camry",
      tankNumber: "TK12345",
      cost: 1000,
    },
    {
      id: "car_2",
      indexNumber: "SP1235",
      brand: "Honda",
      model: "Civic",
      tankNumber: "TK12346",
      cost: 1000,
    },
  ],
}

// POST /api/documents/tax - Request body
export interface GenerateTaxDocumentRequest {
  vehicleId: string
  date: string
  carTaxInvoiceNumber: string
  commissionTaxInvoiceNumber: string
}

// Example of generating a tax document
export const generateTaxDocumentExample: GenerateTaxDocumentRequest = {
  vehicleId: "car_1",
  date: "2023-07-15",
  carTaxInvoiceNumber: "INV12345",
  commissionTaxInvoiceNumber: "COM12345",
}

/**
 * ===================================
 * REPORTS API FORMATS
 * ===================================
 */

// GET /api/reports/monthly-summary - Query parameters
export interface MonthlySummaryParams {
  month: number
  year: number
}

// GET /api/reports/sales-commission - Query parameters
export interface SalesCommissionParams {
  month: number
  year: number
  salesperson?: string
}

// GET /api/reports/monthly-tax - Query parameters
export interface MonthlyTaxParams {
  month: number
  year: number
  type?: "purchase" | "sellout"
}

// GET /api/reports/daily-auction - Query parameters
export interface DailyAuctionParams {
  date: string
  location?: string
}

// GET /api/reports/performance - Query parameters
export interface PerformanceParams {
  startDate: string
  endDate: string
  reportType: string
}

/**
 * ===================================
 * AUTHENTICATION API FORMATS
 * ===================================
 */

// POST /api/auth/login - Request body
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

// Example of a login request
export const loginExample: LoginRequest = {
  username: "admin",
  password: "password123",
  rememberMe: true,
}

// POST /api/auth/login - Response
export interface LoginResponse {
  token: string
  user: {
    id: string
    username: string
    name: string
    role: string
  }
}

// GET /api/auth/me - Response
export interface GetCurrentUserResponse {
  id: string
  username: string
  name: string
  role: string
  permissions: string[]
}
