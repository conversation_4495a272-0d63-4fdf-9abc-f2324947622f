{
  "compilerOptions": {
    "baseUrl": "./",                     // ✅ Required for paths
    "paths": {
      "@/*": ["./*"],
      "@types/*": ["../types/*"]        // ✅ Alias for shared types
    },
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "target": "ES6",
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }]
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "../types"                          // ✅ Required to include it in project
  ],
  "exclude": ["node_modules"]
}
