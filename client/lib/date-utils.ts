// Date utility functions
import dayjs from "dayjs"
import buddhistEra from "dayjs/plugin/buddhistEra"
import "dayjs/locale/th"

dayjs.extend(buddhistEra)
dayjs.locale("th")

// Convert a date to Buddhist Era
export function toBuddhistDate(date: Date): Date {
    const buddhistYear = date.getFullYear() + 543
    return new Date(buddhistYear, date.getMonth(), date.getDate())
}
// Format a date to display format (DD/MM/YYYY)
export function formatToDisplayDate(date: Date | string): string {
    if (!date) return ""

    const dateObj = typeof date === "string" ? new Date(date) : date
    const day = dateObj.getDate().toString().padStart(2, "0")
    const month = (dateObj.getMonth() + 1).toString().padStart(2, "0")

    // Display Buddhist Era years directly (no conversion)
    // Users should see Buddhist Era years consistently
    const year = dateObj.getFullYear()
    const displayYear = year // Keep the year as-is (Buddhist Era)

    return `${day}/${month}/${displayYear}`
}

// Generate a random date between start and end
export function randomDate(start: Date, end: Date): string {
    // Ensure we're working with dates in the correct range
    const startTime = start.getTime()
    const endTime = end.getTime()

    if (startTime > endTime) {
        throw new Error("Start date must be before end date")
    }

    const randomTime = startTime + Math.random() * (endTime - startTime)
    return new Date(randomTime).toISOString()
}

// Generate a random Buddhist date between start and end
export function randomBuddhistDate(start: Date, end: Date): string {
    // Convert to Buddhist Era if needed
    const buddhistStart = start.getFullYear() >= 2500 ? start : toBuddhistDate(start)
    const buddhistEnd = end.getFullYear() >= 2500 ? end : toBuddhistDate(end)

    return randomDate(buddhistStart, buddhistEnd)
}

// Check if a date is valid
export function isValidDate(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime())
}

// Parse a date string to a Date object
export function parseDate(dateString: string): Date | null {
    const date = new Date(dateString)
    return isValidDate(date) ? date : null
}

// Format a date for API requests
export function formatDateForApi(date: Date): string {
    return date.toISOString()
}

// Get current date in Buddhist Era
export function getCurrentBuddhistDate(): Date {
    return toBuddhistDate(new Date())
}

// Get the Buddhist year from a date
export function getBuddhistYear(date: Date | string): number {
    const dateObj = typeof date === "string" ? new Date(date) : date
    return dateObj.getFullYear()
}

// Convert a Buddhist year to Gregorian
export function toGregorianYear(buddhistYear: number): number {
    return buddhistYear - 543
}

// Convert a Gregorian year to Buddhist
export function toBuddhistYear(gregorianYear: number): number {
    return gregorianYear + 543
}
