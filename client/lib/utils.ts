import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { v4 as uuidv4 } from 'uuid'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a number as currency (THB)
 * @param value Number to format
 * @returns Formatted currency string
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat("th-TH", {
    style: "currency",
    currency: "THB",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value)
}

/**
 * Format a number as a percentage
 * @param value Number to format
 * @returns Formatted percentage string
 */
export function formatPercent(value: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "percent",
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  }).format(value / 100)
}

/**
 * Format a date string
 * @param dateString Date string to format
 * @returns Formatted date string or empty string if invalid
 */
export function formatDate(dateString: string | null | undefined): string {
  if (!dateString) return "";

  try {
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "";
    }

    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return "";
  }
}

/**
 * Format a date string to Buddhist Era (BE) format
 * @param dateString Date string to format
 * @returns Formatted date string in Buddhist Era
 */
export function formatBuddhistDate(dateString: string): string {
  const date = new Date(dateString)

  // Get Gregorian year and add 543 to convert to Buddhist Era
  const gregorianYear = date.getFullYear()
  const buddhistYear = gregorianYear + 543

  // Format the date with Buddhist year
  return new Intl.DateTimeFormat("th-TH", {
    day: "numeric",
    month: "long",
    year: "numeric",
  }).format(date)
}

/**
 * Convert a Gregorian date to Buddhist Era (BE) date
 * @param date Date object or string in Gregorian calendar
 * @returns Date string in Buddhist Era format
 */
export function toBuddhistDate(date: Date | string): string {
  const dateObj = typeof date === "string" ? new Date(date) : date

  // Get Gregorian year and add 543 to convert to Buddhist Era
  const gregorianYear = dateObj.getFullYear()
  const buddhistYear = gregorianYear + 543

  // Format the date with Buddhist year
  return new Intl.DateTimeFormat("th-TH", {
    day: "numeric",
    month: "long",
    year: "numeric",
  }).format(dateObj)
}

/**
 * Convert a Buddhist Era (BE) date to Gregorian date
 * @param buddhistDateString Date string in Buddhist Era format
 * @returns Date object in Gregorian calendar
 */
export function fromBuddhistDate(buddhistDateString: string): Date | null {
  try {
    // Parse the Buddhist date string
    const parts = buddhistDateString.split(/[/\s-]+/)
    if (parts.length < 3) return null

    // Extract day, month, and year
    let day, month, year

    // Handle different formats
    if (parts[0].length === 4) {
      // If format is YYYY-MM-DD
      year = Number.parseInt(parts[0]) - 543
      month = Number.parseInt(parts[1]) - 1
      day = Number.parseInt(parts[2])
    } else {
      // If format is DD-MM-YYYY
      day = Number.parseInt(parts[0])
      month = Number.parseInt(parts[1]) - 1
      year = Number.parseInt(parts[2]) - 543
    }

    // Create and return Gregorian date
    return new Date(year, month, day)
  } catch (error) {
    console.error("Error converting Buddhist date:", error)
    return null
  }
}

export function generateUUID(): string {
  return uuidv4()
}

/**
 * Generate an ISO string with Thailand timezone (UTC+7)
 * @returns ISO string with Thailand timezone
 */
export function getThailandTimeISO(): string {
  // Thailand is UTC+7
  const date = new Date()
  // Convert to Thailand time
  const thailandOffset = 7 * 60 // minutes
  const localOffset = date.getTimezoneOffset() // minutes
  const diff = thailandOffset + localOffset
  const thailandDate = new Date(date.getTime() + diff * 60 * 1000)

  // Format with Thailand timezone
  return thailandDate.toISOString().replace('Z', '+07:00')
}