// "use client"

// import React, {
//     createContext,
//     useContext,
//     useState,
//     useEffect,
//     ReactNode,
// } from "react"

// interface AuthContextType {
//     isLoggedIn: boolean
//     token?: string
//     login: (user: any, token: string) => void
//     logout: () => void
// }

// const AuthContext = createContext<AuthContextType>({
//     isLoggedIn: false,
//     login: () => { },
//     logout: () => { },
//     token: undefined,
// })

// export const AuthProvider = ({ children }: { children: ReactNode }) => {
//     const [isLoggedIn, setIsLoggedIn] = useState(false)
//     const [token, setToken] = useState<string | undefined>()

//     useEffect(() => {
//         const storedToken = localStorage.getItem("token")
//         if (storedToken) {
//             setToken(storedToken)
//             setIsLoggedIn(true)
//         }
//     }, [])

//     const login = (user: any, token: string) => {
//         localStorage.setItem("token", token)
//         localStorage.setItem("user", JSON.stringify(user))
//         setToken(token)
//         setIsLoggedIn(true)
//     }

//     const logout = () => {
//         localStorage.removeItem("token")
//         localStorage.removeItem("user")
//         setToken(undefined)
//         setIsLoggedIn(false)
//     }

//     return (
//         <AuthContext.Provider value= {{ isLoggedIn, login, logout, token }
// }>
//     { children }
//     </AuthContext.Provider>
//   )
// }

// export const useAuth = () => useContext(AuthContext)
