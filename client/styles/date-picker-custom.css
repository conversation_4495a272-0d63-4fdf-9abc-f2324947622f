/* Custom styles for date picker in modal */
.modal-date-picker input {
  height: 40px !important;
  font-size: 14px !important;
  padding: 8px 12px !important;
  line-height: 24px !important;
}

.modal-date-picker button {
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* Ensure the calendar icon is properly positioned */
.modal-date-picker .thai-date-picker-wrapper button {
  right: 10px !important;
}

/* Ensure the clear button is properly positioned */
.modal-date-picker .thai-date-picker-wrapper button[aria-label="Clear date"] {
  right: 36px !important;
}

/* Style the calendar popup to match the form */
.thai-datepicker-popper {
  font-family: var(--font-sarabun) !important;
}

/* Match the border radius and border color with other form elements */
.modal-date-picker input {
  border-radius: 0.375rem !important;
  border-color: hsl(var(--input)) !important;
}

/* Match focus state with other form elements */
.modal-date-picker input:focus {
  outline: none !important;
  border-color: hsl(var(--ring)) !important;
  box-shadow: 0 0 0 1px hsl(var(--ring)) !important;
}
