import type React from "react"
import "./globals.css"
import type { Metadata } from "next"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { sarabun, inter } from "@/lib/fonts"


// Add Sarabun font with Thai subset

export const metadata: Metadata = {
  title: "SP Autocar - Vehicle Management System",
  description: "Second hand vehicle management system",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`overflow-hidden ${sarabun.variable}`}>
      <body className={`${inter.className} overflow-hidden h-screen`}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}

// client/app/layout.tsx
// import type { Metadata } from 'next'

// export const metadata: Metadata = {
//   title: 'Test App',
//   description: 'This is a test layout for Vercel deployment.',
// }

// export default function RootLayout({
//   children,
// }: {
//   children: React.ReactNode
// }) {
//   return (
//     <html lang="en">
//       <body>{children}</body>
//     </html>
//   )
// }


import './globals.css'