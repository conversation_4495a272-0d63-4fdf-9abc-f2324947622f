"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Head<PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { PaperCard } from "@/components/papers/paper-card"
import { FileText, Car, Receipt, DollarSign, Wrench } from "lucide-react"

export default function PapersPage() {
  const router = useRouter()

  // Check if user is logged in
  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn")
    if (!isLoggedIn) {
      router.push("/login")
    }
  }, [router])

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Papers Generator</h1>
            <p className="text-muted-foreground">Generate transport and transaction documents</p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <PaperCard
              title="Sales Commission"
              description="Calculate and generate sales commission reports"
              icon={<DollarSign className="h-10 w-10 text-purple-600" />}
              buttonText="Generate Commission"
              href="/papers/sales-commission/report"
              iconBgColor="bg-purple-50"
            />

            <PaperCard
              title="Monthly Tax Summation"
              description="Generate monthly tax summary reports"
              icon={<FileText className="h-10 w-10 text-amber-600" />}
              buttonText="Generate Report"
              href="/papers/monthly-tax"
              iconBgColor="bg-amber-50"
            />

            <PaperCard
              title="Tax Generator"
              description="Generate tax documents for vehicle transactions"
              icon={<Receipt className="h-10 w-10 text-primary" />}
              buttonText="Generate Tax Document"
              href="/papers/tax-generator"
              iconBgColor="bg-blue-50"
            />

            <PaperCard
              title="Daily Auction Summary"
              description="Create daily summary reports for auction activities"
              icon={<Car className="h-10 w-10 text-green-600" />}
              buttonText="Create Summary"
              href="/papers/daily-auction"
              iconBgColor="bg-green-50"
            />

            <PaperCard
              title="Job Order Generator"
              description="Create and manage vehicle repair job orders"
              icon={<Wrench className="h-10 w-10 text-blue-600" />}
              buttonText="Create Job Order"
              href="/papers/job-order"
              iconBgColor="bg-blue-50"
            />
          </div>
        </main>
      </div>
    </div>
  )
}
