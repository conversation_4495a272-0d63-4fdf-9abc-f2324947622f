"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, FileDown } from "lucide-react"
import Link from "next/link"
import { toast } from "@/components/ui/use-toast"
import { mockCars } from "@/data/mock-data"

export default function TaxGeneratorPage() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [vehicleId, setVehicleId] = useState("")
  const [date, setDate] = useState("")
  const [carTaxInvoiceNumber, setCarTaxInvoiceNumber] = useState("")
  const [commissionTaxInvoiceNumber, setCommissionTaxInvoiceNumber] = useState("")

  const handleGenerate = () => {
    setIsGenerating(true)

    // Validate vehicle ID
    if (!vehicleId) {
      toast({
        title: "Error",
        description: "Please enter a vehicle ID or index number",
        variant: "destructive",
      })
      setIsGenerating(false)
      return
    }

    // Find the car in mock data
    const car = mockCars.find((c) => c.stockInfo.car_id === vehicleId || c.stockInfo.index_number === vehicleId)

    if (!car) {
      toast({
        title: "Error",
        description: "Vehicle not found",
        variant: "destructive",
      })
      setIsGenerating(false)
      return
    }

    // Check if car status is valid for tax generation
    const validStatuses = ["finance_request", "finance_done", "sold"]
    if (!validStatuses.includes(car.stockInfo.car_status)) {
      toast({
        title: "Tax Information Not Found",
        description:
          "Tax information is only available for vehicles with finance request, finance done, or sold status.",
        variant: "destructive",
      })
      setIsGenerating(false)
      return
    }

    // Simulate document generation
    setTimeout(() => {
      setIsGenerating(false)
      toast({
        title: "Success",
        description: "Tax document generated successfully",
      })
    }, 2000)
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/papers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Tax Generator</h1>
              <p className="text-muted-foreground">Generate tax documents for vehicle transactions</p>
            </div>
          </div>

          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Generate Tax Document</CardTitle>
              <CardDescription>Fill in the details to generate a tax document</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="vehicle-id">Vehicle ID</Label>
                <Input
                  id="vehicle-id"
                  placeholder="Enter vehicle ID or index number"
                  value={vehicleId}
                  onChange={(e) => setVehicleId(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input id="date" type="date" value={date} onChange={(e) => setDate(e.target.value)} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="car-tax-invoice-number">Car Tax Invoice Number</Label>
                <Input
                  id="car-tax-invoice-number"
                  placeholder="Enter car tax invoice number"
                  value={carTaxInvoiceNumber}
                  onChange={(e) => setCarTaxInvoiceNumber(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="commission-tax-invoice-number">Commission Tax Invoice Number</Label>
                <Input
                  id="commission-tax-invoice-number"
                  placeholder="Enter commission tax invoice number"
                  value={commissionTaxInvoiceNumber}
                  onChange={(e) => setCommissionTaxInvoiceNumber(e.target.value)}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full" onClick={handleGenerate} disabled={isGenerating}>
                {isGenerating ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Generating...
                  </>
                ) : (
                  <>
                    <FileDown className="mr-2 h-4 w-4" />
                    Generate Document
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </main>
      </div>
    </div>
  )
}
