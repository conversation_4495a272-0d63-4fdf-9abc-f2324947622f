// Tax data interfaces
export interface PurchaseTaxData {
  id: string
  indexNumber: string
  carStatus: string
  purchase_date: string
  brand: string
  model: string
  color: string
  year: number
  old_license_plate: string
  vat_percent: number
  purchase_price: number
  purchase_vat_percent: number
  operation_cost_incl_vat: number
  transport_1_auction_lot: number
  initial_check: number
  tax_insurance_cost_zero: number
  other_costs_seven: number
  five_three_tax_percentage: number
  total_purchase_cost: number,
  tank_number: string
  auction_location: string
}

export interface SelloutTaxData {
  id: string
  indexNumber: string
  sale_date: string
  finance_received_date: string
  car_tax_invoice_date: string
  car_tax_invoice_number: string
  car_amount: number
  car_vat_amount: number
  commission_tax_invoice_date: string
  commission_tax_invoice_number: string
  car_commission_amount: number
  input_vat_commission: number
  withholding_tax: number
  vat_percent: number
  tank_number: string
}

// Purchase tax totals (15 properties)
export interface PurchaseTaxTotals {
  // Original 9 properties
  total_purchase_price: number
  total_purchase_vat_percent: number
  total_operation_cost: number
  total_transport_1_auction_lot: number
  total_initial_check: number
  total_tax_insurance_cost_zero: number
  total_other_costs_seven: number
  total_five_three_tax_percentage: number
  total_total_purchase_cost: number

  // New 6 properties for enhanced VAT calculations
  total_operation_cost_vat: number
  total_transport_1_auction_lot_vat: number
  total_initial_check_vat: number
  total_other_costs_seven_vat: number
  total_purchase_cost_without_vat: number
  hightlight_net_vat: number
}

// Purchase tax calculated values
export interface PurchaseCalculated {
  vat_operation: number
  vat_transport_1: number
  vat_initial_check: number
  vat_other_costs_seven: number
  vat_purchase: number
  net_vat_purchase: number
}

// Sellout tax totals
export interface SelloutTaxTotals {
  total_car_amount: number
  total_vat_on_car_amount: number
  total_commission_of_car: number
  total_input_vat_comm: number
}

// Sellout tax calculated values
export interface SelloutCalculated {
  total_purchase_vat: number
  net_purchase_vat: number
}
