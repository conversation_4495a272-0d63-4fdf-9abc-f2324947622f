/**
 * Utility functions for the monthly tax page
 */

/**
 * Format a date string for display in the monthly tax page
 * @param dateString Date string to format
 * @returns Formatted date string (DD/MM/YYYY) or empty string if invalid
 */
export function formatTaxDate(dateString: string | null | undefined): string {
  if (!dateString) return "";
  
  try {
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "";
    }
    
    // Format as DD/MM/YYYY
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error("Error formatting tax date:", error);
    return "";
  }
}

/**
 * Calculate purchase tax totals with enhanced VAT calculations
 * @param data Purchase tax data array
 * @returns Object with calculated totals (15 properties)
 */
export function calculatePurchaseTotals(data: any[]) {
  // Calculate basic totals
  const total_purchase_price = data.reduce((sum, item) => sum + (item.purchase_price || 0), 0);
  const total_purchase_vat_percent = data.reduce((sum, item) => sum + (item.purchase_vat_percent || 0), 0);
  const total_operation_cost = data.reduce((sum, item) => sum + (item.operation_cost_incl_vat || 0), 0);
  const total_transport_1_auction_lot = data.reduce((sum, item) => sum + (item.transport_1_auction_lot || 0), 0);
  const total_initial_check = data.reduce((sum, item) => sum + (item.initial_check || 0), 0);
  const total_tax_insurance_cost_zero = data.reduce((sum, item) => sum + (item.tax_insurance_cost_zero || 0), 0);
  const total_other_costs_seven = data.reduce((sum, item) => sum + (item.other_costs_seven || 0), 0);
  const total_five_three_tax_percentage = data.reduce((sum, item) => sum + (item.five_three_tax_percentage || 0), 0);
  const total_total_purchase_cost = data.reduce((sum, item) => sum + (item.total_purchase_cost || 0), 0);

  // Calculate VAT components using 7/107 formula
  const total_operation_cost_vat = total_operation_cost * 7 / 107;
  const total_transport_1_auction_lot_vat = total_transport_1_auction_lot * 7 / 107;
  const total_initial_check_vat = total_initial_check * 7 / 107;
  const total_other_costs_seven_vat = total_other_costs_seven * 7 / 107;

  // Calculate purchase cost without VAT
  const total_purchase_cost_without_vat =
    total_purchase_price +
    total_purchase_vat_percent +
    total_operation_cost +
    total_transport_1_auction_lot +
    total_initial_check +
    total_other_costs_seven;

  // Calculate highlighted net VAT
  const hightlight_net_vat =
    total_purchase_vat_percent +
    total_operation_cost_vat +
    total_transport_1_auction_lot_vat +
    total_initial_check_vat +
    total_other_costs_seven_vat;

  return {
    // Original 9 properties
    total_purchase_price,
    total_purchase_vat_percent,
    total_operation_cost,
    total_transport_1_auction_lot,
    total_initial_check,
    total_tax_insurance_cost_zero,
    total_other_costs_seven,
    total_five_three_tax_percentage,
    total_total_purchase_cost,

    // New 6 properties for enhanced calculations
    total_operation_cost_vat,
    total_transport_1_auction_lot_vat,
    total_initial_check_vat,
    total_other_costs_seven_vat,
    total_purchase_cost_without_vat,
    hightlight_net_vat,
  };
}

/**
 * Calculate purchase tax derived values
 * @param totals Purchase tax totals
 * @returns Object with calculated values
 */
export function calculatePurchaseDerived(totals: any) {
  return {
    vat_operation: totals.total_operation_cost * 0.07,
    vat_transport_1: totals.total_transport_1_auction_lot * 0.07,
    vat_initial_check: totals.total_initial_check * 0.07,
    vat_other_costs_seven: totals.total_other_costs_seven * 0.07,
    vat_purchase: totals.total_purchase_vat_percent,
    net_vat_purchase: totals.total_purchase_vat_percent - 
      (totals.total_operation_cost * 0.07) - 
      (totals.total_transport_1_auction_lot * 0.07) - 
      (totals.total_initial_check * 0.07) - 
      (totals.total_other_costs_seven * 0.07),
  };
}

/**
 * Extract month from date string (YYYY-MM-DD format)
 * @param dateString Date string in YYYY-MM-DD format
 * @returns Month string in YYYY-MM format or empty string if invalid
 */
function extractMonth(dateString: string | null | undefined): string {
  if (!dateString) return "";

  try {
    // Extract YYYY-MM from YYYY-MM-DD
    const parts = dateString.split('-');
    if (parts.length >= 2) {
      return `${parts[0]}-${parts[1]}`;
    }
    return "";
  } catch (error) {
    console.error("Error extracting month from date:", error);
    return "";
  }
}

/**
 * Calculate sellout tax totals with complex month-based logic
 * @param data Sellout tax data array
 * @param selectedMonth Selected month (1-12)
 * @param selectedYear Selected year (Buddhist format)
 * @returns Object with calculated totals
 */
export function calculateSelloutTotals(data: any[], selectedMonth: number, selectedYear: number) {
  // Convert Buddhist year to Gregorian for comparison
  const gregorianYear = selectedYear - 543;
  const targetMonth = `${gregorianYear}-${selectedMonth.toString().padStart(2, '0')}`;

  console.log(`[calculateSelloutTotals] Target month: ${targetMonth}, Total records: ${data.length}`);

  // Group data by car_tax_invoice_date month
  const groupedByMonth: Record<string, any[]> = {};

  data.forEach(item => {
    const month = extractMonth(item.car_tax_invoice_date);
    if (month) {
      if (!groupedByMonth[month]) {
        groupedByMonth[month] = [];
      }
      groupedByMonth[month].push(item);
    }
  });

  console.log(`[calculateSelloutTotals] Grouped by months:`, Object.keys(groupedByMonth));

  // Calculate car amount and VAT with month-based logic
  const selectedMonthData = groupedByMonth[targetMonth] || [];
  const otherMonthsData = Object.keys(groupedByMonth)
    .filter(month => month !== targetMonth)
    .flatMap(month => groupedByMonth[month]);

  const selectedMonthCarAmount = selectedMonthData.reduce((sum, item) => sum + (item.car_amount || 0), 0);
  const otherMonthsCarAmount = otherMonthsData.reduce((sum, item) => sum + (item.car_amount || 0), 0);
  const total_car_amount = selectedMonthCarAmount - otherMonthsCarAmount;

  const selectedMonthVatAmount = selectedMonthData.reduce((sum, item) => sum + (item.car_vat_amount || 0), 0);
  const otherMonthsVatAmount = otherMonthsData.reduce((sum, item) => sum + (item.car_vat_amount || 0), 0);
  const total_vat_on_car_amount = selectedMonthVatAmount - otherMonthsVatAmount;

  // Calculate commission and input VAT using all data
  const total_commission_of_car = data.reduce((sum, item) => sum + (item.car_commission_amount || 0), 0);
  const total_input_vat_comm = data.reduce((sum, item) => sum + (item.input_vat_commission || 0), 0);
  const total_withholding_tax = data.reduce((sum, item) => sum + (item.withholding_tax || 0), 0);

  console.log(`[calculateSelloutTotals] Calculations:`, {
    selectedMonthCarAmount,
    otherMonthsCarAmount,
    total_car_amount,
    selectedMonthVatAmount,
    otherMonthsVatAmount,
    total_vat_on_car_amount,
    total_commission_of_car,
    total_input_vat_comm,
    total_withholding_tax
  });

  return {
    total_car_amount,
    total_vat_on_car_amount,
    total_commission_of_car,
    total_input_vat_comm,
    total_withholding_tax
  };
}

/**
 * Calculate sellout tax derived values with new highlight_net_vat formula
 * @param totals Sellout tax totals
 * @returns Object with calculated values
 */
export function calculateSelloutDerived(totals: any) {
  const highlight_net_vat = totals.total_vat_on_car_amount + totals.total_input_vat_comm;

  return {
    total_purchase_vat: totals.total_vat_on_car_amount,
    net_purchase_vat: totals.total_vat_on_car_amount - totals.total_input_vat_comm,
    highlight_net_vat: highlight_net_vat
  };
}
