"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, ArrowRight, Calendar, FileDown, FileSpreadsheet } from "lucide-react"
import Link from "next/link"
import { formatCurrency } from "@/lib/utils"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  formatTaxDate,
  calculatePurchaseTotals,
  calculateSelloutTotals,
  calculateSelloutDerived,
} from "./utils"

import type { PurchaseTaxData, SelloutTaxData } from "./types"
import { MONTHLY_TAX_API } from "../../services/monthlyTaxService"

export default function MonthlyTaxPage() {
  const [activeTab, setActiveTab] = useState("purchase")
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1) // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear() + 543) // Current year in Buddhist format
  const [isLoading, setIsLoading] = useState(false)
  const [purchaseTaxData, setPurchaseTaxData] = useState<PurchaseTaxData[]>([])
  const [selloutTaxData, setSelloutTaxData] = useState<SelloutTaxData[]>([])

  // Generate month options in Thai
  const months = [
    { value: 1, label: "มกราคม" },
    { value: 2, label: "กุมภาพันธ์" },
    { value: 3, label: "มีนาคม" },
    { value: 4, label: "เมษายน" },
    { value: 5, label: "พฤษภาคม" },
    { value: 6, label: "มิถุนายน" },
    { value: 7, label: "กรกฎาคม" },
    { value: 8, label: "สิงหาคม" },
    { value: 9, label: "กันยายน" },
    { value: 10, label: "ตุลาคม" },
    { value: 11, label: "พฤศจิกายน" },
    { value: 12, label: "ธันวาคม" },
  ]

  // Generate year options (last 5 years) in Buddhist format
  const currentYear = new Date().getFullYear() + 543 // Convert to Buddhist year
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  // Fetch tax data
  const fetchTaxData = async () => {
    setIsLoading(true)
    try {
      // Get auth token from localStorage
      const authToken = localStorage.getItem("token") || ""

      // Convert Buddhist year to Gregorian year for the API
      const gregorianYear = selectedYear - 543
      console.log("Buddhist Year:", selectedYear, "Gregorian Year:", gregorianYear)

      const response = await MONTHLY_TAX_API.GET_MONTHLY_TAX(selectedMonth, selectedYear, authToken)

      setPurchaseTaxData(response.purchaseData)
      setSelloutTaxData(response.selloutData)
    } catch (error) {
      console.error("Error fetching tax data:", error)
      // Set empty arrays on error
      setPurchaseTaxData([])
      setSelloutTaxData([])
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data on initial load and when month/year changes
  useEffect(() => {
    fetchTaxData()
  }, [selectedMonth, selectedYear])

  // Use all data without search filtering
  const filteredPurchaseData = purchaseTaxData
  const filteredSelloutData = selloutTaxData
  
  const purchaseTotals = calculatePurchaseTotals(filteredPurchaseData)
  const selloutTotals = calculateSelloutTotals(filteredSelloutData)
  const selloutCalculated = calculateSelloutDerived(selloutTotals)

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex flex-1 flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-hidden p-4">
          <div className="mb-4 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/papers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-xl font-bold">Monthly Tax Summation</h1>
              <p className="text-muted-foreground text-sm">Generate monthly tax summary reports</p>
            </div>
          </div>

          <div className="flex flex-col h-[calc(100vh-120px)] overflow-hidden">
            <Card className="flex-1 overflow-hidden flex flex-col">
              <CardHeader className="py-3 px-4 border-b flex-shrink-0">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-lg">Monthly Tax Report</CardTitle>
                    <CardDescription className="text-xs">Tax details for the selected period</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">
                      {months.find((m) => m.value === selectedMonth)?.label} {selectedYear}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0 flex-1 overflow-hidden flex flex-col">
                {/* Date selection controls and Tab switching */}
                <div className="flex flex-wrap gap-2 p-3 border-b bg-gray-50 flex-shrink-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Month:</span>
                    <Select
                      value={selectedMonth.toString()}
                      onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                    >
                      <SelectTrigger className="w-[120px] h-8 text-xs">
                        <SelectValue placeholder="Select month" />
                      </SelectTrigger>
                      <SelectContent>
                        {months.map((month) => (
                          <SelectItem key={month.value} value={month.value.toString()}>
                            {month.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Year (พ.ศ.):</span>
                    <Select
                      value={selectedYear.toString()}
                      onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                    >
                      <SelectTrigger className="w-[100px] h-8 text-xs">
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                      <SelectContent>
                        {years.map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button variant="outline" onClick={fetchTaxData} className="flex items-center gap-1 h-8 text-xs">
                    <span>Enter</span>
                    <ArrowRight className="h-3 w-3" />
                  </Button>

                  {/* Tax Type Tabs - Moved here from below */}
                  <div className="ml-auto flex items-center gap-2">
                    <div className="flex h-8 items-center rounded-md bg-muted p-1 text-muted-foreground">
                      <button
                        className={`flex-1 items-center justify-center whitespace-nowrap rounded-sm px-3 py-1 text-xs font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                          activeTab === "purchase"
                            ? "bg-background text-foreground shadow-sm"
                            : "hover:bg-background/50"
                        }`}
                        onClick={() => setActiveTab("purchase")}
                      >
                        Purchase Tax
                      </button>
                      <button
                        className={`flex-1 items-center justify-center whitespace-nowrap rounded-sm px-3 py-1 text-xs font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                          activeTab === "sellout" ? "bg-background text-foreground shadow-sm" : "hover:bg-background/50"
                        }`}
                        onClick={() => setActiveTab("sellout")}
                      >
                        Sellout Tax
                      </button>
                    </div>

                    <Button
                      variant="outline"
                      disabled={activeTab === "purchase" ? purchaseTaxData.length === 0 : selloutTaxData.length === 0}
                      className="h-8 text-xs"
                    >
                      <FileSpreadsheet className="h-3 w-3 mr-1" />
                      Export Excel
                    </Button>

                    <Button
                      variant="outline"
                      disabled={activeTab === "purchase" ? purchaseTaxData.length === 0 : selloutTaxData.length === 0}
                      className="h-8 text-xs"
                    >
                      <FileDown className="h-3 w-3 mr-1" />
                      Export PDF
                    </Button>
                  </div>
                </div>

                {/* Content Area */}
                <div className="flex-1 overflow-hidden px-4 py-2">
                  {activeTab === "purchase" && (
                    <div className="flex flex-col h-full relative">
                      {/* Enhanced Table Container for Purchase - Moved Up */}
                      <div className="flex-1 overflow-hidden border rounded-md mb-2">
                        {isLoading ? (
                          <div className="h-full flex items-center justify-center">
                            <div className="animate-pulse text-muted-foreground">Loading tax data...</div>
                          </div>
                        ) : filteredPurchaseData.length === 0 ? (
                          <div className="h-full flex items-center justify-center">
                            <div className="text-muted-foreground">No purchase tax data available for this period.</div>
                          </div>
                        ) : (
                          <div className="h-full overflow-auto">
                            <div className="min-w-max">
                              <Table className="border-collapse text-xs">
                                <TableHeader className="sticky top-0 bg-slate-700 z-10">
                                  <TableRow>
                                    <TableHead className="w-[50px] border text-white text-xs p-1 text-center">
                                      ลำดับ
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      วันที่ซื้อ
                                    </TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">ยี่ห้อ</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">รุ่นรถ</TableHead>
                                    <TableHead className="w-[70px] border text-white text-xs p-1">สี</TableHead>
                                    <TableHead className="w-[60px] border text-white text-xs p-1">ปี</TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      ทะเบียนเดิม
                                    </TableHead>
                                    <TableHead className="w-[60px] border text-white text-xs p-1">VAT %</TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      ราคาซื้อเข้า
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      VAT(%) ซื้อ
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      ค่าดำเนินการรวม VAT
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      ขนย้าย ลานประมูล
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      เช็คต้น
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      ภาษี พรบ
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      คชจ อื่นๆ
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">ภงด. 53</TableHead>
                                    <TableHead className="w-[120px] border text-white text-xs p-1">
                                      รวมซื้อเข้า
                                    </TableHead>
                                    <TableHead className="w-[120px] border text-white text-xs p-1">
                                      เลขตัวถัง 
                                    </TableHead><TableHead className="w-[120px] border text-white text-xs p-1">
                                      ลานประมูล
                                    </TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {filteredPurchaseData.map((item, index) => (
                                    <TableRow key={item.id} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                                      <TableCell className="border text-xs p-1 text-center font-medium">
                                        {index + 1}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatTaxDate(item.purchase_date)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.brand}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.model}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.color}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.year}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.old_license_plate}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.vat_percent}%</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.purchase_price)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.purchase_vat_percent)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.operation_cost_incl_vat)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.transport_1_auction_lot)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.initial_check)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.tax_insurance_cost_zero)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.other_costs_seven)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.five_three_tax_percentage)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center font-medium">
                                        {formatCurrency(item.total_purchase_cost)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.tank_number}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.auction_location}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* 3-Section Grouped Tax Summary for Purchase */}
                      <div className="sticky bottom-0 bg-white border-t-2 border-slate-300 shadow-lg z-10">
                        <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-2">
                          <h3 className="text-xs font-medium mb-2 text-center text-slate-700">สรุปภาษี (Tax Summary)</h3>

                          <div className="bg-white p-2 rounded border shadow-sm">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">

                              {/* Group 1: Total Costs (9 items in 3x3 grid) */}
                              <div className="bg-slate-50 p-2 rounded border">
                                <h4 className="text-[9px] font-semibold text-slate-600 mb-1 text-center">รวมยอดต้นทุน</h4>
                                <div className="grid grid-cols-3 gap-1">
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">ราคาซื้อเข้า</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(purchaseTotals.total_purchase_price)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">VAT (%)ซื้อ</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(purchaseTotals.total_purchase_vat_percent)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">ค่าดำเนินการรวม VAT</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(purchaseTotals.total_operation_cost)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">ขนย้ายลานประมูล</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(purchaseTotals.total_transport_1_auction_lot)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">เช็คต้น</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(purchaseTotals.total_initial_check)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">ภาษี พรบ</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(purchaseTotals.total_tax_insurance_cost_zero)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">ค่าใช้จ่ายอื่นๆ</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(purchaseTotals.total_other_costs_seven)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">ภงด.53</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(purchaseTotals.total_five_three_tax_percentage)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1 bg-orange-100 rounded border border-orange-300">
                                    <div className="text-[8px] text-orange-600 mb-0.5 font-medium">รวมซื้อเข้า</div>
                                    <div className="text-[9px] font-bold text-orange-700">
                                      {formatCurrency(purchaseTotals.total_total_purchase_cost)}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Group 2: VAT Breakdown (5 items in flexible grid) */}
                              <div className="bg-blue-50 p-2 rounded border border-blue-200">
                                <h4 className="text-[9px] font-semibold text-blue-700 mb-1 text-center">รายละเอียด VAT</h4>
                                <div className="grid grid-cols-2 gap-1">
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-blue-600 mb-0.5 font-medium">VAT ดำเนินการ</div>
                                    <div className="text-[9px] font-bold text-blue-700">
                                      {formatCurrency(purchaseTotals.total_operation_cost_vat)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-blue-600 mb-0.5 font-medium">VAT ขนย้ายลานประ</div>
                                    <div className="text-[9px] font-bold text-blue-700">
                                      {formatCurrency(purchaseTotals.total_transport_1_auction_lot_vat)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-blue-600 mb-0.5 font-medium">VAT เช็คต้น</div>
                                    <div className="text-[9px] font-bold text-blue-700">
                                      {formatCurrency(purchaseTotals.total_initial_check_vat)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-blue-600 mb-0.5 font-medium">VAT คชจ อื่นๆ</div>
                                    <div className="text-[9px] font-bold text-blue-700">
                                      {formatCurrency(purchaseTotals.total_other_costs_seven_vat)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1 bg-indigo-100 rounded border border-indigo-300 col-span-2">
                                    <div className="text-[8px] text-indigo-600 mb-0.5 font-medium">ซื้อเข้ารวม-ไม่รวม ภาษี พรบ</div>
                                    <div className="text-[9px] font-bold text-indigo-700">
                                      {formatCurrency(purchaseTotals.total_purchase_cost_without_vat)}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Group 3: Main Result (1 dominant item) */}
                              <div className="bg-gradient-to-br from-green-100 to-emerald-100 p-3 rounded border-2 border-green-300">
                                <h4 className="text-[9px] font-semibold text-green-700 mb-2 text-center">ผลรวมสุดท้าย</h4>
                                <div className="bg-white p-3 rounded border-2 border-green-400 shadow-inner">
                                  <div className="text-center">
                                    <div className="text-[8px] text-green-600 font-medium mb-1">VAT ซื้อรวม</div>
                                    <div className="text-lg font-bold text-green-800 mb-1">
                                      {formatCurrency(purchaseTotals.hightlight_net_vat)}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === "sellout" && (
                    <div className="flex flex-col h-full relative">
                      {/* Enhanced Table Container for Sellout - Moved Up */}
                      <div className="flex-1 overflow-hidden border rounded-md mb-2">
                        {isLoading ? (
                          <div className="h-full flex items-center justify-center">
                            <div className="animate-pulse text-muted-foreground">Loading tax data...</div>
                          </div>
                        ) : filteredSelloutData.length === 0 ? (
                          <div className="h-full flex items-center justify-center">
                            <div className="text-muted-foreground">No tax invoice data available for this period.</div>
                          </div>
                        ) : (
                          <div className="h-full overflow-auto">
                            <div className="min-w-max">
                              <Table className="border-collapse text-xs">
                                <TableHeader className="sticky top-0 bg-slate-700 z-10">
                                  <TableRow>
                                    <TableHead className="w-[50px] border text-white text-xs p-1 text-center">
                                      ลำดับ
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">วันที่ขาย</TableHead>
                                    {/* <TableHead className="w-[100px] border text-white text-xs p-1">
                                      วันที่ไฟแนนซ์
                                    </TableHead> */}
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      วันออกภาษีขาย-Car
                                    </TableHead>
                                    <TableHead className="w-[120px] border text-white text-xs p-1">
                                      เลขที่/เล่มที่ ภาษี Car
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      CAR AMOUNT
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">VAT ON CAR AMOUNT</TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                    วันออกภาษีขาย-Comm
                                    </TableHead>
                                    <TableHead className="w-[120px] border text-white text-xs p-1">
                                      เลขที่/เล่มที่ ภาษี Comm
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      COMMISSION OF CAR
                                    </TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">Input-VAT-comm.</TableHead>
                                    <TableHead className="w-[100px] border text-white text-xs p-1">
                                      WITHHOLDING TAX.
                                    </TableHead>
                                    <TableHead className="w-[60px] border text-white text-xs p-1">BANK</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">ยี่ห้อ</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">รุ่นรถ</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">สี</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">ปี</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">ทะเบียนเดิม</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">ทะเบียนใหม่</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">VAT (%)</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">เลขตัวถัง</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">เลขเครื่องยนต์</TableHead>
                                    <TableHead className="w-[80px] border text-white text-xs p-1">ลูกค้า</TableHead>

                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {filteredSelloutData.map((item, index) => (
                                    <TableRow key={item.id} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                                      <TableCell className="border text-xs p-1 text-center font-medium">
                                        {index + 1}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatTaxDate(item.sale_date)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatTaxDate(item.car_tax_invoice_date)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {item.car_tax_invoice_number}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.car_amount)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.car_vat_amount)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatTaxDate(item.commission_tax_invoice_date)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {item.commission_tax_invoice_number}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.car_commission_amount)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.input_vat_commission)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">
                                        {formatCurrency(item.withholding_tax)}
                                      </TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.bank}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.brand}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.model}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.color}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.year}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.old_license_plate}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.new_license_plate}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.vat_percent}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.tank_number}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.engine_number}</TableCell>
                                      <TableCell className="border text-xs p-1 text-center">{item.customer_address}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* 3-Section Grouped Tax Summary for Sellout */}
                      <div className="sticky bottom-0 bg-white border-t-2 border-slate-300 shadow-lg z-10">
                        <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-2">
                          <h3 className="text-xs font-medium mb-2 text-center text-slate-700">สรุปภาษีขาย (Sellout Tax Summary)</h3>

                          <div className="bg-white p-2 rounded border shadow-sm">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">

                              {/* Group 1: Sellout Amounts (4 items in 2x2 grid) */}
                              <div className="bg-slate-50 p-2 rounded border">
                                <h4 className="text-[9px] font-semibold text-slate-600 mb-1 text-center">ยอดขายรวม</h4>
                                <div className="grid grid-cols-2 gap-1">
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">ยอดขายรถ</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(selloutTotals.total_car_amount)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">VAT ขายรถ</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(selloutTotals.total_vat_on_car_amount)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">ค่าคอมมิชชั่น</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(selloutTotals.total_commission_of_car)}
                                    </div>
                                  </div>
                                  <div className="text-center p-1">
                                    <div className="text-[8px] text-slate-600 mb-0.5">VAT นำเข้า</div>
                                    <div className="text-[9px] font-bold text-slate-800">
                                      {formatCurrency(selloutTotals.total_input_vat_comm)}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Group 2: VAT Calculations (1 item) */}
                              <div className="bg-blue-50 p-2 rounded border border-blue-200">
                                <h4 className="text-[9px] font-semibold text-blue-700 mb-1 text-center">การคำนวณ VAT</h4>
                                <div className="text-center p-1 bg-indigo-100 rounded border border-indigo-300">
                                  <div className="text-[8px] text-indigo-600 mb-0.5 font-medium">VAT รวม</div>
                                  <div className="text-[9px] font-bold text-indigo-700">
                                    {formatCurrency(selloutCalculated.total_purchase_vat)}
                                  </div>
                                </div>
                              </div>

                              {/* Group 3: Main Result (1 dominant item) */}
                              <div className="bg-gradient-to-br from-green-100 to-emerald-100 p-3 rounded border-2 border-green-300">
                                <h4 className="text-[9px] font-semibold text-green-700 mb-2 text-center">ผลรวมสุดท้าย</h4>
                                <div className="bg-white p-3 rounded border-2 border-green-400 shadow-inner">
                                  <div className="text-center">
                                    <div className="text-[8px] text-green-600 font-medium mb-1">VAT ขายสุทธิ</div>
                                    <div className="text-lg font-bold text-green-800 mb-1">
                                      {formatCurrency(selloutCalculated.net_purchase_vat)}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
