"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, ArrowRight, Calendar, FileDown, FileSpreadsheet } from "lucide-react"
import Link from "next/link"
import { ScrollArea } from "@/components/ui/scroll-area"
import { formatCurrency } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"

// Mock data for commission report
interface CommissionData {
  id: string
  carNumber: string
  brand: string
  model: string
  saleDate: string
  sellingPrice: number
  commissionAmount: number
  commissionType: string
  salesperson: string
  nominee?: string
}

// Generate mock commission data
const generateMockCommissionData = (): CommissionData[] => {
  const salespeople = ["John Smith", "Jane Doe", "Robert Johnson", "Emily Davis"]
  const brands = ["Toyota", "Honda", "Nissan", "Mazda", "Mitsubishi"]
  const models = ["Camry", "Civic", "Altima", "3", "Lancer"]
  const commissionTypes = ["Sales", "Manager", "Agent"]

  return Array.from({ length: 20 }, (_, i) => {
    const salesperson = salespeople[Math.floor(Math.random() * salespeople.length)]
    const brand = brands[Math.floor(Math.random() * brands.length)]
    const model = models[Math.floor(Math.random() * models.length)]
    const sellingPrice = 300000 + Math.floor(Math.random() * 700000)
    const commissionType = commissionTypes[Math.floor(Math.random() * commissionTypes.length)]
    const commissionAmount =
      commissionType === "Sales"
        ? sellingPrice * 0.01
        : commissionType === "Manager"
          ? sellingPrice * 0.005
          : sellingPrice * 0.02

    return {
      id: `comm-${i + 1}`,
      carNumber: `SP${1000 + i}`,
      brand,
      model,
      saleDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)
        .toISOString()
        .split("T")[0],
      sellingPrice,
      commissionAmount,
      commissionType,
      salesperson,
      nominee: undefined,
    }
  })
}

export default function CommissionReportPage() {
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1) // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()) // Current year
  const [isLoading, setIsLoading] = useState(false)
  const [commissionData, setCommissionData] = useState<CommissionData[]>([])
  const [activeTab, setActiveTab] = useState("all")

  // Generate month options
  const months = [
    { value: 1, label: "January" },
    { value: 2, label: "February" },
    { value: 3, label: "March" },
    { value: 4, label: "April" },
    { value: 5, label: "May" },
    { value: 6, label: "June" },
    { value: 7, label: "July" },
    { value: 8, label: "August" },
    { value: 9, label: "September" },
    { value: 10, label: "October" },
    { value: 11, label: "November" },
    { value: 12, label: "December" },
  ]

  // Generate year options (last 5 years)
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  // Fetch commission data
  const fetchCommissionData = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      const data = generateMockCommissionData()
      setCommissionData(data)
      setIsLoading(false)
    }, 1000)
  }

  // Fetch data on initial load
  useEffect(() => {
    fetchCommissionData()
  }, [])

  // Get unique salespersons from data
  const salespersons = Array.from(new Set(commissionData.map((item) => item.salesperson)))

  // Filter data by salesperson
  const getFilteredData = (salesperson: string) => {
    if (salesperson === "all") {
      return commissionData
    }
    return commissionData.filter((item) => {
      // If viewing a nominee tab, show items where this person is the nominee
      if (salesperson !== item.salesperson && item.nominee === salesperson) {
        return true
      }
      // If viewing a salesperson tab, show items where this person is the salesperson and has no nominee
      return item.salesperson === salesperson && !item.nominee
    })
  }

  // Handle nominee change
  const handleNomineeChange = (itemId: string, nominee: string) => {
    setCommissionData((prevData) =>
      prevData.map((item) => {
        if (item.id === itemId) {
          return { ...item, nominee }
        }
        return item
      }),
    )

    toast({
      title: "Nominee Updated",
      description: `Commission has been assigned to ${nominee}`,
      duration: 3000,
    })
  }

  // Calculate totals for a salesperson
  const calculateTotals = (salesperson: string) => {
    const filteredData = getFilteredData(salesperson)
    const totalSales = filteredData.length
    const totalAmount = filteredData.reduce((sum, item) => sum + item.sellingPrice, 0)
    const totalCommission = filteredData.reduce((sum, item) => sum + item.commissionAmount, 0)

    return { totalSales, totalAmount, totalCommission }
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/papers/sales-commission">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Commission Report</h1>
              <p className="text-muted-foreground">Sales commission breakdown by salesperson</p>
            </div>
          </div>

          <Card className="max-w-full">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Sales Commission Report</CardTitle>
                  <CardDescription>Commission details for the selected period</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <span className="text-sm font-medium">
                    {months.find((m) => m.value === selectedMonth)?.label} {selectedYear}
                  </span>
                  {!isLoading && commissionData.length > 0 && (
                    <span className="ml-2 bg-primary text-white text-xs font-medium px-2 py-0.5 rounded-full">
                      {commissionData.length} Commissions
                    </span>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-4">
              {/* Date selection controls */}
              <div className="flex flex-wrap gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Month:</span>
                  <Select
                    value={selectedMonth.toString()}
                    onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Select month" />
                    </SelectTrigger>
                    <SelectContent>
                      {months.map((month) => (
                        <SelectItem key={month.value} value={month.value.toString()}>
                          {month.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Year:</span>
                  <Select
                    value={selectedYear.toString()}
                    onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                  >
                    <SelectTrigger className="w-[100px]">
                      <SelectValue placeholder="Select year" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Button variant="outline" onClick={fetchCommissionData} className="flex items-center gap-1">
                  <span>Enter</span>
                  <ArrowRight className="h-4 w-4" />
                </Button>

                <Button onClick={fetchCommissionData} className="ml-auto text-white" disabled={isLoading}>
                  {isLoading ? "Loading..." : "Generate Report"}
                </Button>

                <Button variant="outline" disabled={commissionData.length === 0}>
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  Export Excel
                </Button>

                <Button variant="outline" disabled={commissionData.length === 0}>
                  <FileDown className="h-4 w-4 mr-2" />
                  Export PDF
                </Button>
              </div>

              {isLoading ? (
                <div className="h-[400px] flex items-center justify-center">
                  <div className="animate-pulse text-muted-foreground">Loading commission data...</div>
                </div>
              ) : commissionData.length === 0 ? (
                <div className="h-[400px] flex items-center justify-center">
                  <div className="text-muted-foreground">No commission data available for this period.</div>
                </div>
              ) : (
                <div className="flex gap-4 h-[calc(100vh-350px)] overflow-hidden">
                  {/* Vertical tabs on the left */}
                  <Tabs
                    defaultValue="all"
                    value={activeTab}
                    onValueChange={setActiveTab}
                    orientation="vertical"
                    className="w-48 flex-shrink-0 h-full"
                  >
                    <TabsList className="flex flex-col h-full border border-gray-200 overflow-y-auto">
                      <TabsTrigger
                        value="all"
                        className="justify-start w-full text-left border-b border-gray-200 py-3 px-4 data-[state=active]:bg-primary-light data-[state=active]:text-white"
                      >
                        All Salespersons
                      </TabsTrigger>
                      {salespersons.map((person, index) => (
                        <TabsTrigger
                          key={person}
                          value={person}
                          className={`justify-start w-full text-left py-3 px-4 data-[state=active]:bg-primary-light data-[state=active]:text-white ${
                            index < salespersons.length - 1 ? "border-b border-gray-200" : ""
                          }`}
                        >
                          {person}
                        </TabsTrigger>
                      ))}
                    </TabsList>
                  </Tabs>

                  {/* Content area on the right */}
                  <div className="flex-1 overflow-hidden">
                    {activeTab === "all" && (
                      <CommissionSummary
                        data={commissionData}
                        salesperson="All Salespersons"
                        nominees={salespersons}
                        onNomineeChange={handleNomineeChange}
                        currentTab={activeTab}
                      />
                    )}
                    {salespersons.map(
                      (person) =>
                        activeTab === person && (
                          <CommissionSummary
                            key={person}
                            data={getFilteredData(person)}
                            salesperson={person}
                            nominees={salespersons.filter((p) => p !== person)}
                            onNomineeChange={handleNomineeChange}
                            currentTab={activeTab}
                          />
                        ),
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}

// Commission Summary Component
function CommissionSummary({
  data,
  salesperson,
  nominees,
  onNomineeChange,
  currentTab,
}: {
  data: CommissionData[]
  salesperson: string
  nominees: string[]
  onNomineeChange: (itemId: string, nominee: string) => void
  currentTab: string
}) {
  // Calculate totals
  const totalSales = data.length
  const totalAmount = data.reduce((sum, item) => sum + item.sellingPrice, 0)
  const totalCommission = data.reduce((sum, item) => sum + item.commissionAmount, 0)

  return (
    <div className="space-y-6">
      {/* Summary Cards with smaller text */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-4">
            <div className="text-xs text-muted-foreground">Total Sales</div>
            <div className="text-lg font-bold">{totalSales} vehicles</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="text-xs text-muted-foreground">Total Amount</div>
            <div className="text-lg font-bold">{formatCurrency(totalAmount)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="text-xs text-muted-foreground">Total Commission</div>
            <div className="text-lg font-bold text-primary">{formatCurrency(totalCommission)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Table */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base">Commission Details</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[calc(100vh-480px)]">
            <Table className="commission-table">
              <TableHeader className="sticky top-0 bg-primary-dark">
                <TableRow className="border-none">
                  <TableHead className="w-[10%] text-white">Car #</TableHead>
                  <TableHead className="w-[20%] text-white">Vehicle</TableHead>
                  <TableHead className="w-[15%] text-white">Sale Date</TableHead>
                  <TableHead className="w-[20%] text-white">Selling Price</TableHead>
                  <TableHead className="w-[20%] text-white text-right">Commission Amount</TableHead>
                  <TableHead className="w-[15%] text-white">Nominee</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.carNumber}</TableCell>
                    <TableCell>
                      {item.brand} {item.model}
                    </TableCell>
                    <TableCell>{new Date(item.saleDate).toLocaleDateString()}</TableCell>
                    <TableCell>{formatCurrency(item.sellingPrice)}</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(item.commissionAmount)}</TableCell>
                    <TableCell>
                      {/* Only show nominee select if we're on the original salesperson's tab or all tab */}
                      {(currentTab === "all" || currentTab === item.salesperson) && !item.nominee ? (
                        <Select onValueChange={(value) => onNomineeChange(item.id, value)}>
                          <SelectTrigger className="w-[140px] h-8 text-xs">
                            <SelectValue placeholder="Assign to..." />
                          </SelectTrigger>
                          <SelectContent>
                            {nominees.map((nominee) => (
                              <SelectItem key={nominee} value={nominee} className="text-xs">
                                {nominee}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : item.nominee ? (
                        <Badge className="bg-green-100 text-green-800 border-green-200">{item.nominee}</Badge>
                      ) : (
                        <span className="text-gray-400 text-xs">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  )
}
