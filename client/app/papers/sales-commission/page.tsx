"use client"

import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, FileDown } from "lucide-react"
import Link from "next/link"

export default function SalesCommissionPage() {
  // Current month and year for default values
  const currentMonth = new Date().getMonth() + 1
  const currentYear = new Date().getFullYear()

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/papers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Sales Commission</h1>
              <p className="text-muted-foreground">Calculate and generate sales commission reports</p>
            </div>
          </div>

          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Generate Commission Report</CardTitle>
              <CardDescription>Select salesperson and date range for commission calculation</CardDescription>
            </CardHeader>
            <CardContent className="text-center py-8">
              <p className="mb-6 text-muted-foreground">
                Click the button below to generate a comprehensive sales commission report. You'll be able to filter by
                month, year, and salesperson on the report page.
              </p>
              <Link href="/papers/sales-commission/report">
                <Button className="w-full" size="lg">
                  <FileDown className="mr-2 h-4 w-4" />
                  Generate Commission Report
                </Button>
              </Link>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
