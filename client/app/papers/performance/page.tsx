"use client"

import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, BarChart3, FileDown, PieChart } from "lucide-react"
import Link from "next/link"

export default function PerformancePage() {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/papers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Performance Analytics</h1>
              <p className="text-muted-foreground">View and export sales performance analytics</p>
            </div>
          </div>

          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle>Performance Analytics</CardTitle>
              <CardDescription>View and generate performance reports</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start-date">Start Date</Label>
                  <Input id="start-date" type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date">End Date</Label>
                  <Input id="end-date" type="date" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="report-type">Report Type</Label>
                <Select defaultValue="sales">
                  <SelectTrigger id="report-type">
                    <SelectValue placeholder="Select report type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sales">Sales Performance</SelectItem>
                    <SelectItem value="inventory">Inventory Turnover</SelectItem>
                    <SelectItem value="profit">Profit Margin</SelectItem>
                    <SelectItem value="commission">Commission Analysis</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Tabs defaultValue="chart" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="chart">Chart View</TabsTrigger>
                  <TabsTrigger value="table">Table View</TabsTrigger>
                </TabsList>
                <TabsContent value="chart" className="p-4 border rounded-md mt-2">
                  <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-md">
                    <BarChart3 className="h-16 w-16 text-gray-300 mb-4" />
                    <p className="text-sm text-gray-500">Chart visualization will appear here</p>
                  </div>
                </TabsContent>
                <TabsContent value="table" className="p-4 border rounded-md mt-2">
                  <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-md">
                    <PieChart className="h-16 w-16 text-gray-300 mb-4" />
                    <p className="text-sm text-gray-500">Table data will appear here</p>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <PieChart className="mr-2 h-4 w-4" />
                View Full Report
              </Button>
              <Button>
                <FileDown className="mr-2 h-4 w-4" />
                Export Report
              </Button>
            </CardFooter>
          </Card>
        </main>
      </div>
    </div>
  )
}
