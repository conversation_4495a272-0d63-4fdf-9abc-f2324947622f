"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Printer, FileText } from "lucide-react"
import Link from "next/link"
import { toast } from "@/components/ui/use-toast"
import { mockCars } from "@/data/mock-data"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ScrollArea } from "@/components/ui/scroll-area"
import { formatCurrency } from "@/lib/utils"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

export default function JobOrderPage() {
  const [isGenerating, setIsGenerating] = useState(false)

  // Payment document specific states
  const [documentType, setDocumentType] = useState("qc")
  const [qcName, setQcName] = useState("")
  const [transportType, setTransportType] = useState("personal")
  const [transporterName, setTransporterName] = useState("")
  const [selectedCars, setSelectedCars] = useState<string[]>([])
  const [filteredCars, setFilteredCars] = useState<any[]>([])
  const [paymentAmount, setPaymentAmount] = useState("")

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split("T")[0]
  const [purchaseDate, setPurchaseDate] = useState(today)

  // Get unique QC names from mock data
  const qcNames = Array.from(
    new Set(mockCars.filter((car) => car.buyin?.auction_checker).map((car) => car.buyin?.auction_checker)),
  )

  // Get unique transporter names from mock data
  const personalTransporters = Array.from(
    new Set(mockCars.filter((car) => car.buyin?.transport_personal).map((car) => car.buyin?.transport_personal)),
  )

  const companyTransporters = Array.from(
    new Set(mockCars.filter((car) => car.buyin?.transport_company).map((car) => car.buyin?.transport_company)),
  )

  // Filter cars based on selected criteria
  useEffect(() => {
    const filtered = mockCars.filter((car) => {
      // Filter by purchase date
      if (car.buyin?.purchase_date !== purchaseDate) {
        return false
      }

      if (documentType === "qc") {
        // Filter by QC name
        return car.buyin?.auction_checker === qcName
      } else {
        // Filter by transporter name
        if (transportType === "personal") {
          return car.buyin?.transport_personal === transporterName
        } else {
          return car.buyin?.transport_company === transporterName
        }
      }
    })

    setFilteredCars(
      filtered.map((car) => ({
        id: car.stockInfo.car_id,
        indexNumber: car.stockInfo.index_number,
        brand: car.buyin?.brand || "Unknown",
        model: car.buyin?.model || "Unknown",
        tankNumber: car.buyin?.tank_number || "Unknown",
        purchasePrice: car.buyin?.purchase_price || 0,
        qcCost: car.buyin?.qc1_auction_lot || 0,
        transportCost:
          documentType === "qc"
            ? 0
            : transportType === "personal"
              ? car.buyin?.transport_2_personal_payment || 0
              : car.buyin?.transport_3_tl_payment || 0,
        selected: false,
      })),
    )
  }, [documentType, qcName, transportType, transporterName, purchaseDate])

  // Toggle car selection
  const toggleCarSelection = (carId: string) => {
    if (selectedCars.includes(carId)) {
      setSelectedCars(selectedCars.filter((id) => id !== carId))
    } else {
      setSelectedCars([...selectedCars, carId])
    }
  }

  // Calculate total cost for selected cars
  const calculateTotalSelectedCost = () => {
    return filteredCars
      .filter((car) => selectedCars.includes(car.id))
      .reduce((total, car) => {
        return total + (documentType === "qc" ? car.qcCost : car.transportCost)
      }, 0)
  }

  // Generate payment document
  const handleGeneratePayment = () => {
    // Validate form
    if (documentType === "qc" && !qcName) {
      toast({
        title: "Error",
        description: "Please select a QC name",
        variant: "destructive",
      })
      return
    }

    if (documentType === "transfer") {
      if (transportType === "personal" && !transporterName) {
        toast({
          title: "Error",
          description: "Please select a personal transporter name",
          variant: "destructive",
        })
        return
      }
      if (transportType === "company" && !transporterName) {
        toast({
          title: "Error",
          description: "Please select a company transporter name",
          variant: "destructive",
        })
        return
      }
    }

    if (selectedCars.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one vehicle",
        variant: "destructive",
      })
      return
    }

    if (!paymentAmount) {
      toast({
        title: "Error",
        description: "Please enter the payment amount",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)

    // Simulate payment document generation
    setTimeout(() => {
      setIsGenerating(false)
      toast({
        title: "Payment Document Generated",
        description: `Payment document for ${documentType === "qc" ? "QC" : "Transport"} has been created successfully`,
      })
    }, 1500)
  }

  // Handle document type change
  const handleDocumentTypeChange = (value: string) => {
    setDocumentType(value)
    setQcName("")
    setTransportType("personal")
    setTransporterName("")
    setSelectedCars([])
  }

  // Handle transport type change
  const handleTransportTypeChange = (value: string) => {
    setTransportType(value)
    setTransporterName("")
    setSelectedCars([])
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/papers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Payment Document Generator</h1>
              <p className="text-muted-foreground">Create payment documents for QC and car transportation</p>
            </div>
          </div>

          <div className="space-y-6">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Payment Document Type</CardTitle>
                <CardDescription>Select the type of payment document to generate</CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup value={documentType} onValueChange={handleDocumentTypeChange} className="flex space-x-4">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="qc" id="qc" />
                    <Label htmlFor="qc">QC Payment</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="transfer" id="transfer" />
                    <Label htmlFor="transfer">Car Transfer Payment</Label>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>

            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Payment Details</CardTitle>
                <CardDescription>
                  {documentType === "qc"
                    ? "Select QC name and purchase date"
                    : "Select transport type, transporter name, and purchase date"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {documentType === "qc" ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="qc-name">QC Name</Label>
                        <Select value={qcName} onValueChange={setQcName}>
                          <SelectTrigger id="qc-name">
                            <SelectValue placeholder="Select QC name" />
                          </SelectTrigger>
                          <SelectContent>
                            {qcNames.map((name) => (
                              <SelectItem key={name} value={name || ""}>
                                {name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="purchase-date">Purchase Date</Label>
                        <Input
                          id="purchase-date"
                          type="date"
                          value={purchaseDate}
                          onChange={(e) => setPurchaseDate(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="payment-amount">Payment Amount</Label>
                        <Input
                          id="payment-amount"
                          type="number"
                          placeholder="Enter payment amount"
                          value={paymentAmount}
                          onChange={(e) => setPaymentAmount(e.target.value)}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="transport-type">Transport Type</Label>
                        <Select value={transportType} onValueChange={handleTransportTypeChange}>
                          <SelectTrigger id="transport-type">
                            <SelectValue placeholder="Select transport type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="personal">Personal</SelectItem>
                            <SelectItem value="company">Company</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="transporter-name">Transporter Name</Label>
                        <Select value={transporterName} onValueChange={setTransporterName}>
                          <SelectTrigger id="transporter-name">
                            <SelectValue placeholder="Select transporter name" />
                          </SelectTrigger>
                          <SelectContent>
                            {(transportType === "personal" ? personalTransporters : companyTransporters).map((name) => (
                              <SelectItem key={name} value={name || ""}>
                                {name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="purchase-date-transport">Purchase Date</Label>
                        <Input
                          id="purchase-date-transport"
                          type="date"
                          value={purchaseDate}
                          onChange={(e) => setPurchaseDate(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="payment-amount-transport">Payment Amount</Label>
                        <Input
                          id="payment-amount-transport"
                          type="number"
                          placeholder="Enter payment amount"
                          value={paymentAmount}
                          onChange={(e) => setPaymentAmount(e.target.value)}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Vehicle Selection Table */}
            {(documentType === "qc" && qcName) || (documentType === "transfer" && transporterName) ? (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Vehicle Selection</CardTitle>
                  <CardDescription>Select vehicles to include in the payment document</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[300px] w-full border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]">Select</TableHead>
                          <TableHead>Index #</TableHead>
                          <TableHead>Vehicle</TableHead>
                          <TableHead>Tank #</TableHead>
                          <TableHead>Purchase Price</TableHead>
                          <TableHead className="text-right">
                            {documentType === "qc" ? "QC Cost" : "Transport Cost"}
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredCars.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-4">
                              No vehicles found for the selected criteria
                            </TableCell>
                          </TableRow>
                        ) : (
                          filteredCars.map((car) => (
                            <TableRow key={car.id}>
                              <TableCell>
                                <input
                                  type="checkbox"
                                  checked={selectedCars.includes(car.id)}
                                  onChange={() => toggleCarSelection(car.id)}
                                  className="h-4 w-4 rounded border-gray-300"
                                />
                              </TableCell>
                              <TableCell>{car.indexNumber}</TableCell>
                              <TableCell>
                                {car.brand} {car.model}
                              </TableCell>
                              <TableCell>{car.tankNumber}</TableCell>
                              <TableCell>{formatCurrency(car.purchasePrice)}</TableCell>
                              <TableCell className="text-right">
                                {formatCurrency(documentType === "qc" ? car.qcCost : car.transportCost)}
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </ScrollArea>

                  <div className="mt-4 flex justify-between items-center">
                    <div>
                      <span className="text-sm font-medium">Selected: </span>
                      <span className="text-sm">{selectedCars.length} vehicles</span>
                    </div>
                    <div className="bg-gray-50 p-2 rounded-md">
                      <span className="text-sm font-medium">Total Cost: </span>
                      <span className="text-sm font-bold">{formatCurrency(calculateTotalSelectedCost())}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" asChild>
                    <Link href="/papers">Cancel</Link>
                  </Button>
                  <div className="flex gap-2">
                    <Button variant="outline" disabled={selectedCars.length === 0}>
                      <Printer className="mr-2 h-4 w-4" />
                      Print Preview
                    </Button>
                    <Button onClick={handleGeneratePayment} disabled={isGenerating || selectedCars.length === 0}>
                      {isGenerating ? (
                        <>
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Generating...
                        </>
                      ) : (
                        <>
                          <FileText className="mr-2 h-4 w-4" />
                          Generate Payment Document
                        </>
                      )}
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ) : null}
          </div>
        </main>
      </div>
    </div>
  )
}
