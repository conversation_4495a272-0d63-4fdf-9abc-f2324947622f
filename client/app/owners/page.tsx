"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, AlertCircle, Clock, FileDown, FileSpreadsheet, ArrowRight } from "lucide-react"
import { InventoryCountWidget } from "@/components/dashboard/inventory-count-widget"
import { FinancialSnapshot } from "@/components/dashboard/financial-snapshot"
import { SalesChannelChart } from "@/components/dashboard/sales-channel-chart"
import { BottleneckTracker } from "@/components/dashboard/bottleneck-tracker"
import { AgingInventory } from "@/components/dashboard/aging-inventory"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MonthlySummaryTable } from "@/components/monthly-summary/monthly-summary-table"
import { getMonthlySummaryData } from "@/data/mock-monthly-summary"
import type { MonthlySummary } from "@/types/models"

export default function OwnersPage() {
  const [activeTab, setActiveTab] = useState("dashboard")
  const [bottleneckStatusFilter, setBottleneckStatusFilter] = useState("all")
  const router = useRouter()

  // Check if user is logged in
  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn")
    if (!isLoggedIn) {
      router.push("/login")
    }
  }, [router])

  // Monthly summary state
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1) // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()) // Current year
  const [monthlySummaryData, setMonthlySummaryData] = useState<MonthlySummary[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Generate month options
  const months = [
    { value: 1, label: "January" },
    { value: 2, label: "February" },
    { value: 3, label: "March" },
    { value: 4, label: "April" },
    { value: 5, label: "May" },
    { value: 6, label: "June" },
    { value: 7, label: "July" },
    { value: 8, label: "August" },
    { value: 9, label: "September" },
    { value: 10, label: "October" },
    { value: 11, label: "November" },
    { value: 12, label: "December" },
  ]

  // Generate year options (last 5 years)
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  // Fetch monthly summary data
  const fetchMonthlySummary = async () => {
    setIsLoading(true)
    try {
      const data = await getMonthlySummaryData(selectedMonth, selectedYear)
      setMonthlySummaryData(data)
    } catch (error) {
      console.error("Error fetching monthly summary:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data when tab changes to monthly
  useEffect(() => {
    if (activeTab === "monthly") {
      fetchMonthlySummary()
    }
  }, [activeTab])

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Owner Records</h1>
            <p className="text-muted-foreground">Manage vehicle owner information and monthly summaries</p>
          </div>

          <Tabs defaultValue="dashboard" className="w-full" onValueChange={setActiveTab}>
            <div className="flex justify-between items-center mb-6">
              <TabsList className="grid w-[400px] grid-cols-2">
                <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                <TabsTrigger value="monthly">Monthly Summary</TabsTrigger>
              </TabsList>
            </div>

            {/* Dashboard Tab Content */}
            <TabsContent value="dashboard" className="space-y-5">
              {/* First Row: Financial Snapshot (moved to top) */}
              <div className="grid gap-6">
                <FinancialSnapshot />
              </div>

              {/* Second Row: Left - Inventory Status & Sales Channels, Right - Bottleneck & Aging */}
              <div className="grid gap-6 md:grid-cols-2">
                {/* Left Column: Inventory Status and Top Sales Channels */}
                <div className="md:col-span-1 space-y-5">
                  {/* Inventory Status */}
                  <InventoryCountWidget />

                  {/* Top Sales Channels (below Inventory Status) */}
                  <Card className="h-[calc(100%-280px)]">
                    <CardHeader className="p-4 pb-0">
                      <CardTitle className="text-sm">Top Sales Channels</CardTitle>
                      <CardDescription className="text-xs">Distribution of sales by channel</CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 h-[calc(100%-60px)]">
                      <SalesChannelChart />
                    </CardContent>
                  </Card>
                </div>

                {/* Right Column: Bottleneck Tracker and Aging Inventory */}
                <div className="md:col-span-1 space-y-5">
                  {/* Bottleneck Tracker */}
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 p-4 pb-0">
                      <div>
                        <CardTitle className="text-lg">Bottleneck Tracker</CardTitle>
                        <CardDescription className="text-xs">Vehicles stuck in process for &gt;10 days</CardDescription>
                      </div>
                      <AlertCircle className="h-5 w-5 text-amber-500" />
                    </CardHeader>
                    <CardContent className="p-4">
                      <div className="mb-3">
                        <Select defaultValue="all" onValueChange={(value) => setBottleneckStatusFilter(value)}>
                          <SelectTrigger className="h-8 text-xs w-[180px]">
                            <SelectValue placeholder="Filter by status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Statuses</SelectItem>
                            <SelectItem value="transfer">Transfer</SelectItem>
                            <SelectItem value="repair">Repair</SelectItem>
                            <SelectItem value="finance_request">Finance Request</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <BottleneckTracker statusFilter={bottleneckStatusFilter} />
                    </CardContent>
                  </Card>

                  {/* Aging Inventory */}
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 p-4 pb-0">
                      <div>
                        <CardTitle className="text-lg">Aging Inventory</CardTitle>
                        <CardDescription className="text-xs">Available vehicles with slow turnover</CardDescription>
                      </div>
                      <Clock className="h-5 w-5 text-red-500" />
                    </CardHeader>
                    <CardContent className="p-4">
                      <AgingInventory />
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            {/* Monthly Summary Tab Content */}
            <TabsContent value="monthly" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Monthly Owner Summary</CardTitle>
                      <CardDescription>Overview of sold vehicles for the selected period</CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-5 w-5 text-muted-foreground" />
                      <span className="text-sm font-medium">
                        {months.find((m) => m.value === selectedMonth)?.label} {selectedYear}
                      </span>
                      {!isLoading && monthlySummaryData.length > 0 && (
                        <span className="ml-2 bg-primary text-white text-xs font-medium px-2 py-0.5 rounded-full">
                          {monthlySummaryData.length} Cars Sold
                        </span>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Date selection controls */}
                  <div className="flex flex-wrap gap-4 mb-6">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Month:</span>
                      <Select
                        value={selectedMonth.toString()}
                        onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                      >
                        <SelectTrigger className="w-[140px]">
                          <SelectValue placeholder="Select month" />
                        </SelectTrigger>
                        <SelectContent>
                          {months.map((month) => (
                            <SelectItem key={month.value} value={month.value.toString()}>
                              {month.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Year:</span>
                      <Select
                        value={selectedYear.toString()}
                        onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                      >
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="Select year" />
                        </SelectTrigger>
                        <SelectContent>
                          {years.map((year) => (
                            <SelectItem key={year} value={year.toString()}>
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <Button variant="outline" onClick={fetchMonthlySummary} className="flex items-center gap-1">
                      <span>Enter</span>
                      <ArrowRight className="h-4 w-4" />
                    </Button>

                    <Button onClick={fetchMonthlySummary} className="ml-auto text-white" disabled={isLoading}>
                      {isLoading ? "Loading..." : "Generate Report"}
                    </Button>

                    <Button variant="outline" disabled={monthlySummaryData.length === 0}>
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      Export Excel
                    </Button>

                    <Button variant="outline" disabled={monthlySummaryData.length === 0}>
                      <FileDown className="h-4 w-4 mr-2" />
                      Export PDF
                    </Button>
                  </div>

                  {/* Monthly summary table */}
                  <MonthlySummaryTable data={monthlySummaryData} isLoading={isLoading} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}
