"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Header } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"

export default function Home() {
  const router = useRouter()

  // Check if user is logged in
  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn")
    if (!isLoggedIn) {
      router.push("/login")
    }
  }, [router])

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Welcome to SP Autocar Vehicle Management System</p>
          </div>

          {/* Empty content area */}
          <div className="h-full flex items-center justify-center text-muted-foreground">
            Dashboard content will be added here
          </div>
        </main>
      </div>
    </div>
  )
}

// client/app/test/page.tsx
// client/app/page.tsx
// export default function Home() {
//   return (
//     <div className="flex items-center justify-center min-h-screen bg-gray-100">
//       <h1 className="text-3xl font-bold text-blue-600">✅ Home page working!</h1>
//     </div>
//   )
// }
