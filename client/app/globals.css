/* Remove the Google Fonts import that's causing the error */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221 57% 46%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    height: 100vh;
    overflow: hidden;
  }
}

@layer utilities {

  /* Hide scrollbar but allow scrolling */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
  }
}

/* Add Thai font utility class */
.thaifont {
  font-family: var(--font-sarabun);
}

/* Custom styles for the table */
.data-table th {
  @apply bg-primary-dark text-white font-medium text-left p-2 transition-colors;
  height: 36px;
  position: relative;
}

.data-table th:after {
  content: "";
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
}

.data-table th:last-child:after {
  display: none;
}

/* Add this new style for the monthly tax table headers */
.border-collapse th {
  @apply bg-primary-dark text-white;
}

/* Commission table headers */
.commission-table th {
  @apply bg-primary-dark text-white;
}

.data-table tr {
  @apply transition-colors border-b border-gray-100;
  height: 36px;
}

.data-table tr:nth-child(even) {
  @apply bg-gray-50/50;
}

.data-table tr:hover {
  @apply bg-primary/5;
}

.data-table td {
  @apply p-2 transition-colors;
  height: 36px;
  max-height: 36px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Tab styling */
.custom-tabs {
  @apply inline-flex h-8 items-center justify-center rounded-md bg-gray-100 p-1;
}

.custom-tab {
  @apply inline-flex items-center justify-center whitespace-nowrap rounded-sm px-2 py-1 text-xs font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
}

.custom-tab[data-state="active"] {
  @apply bg-primary text-white shadow-sm;
}

/* Make sure the table container fills available space */
.table-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.table-container table {
  flex: 1;
}

.table-scroll-container {
  flex: 1;
  overflow: auto;
}

/* Remove shadow from select trigger and input */
.shadow-none {
  box-shadow: none !important;
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 1366px) {

  /* Adjustments for MacBook screens */
  .data-table th,
  .data-table td,
  .data-table tr {
    height: 32px;
    /* Even more compact for smaller screens */
    padding: 1px 2px;
  }

  .data-table th button,
  .data-table td {
    font-size: 0.7rem;
  }
}

/* Enhanced dropdown styling */
.dropdown-menu-enhanced {
  @apply rounded-md shadow-lg border border-gray-200 overflow-hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced button styling */
.button-enhanced {
  @apply transition-all duration-200 hover:shadow-md;
}

/* Column group headers */
.column-group-header {
  @apply bg-gray-100 font-medium text-xs py-1.5 px-2 text-gray-700;
}

/* Add these animations to your globals.css file */
@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes draw-check {
  from {
    stroke-dashoffset: 100;
  }

  to {
    stroke-dashoffset: 0;
  }
}

.animate-shake {
  animation: shake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out forwards;
}

.animate-draw-check {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: draw-check 0.6s ease-in-out forwards;
}

/* Style the date filter badges */
.date-filter-badge {
  display: inline-flex;
  align-items: center;
  background-color: hsl(var(--primary));
  color: white;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-family: var(--font-sarabun) !important;
  margin-right: 0.25rem;
}

.date-filter-badge-clear {
  margin-left: 0.25rem;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.date-filter-badge-clear:hover {
  color: white;
}

/* Keep existing CSS and add/modify these styles */

/* Thai Date Picker Styling */
.thai-datepicker-container {
  position: relative;
  font-family: var(--font-sarabun) !important;
}

/* Style the input to match other form elements */
.thai-date-input {
  border: 1px solid hsl(var(--input)) !important;
  border-radius: 0.375rem !important;
  height: 1.75rem !important;
  font-size: 0.75rem !important;
  padding: 0 0.75rem !important;
  width: 100% !important;
  font-family: var(--font-sarabun) !important;
}

/* Fix for the date picker popup being cut off */
.thai-date-picker-wrapper {
  position: relative;
}

/* CRITICAL FIX: Ensure the date picker popup appears above everything */
/* This is the main container for the ThaiDatePicker */
.thaidatepicker-container {
  position: relative;
  font-family: var(--font-sarabun) !important;
}

/* This targets the actual calendar popup */
.thaidatepicker-calendar {
  position: absolute !important;
  z-index: 9999 !important;
  /* Very high z-index to ensure it's above everything */
  background-color: white !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  font-family: var(--font-sarabun) !important;
}

/* Target all elements inside the calendar */
.thaidatepicker-calendar *,
.react-datepicker *,
.react-datepicker__header *,
.react-datepicker__month *,
.react-datepicker__day *,
.react-datepicker__current-month,
.react-datepicker__day-name {
  font-family: var(--font-sarabun) !important;
}

/* Ensure the calendar appears in front of the table */
div[class*="react-datepicker"],
div[class*="react-datepicker-popper"] {
  z-index: 9999 !important;
}

/* Target the specific Thai date picker elements */
.thaidatepicker-react-container,
.thaidatepicker-react-calendar,
.thaidatepicker-react-wrapper,
.thaidatepicker-react-portal {
  z-index: 9999 !important;
  position: relative;
}

/* Style the date range container */
.thai-date-range-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.thai-date-range-container .thai-date-picker-wrapper {
  flex: 1;
}

/* Style the date range separator */
.date-range-separator {
  font-family: var(--font-sarabun) !important;
  font-size: 0.75rem;
  color: hsl(var(--foreground));
}

/* Style the date badge */
.date-badge {
  display: inline-flex;
  align-items: center;
  background-color: hsl(var(--primary) / 0.1);
  border: 1px solid hsl(var(--primary) / 0.2);
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-family: var(--font-sarabun) !important;
  color: hsl(var(--primary));
  margin-right: 0.5rem;
}

.date-badge-clear {
  margin-left: 0.25rem;
  color: hsl(var(--muted-foreground));
  cursor: pointer;
}

.date-badge-clear:hover {
  color: hsl(var(--foreground));
}

/* Override any default styles from the library */
.thaidatepicker-react input,
.react-datepicker__input-container input {
  border: 1px solid hsl(var(--input)) !important;
  border-radius: 0.375rem !important;
  height: 1.75rem !important;
  font-size: 0.75rem !important;
  padding: 0 0.75rem !important;
  width: 100% !important;
  font-family: var(--font-sarabun) !important;
}

/* Force Thai font on all text elements */
.thaidatepicker-react,
.thaidatepicker-react *,
.thaidatepicker-react div,
.thaidatepicker-react span,
.thaidatepicker-react button,
.thaidatepicker-react input,
.react-datepicker,
.react-datepicker * {
  font-family: var(--font-sarabun) !important;
}

/* Style the date filter badges */
.date-filter-badge {
  display: inline-flex;
  align-items: center;
  background-color: hsl(var(--primary));
  color: white;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-family: var(--font-sarabun) !important;
  margin-right: 0.25rem;
}

.date-filter-badge-clear {
  margin-left: 0.25rem;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.date-filter-badge-clear:hover {
  color: white;
}