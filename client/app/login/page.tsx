"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { api } from "../services/apiServices"
import { Eye, EyeOff, Loader2 } from "lucide-react"

export default function LoginPage() {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [loginSuccess, setLoginSuccess] = useState(false)
  const router = useRouter()

  // Reset animation state when component unmounts
  useEffect(() => {
    return () => {
      setLoginSuccess(false)
      setIsLoading(false)
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    if (!username || !password) {
      setError("Please enter both username and password")
      setIsLoading(false)
      return
    }

    try {
      const result = await api.login(username, password)

      // Show success animation before redirecting
      setLoginSuccess(true)

      // ✅ Trust backend response
      localStorage.setItem("isLoggedIn", "true")
      localStorage.setItem("user", JSON.stringify(result.user))
      localStorage.setItem("token", result.auth_token)

      // Delay redirect to show success animation
      setTimeout(() => {
        router.push("/")
      }, 800)
    } catch (err: any) {
      console.log("Message from frontend", err.message)
      setError("Login failed please contact admin")
      setIsLoading(false)
      setLoginSuccess(false)
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <div className="flex min-h-screen">
      {/* Left Column - Login Form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-8 lg:p-12 bg-white">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-primary-dark">SP Autocar</h1>
            <p className="mt-2 text-gray-600">Sign in to your account</p>
          </div>

          {error && <div className="bg-red-50 text-red-500 px-4 py-3 rounded-md text-sm animate-shake">{error}</div>}

          <form onSubmit={handleSubmit} className="mt-8 space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter your username"
                  className="mt-1"
                  autoComplete="username"
                  disabled={isLoading || loginSuccess}
                  required
                />
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <a href="#" className="text-sm text-primary hover:text-primary-dark">
                    Forgot password?
                  </a>
                </div>
                <div className="relative mt-1">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="pr-10"
                    autoComplete="current-password"
                    disabled={isLoading || loginSuccess}
                    required
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                    tabIndex={-1}
                    disabled={isLoading || loginSuccess}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" aria-hidden="true" />
                    ) : (
                      <Eye className="h-4 w-4" aria-hidden="true" />
                    )}
                    <span className="sr-only">{showPassword ? "Hide password" : "Show password"}</span>
                  </button>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember-me"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                  disabled={isLoading || loginSuccess}
                />
                <Label htmlFor="remember-me" className="text-sm">
                  Remember me
                </Label>
              </div>
            </div>

            <Button
              type="submit"
              className={`w-full relative overflow-hidden transition-all duration-300 ${loginSuccess ? "bg-green-500 hover:bg-green-600" : "bg-primary hover:bg-primary-dark"
                } text-white`}
              disabled={isLoading || loginSuccess}
            >
              <span
                className={`flex items-center justify-center transition-all duration-300 ${isLoading ? "opacity-0" : loginSuccess ? "opacity-0" : "opacity-100"
                  }`}
              >
                Sign in
              </span>

              {isLoading && (
                <span className="absolute inset-0 flex items-center justify-center animate-fade-in">
                  <Loader2 className="h-5 w-5 animate-spin text-white" />
                </span>
              )}

              {loginSuccess && (
                <span className="absolute inset-0 flex items-center justify-center animate-fade-in">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                      className="animate-draw-check"
                    />
                  </svg>
                  <span className="ml-2">Success!</span>
                </span>
              )}
            </Button>
          </form>
        </div>
      </div>

      {/* Right Column - Background with Car Image */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary-dark to-primary/90 flex-col items-center justify-center relative overflow-hidden">
        {/* Background pattern overlay */}
        <div className="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJ3aGl0ZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMzYgMzRoLTJ2LTRoMnY0em0wLTZ2LTRoLTJ2NGgyek0yNCAzNGgtMnYtNGgydjR6bTAtNnYtNGgtMnY0aDJ6Ii8+PC9nPjwvc3ZnPg==')]"></div>

        <div className="text-center text-white z-10 px-12 pt-12">
          <h2 className="text-3xl font-bold mb-4 drop-shadow-md">Second Hand Vehicle Management</h2>
          <p className="text-lg text-white/90 mb-8 drop-shadow">
            Streamline your inventory, track sales, and manage your second-hand vehicle business efficiently.
          </p>
        </div>

        {/* Car Image with enhanced styling */}
        <div className="relative w-full max-w-2xl mt-4 px-8">
          <div className="absolute -inset-1 bg-white/10 rounded-xl blur-xl"></div>
          <div className="relative">
            <Image
              src="/images/sp-ford_mustang.png"
              alt="Orange Ford Mustang"
              width={800}
              height={450}
              className="object-contain drop-shadow-2xl"
              priority
            />
          </div>
        </div>
      </div>
    </div>
  )
}
