// client/app/services/monthlyTaxService.ts

import { PurchaseTaxData, SelloutTaxData } from "../papers/monthly-tax/types"

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

async function request<T>(url: string, options?: RequestInit): Promise<T> {
    const res = await fetch(`${API_BASE}${url}`, {
        headers: { 'Content-Type': 'application/json' },
        ...options
    })

    const data = await res.json()

    if (!res.ok) throw new Error(data.error || 'API error')
    return data
}

export interface MonthlyTaxParams {
    month: number
    year: number
}

export interface MonthlyTaxResponse {
    purchaseData: PurchaseTaxData[]
    selloutData: SelloutTaxData[]
}

/**
 * ===================================
 * MONTHLY TAX API
 * ===================================
 */
export const MONTHLY_TAX_API = {
    /**
     * Get monthly tax data
     * @param month Month number (1-12)
     * @param year Year in Gregorian format (must be converted from Buddhist year by subtracting 543)
     * @param authToken Authentication token
     * @returns Monthly tax data including purchase and sellout information
     */
    GET_MONTHLY_TAX: async (month: number, year: number, authToken: string): Promise<MonthlyTaxResponse> => {
        console.log("Check from calling function", year)
        return request<MonthlyTaxResponse>(`/api/reports/monthly-tax?month=${month}&year=${year}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
    }
}
