"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { DataTable } from "@/components/ui/data-table/data-table"
import { createColumns } from "@/components/ui/data-table/columns"
import { FilterTabs } from "@/components/ui/data-table/filter-tabs"
// import { mockCars } from "@/data/mock-data" // Not used
// import type { CarComplete } from "@/types/models"
import type { CarComplete } from "../../../types/models"
import { Button } from "@/components/ui/button"
import { Plus, AlertCircle, Loader2 } from "lucide-react"
import { AddItemModal } from "@/components/ui/add-item-modal"
import { EditCarDrawer } from "@/components/ui/edit-car-drawer"
import { toast } from "@/components/ui/use-toast"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { VEHICLES_API } from "../services/apiServices"
import { generateUUID, getThailandTimeISO } from "@/lib/utils"

export default function ItemsPage() {
  // State for cars data
  const [cars, setCars] = useState<CarComplete[]>([])
  const [filteredData, setFilteredData] = useState<CarComplete[]>([])

  // UI state management
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false)
  const [selectedCar, setSelectedCar] = useState<CarComplete | undefined>(undefined)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  // Loading and error states
  const [isLoading, setIsLoading] = useState(true)
  const [isActionLoading, setIsActionLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch cars on component mount
  useEffect(() => {
    fetchCars()
  }, [])

  // Simulate fetching cars from backend
  const fetchCars = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const authToken = localStorage.getItem("token")
      if (!authToken) {
        console.error('No auth token found.')
        setError("Failed to load vehicles. Please try again later.")
        return
      }
      const carsData = await VEHICLES_API.GET_ALL(authToken)

      // Using mock data for demonstration
      setCars(carsData)
      console.log("Check freshly fetch information", carsData[0])
      setFilteredData(carsData)
    } catch (err) {
      console.error("Error fetching cars:", err)
      setError("Failed to load vehicles. Please try again later.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleStatusFilterChange = (value: string) => {
    if (value === "all") {
      setFilteredData(cars)
    } else {
      setFilteredData(cars.filter((car) => car.stockInfo.car_status === value))
    }
  }

  const handleAddItem = async (data: any) => {
    setIsActionLoading(true)

    try {
      // Simulate API call with timeout
      const authToken = localStorage.getItem("token")
      if (!authToken) {
        console.error("No auth token found.")
        setError("Failed to load vehicles. Please try again later.")
        return
      }
      const uuid = generateUUID()
      const carData = { car_id: uuid, ...data }
      await VEHICLES_API.CREATE_ITEM(carData, authToken)

      // Refresh data from the server to ensure we have the latest state
      console.log("Refreshing data from server after adding new car...")
      const updatedCars = await VEHICLES_API.GET_ALL(authToken)

      // Update both the cars list and filtered data
      setCars(updatedCars)
      setFilteredData(updatedCars.filter(car =>
        filteredData.length === cars.length ||
        car.stockInfo.car_status === filteredData[0]?.stockInfo.car_status
      ))

      // Show success toast
      toast({
        title: "เพิ่มรถยนต์สำเร็จ",
        description: "รถยนต์ใหม่ได้ถูกเพิ่มเข้าในระบบเรียบร้อยแล้ว",
      })

      // Close the modal
      setIsAddModalOpen(false)
    } catch (err) {
      console.error("Error adding car:", err)
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถเพิ่มรถยนต์ได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      })
    } finally {
      setIsActionLoading(false)
    }
  }

  const handleEditCar = (car: CarComplete) => {
    setSelectedCar(car)
    setIsEditDrawerOpen(true)
  }

  const handleViewCar = (car: CarComplete) => {
    setSelectedCar(car)
    setIsEditDrawerOpen(true)
  }

  const handleDeleteCar = (car: CarComplete) => {
    setSelectedCar(car)
    setIsDeleteDialogOpen(true)
  }

  const confirmDeleteCar = async () => {
    if (!selectedCar) return

    setIsActionLoading(true)

    try {
      const authToken = localStorage.getItem("token")
      if (!authToken) {
        console.error("No auth token found.")
        setError("Failed to load vehicles. Please try again later.")
        return
      }

      const carId = selectedCar.stockInfo?.car_id
      if (!carId) {
        toast({
          title: "เกิดข้อผิดพลาด",
          description: "ไม่พบข้อมูลรหัสรถยนต์ กรุณาลองใหม่อีกครั้ง",
          variant: "destructive",
        })
        return
      }

      await VEHICLES_API.REMOVE_ITEM(carId, authToken)

      // Refresh data from the server to ensure we have the latest state
      console.log("Refreshing data from server after deleting car...")
      const updatedCars = await VEHICLES_API.GET_ALL(authToken)

      // Update both the cars list and filtered data
      setCars(updatedCars)
      setFilteredData(updatedCars.filter(car =>
        filteredData.length === cars.length ||
        car.stockInfo.car_status === filteredData[0]?.stockInfo.car_status
      ))

      toast({
        title: "ลบรถยนต์สำเร็จ",
        description: "รถยนต์ได้ถูกลบออกจากระบบเรียบร้อยแล้ว",
      })

      setIsDeleteDialogOpen(false)
      setSelectedCar(undefined) // Clear selected car after delete
    } catch (err) {
      console.error("Error deleting car:", err)
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถลบรถยนต์ได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      })
    } finally {
      setIsActionLoading(false)
    }
  }


  const handleSaveEdit = async (data: any) => {
    if (!selectedCar) return

    setIsActionLoading(true)
    const authToken = localStorage.getItem("token")
      if (!authToken) {
        console.error('No auth token found.')
        setError("Failed to load vehicles. Please try again later.")
        return
      }

    try {
      // Create a function to detect changes between original and new data
      const getChangedFields = (original: any, updated: any, prefix = "") => {
        const changes: Record<string, any> = {}

        // Helper function to check if values are different
        const isDifferent = (val1: any, val2: any) => {
          // Handle numeric values stored as strings
          if (typeof val1 === "number" && typeof val2 === "string") {
            return val1 !== Number(val2)
          }
          if (typeof val1 === "string" && typeof val2 === "number") {
            return Number(val1) !== val2
          }
          return val1 !== val2
        }

        // Compare each field in the updated data
        Object.keys(updated).forEach((key) => {
          const fullKey = prefix ? `${prefix}.${key}` : key

          // Skip if the key doesn't exist in the original data
          if (original[key] === undefined) {
            changes[key] = updated[key]
            return
          }

          // If the value is an object, recursively check its fields
          if (
            typeof updated[key] === "object" &&
            updated[key] !== null &&
            !Array.isArray(updated[key]) &&
            typeof original[key] === "object" &&
            original[key] !== null
          ) {
            const nestedChanges = getChangedFields(original[key], updated[key], fullKey)
            if (Object.keys(nestedChanges).length > 0) {
              changes[key] = nestedChanges
            }
          }
          // Otherwise, check if the value has changed
          else if (isDifferent(original[key], updated[key])) {
            changes[key] = updated[key]
          }
        })

        return changes
      }

      // Extract original data from selectedCar
      const originalData = {
        stockInfo: { ...selectedCar.stockInfo },
        buyin: selectedCar.buyin ? { ...selectedCar.buyin } : undefined,
        repair: selectedCar.repair ? { ...selectedCar.repair } : undefined,
        finance: selectedCar.finance ? { ...selectedCar.finance } : undefined,
        sellout: selectedCar.sellout ? { ...selectedCar.sellout } : undefined,
      }

      // Organize the new data in the same structure
      const newData = {
        stockInfo: {
          ...selectedCar.stockInfo,
          updated_at: getThailandTimeISO(),

          car_id: selectedCar.stockInfo.car_id, // Preserved UUID
          index_number: data.index_number || selectedCar.stockInfo.index_number,
          is_auction_car: data.is_auction_car === "Company car", // Convert string to boolean
          registration_book_received_date:
            data.registration_book_received_date || selectedCar.stockInfo.registration_book_received_date,
          old_license_plate: data.old_license_plate || selectedCar.stockInfo.old_license_plate,
          new_license_plate: data.new_license_plate || selectedCar.stockInfo.new_license_plate,
          registration_date: data.registration_date || selectedCar.stockInfo.registration_date,
          total_investment: parseFloat(data.total_investment) || selectedCar.stockInfo.total_investment,
          listed_price: parseFloat(data.listed_price) || selectedCar.stockInfo.listed_price,
          car_status: data.car_status || selectedCar.stockInfo.car_status,
          notes: data.notes || selectedCar.stockInfo.notes,
        },
        buyin: selectedCar.buyin
          ? {
            ...selectedCar.buyin,
            updated_at: getThailandTimeISO(),
            car_id: selectedCar.stockInfo.car_id, // use same car_id

            purchase_date: data.purchase_date || selectedCar.buyin.purchase_date,
            parking_location: data.parking_location || selectedCar.buyin.parking_location,
            transport_1_auction_lot: parseFloat(data.transport_1_auction_lot) || 0,
            transport_2_personal_payment: parseFloat(data.transport_2_personal_payment) || 0,
            transport_3_tl_payment: parseFloat(data.transport_3_tl_payment) || 0,
            qc1_auction_lot: parseFloat(data.qc1_auction_lot) || 0,
            initial_check: parseFloat(data.initial_check) || 0,
            ems_registration_qc3: parseFloat(data.ems_registration || 0) || parseFloat(data.ems_registration_qc3 || 0),
            qc3: parseFloat(data.qc3) || 0,
            registration_fee: parseFloat(data.registration_fee) || 0,

            brand: data.brand || selectedCar.buyin.brand,
            model: data.model || selectedCar.buyin.model,
            color: data.color || selectedCar.buyin.color,
            year: parseInt(data.year) || selectedCar.buyin.year || null,
            vat_percent: parseFloat(data.vat_percent) || 0,
            purchase_price: parseFloat(data.purchase_price) || 0,
            purchase_vat_percent: parseFloat(data.purchase_vat_percent) || 0,
            operation_cost_incl_vat: parseFloat(data.operation_cost_incl_vat) || 0,
            tax_insurance_cost_zero: parseFloat(data.tax_insurance_cost_zero) || 0,
            other_costs_seven: parseFloat(data.other_costs_seven) || 0,
            five_three_tax_percentage: parseFloat(data.five_three_tax_percentage) || 0,
            total_purchase_cost: parseFloat(data.total_purchase_cost) || 0,

            auction_location: data.auction_location || selectedCar.buyin.auction_location,
            auction_provinced: data.auction_provinced || selectedCar.buyin.auction_provinced,
            auction_order: data.auction_order || selectedCar.buyin.auction_order,
            auction_checker: data.auction_checker || selectedCar.buyin.auction_checker,
            transport_personal: data.transport_personal || selectedCar.buyin.transport_personal,
            transport_company: data.transport_company || selectedCar.buyin.transport_company,

            tank_number: data.tank_number || selectedCar.buyin.tank_number,
            engine_number: data.engine_number || selectedCar.buyin.engine_number,
            book_deposit: parseFloat(data.book_deposit) || 0,
            type_of_transport: data.type_of_transport || selectedCar.buyin.type_of_transport,
          }
          : undefined,
        repair: {
            ...selectedCar.repair,
            car_id: selectedCar.stockInfo.car_id, // Ensure linkage with stockInfo

            repainting_cost: parseFloat(data.repainting_cost) || 0,
            engine_repair_cost: parseFloat(data.engine_repair_cost) || 0,
            suspension_repair_cost: parseFloat(data.suspension_repair_cost) || 0,
            autopart_cost: parseFloat(data.autopart_cost) || 0,
            battery_cost: parseFloat(data.battery_cost) || 0,
            tires_wheels_cost: parseFloat(data.tires_wheels_cost) || 0,

            updated_at: getThailandTimeISO()
          },
        finance:  {
            ...selectedCar.finance,
            car_id: selectedCar.stockInfo.car_id,
            updated_at: getThailandTimeISO(),

            finance_request_date: data.finance_request_date || null, // Added finance_request_date field
            finance_received_date: data.finance_received_date || null,
            car_tax_invoice_date: data.car_tax_invoice_date || null,
            car_tax_invoice_number: data.car_tax_invoice_number || null,
            car_amount: parseFloat(data.car_amount) || 0,
            car_vat_amount: parseFloat(data.car_vat_amount) || 0,
            commission_tax_invoice_date: data.commission_tax_invoice_date || null,
            commission_tax_invoice_number: data.commission_tax_invoice_number || null,
            car_commission_amount: parseFloat(data.car_commission_amount) || 0,
            input_vat_commission: parseFloat(data.input_vat_commission) || 0,
            withholding_tax: parseFloat(data.withholding_tax) || 0,
            salesperson: data.salesperson || null,
            bank: data.bank || null,
            marketing_person: data.marketing_person || null,
            promotion_customer: parseFloat(data.promotion_customer) || 0,
            bonus_insurance_car_life_engine: parseFloat(data.bonus_insurance_car_life_engine) || 0,
            customer_name: data.customer_name || null,
            customer_address_or_advance_payment: data.customer_address_or_advance_payment || null,
            down_payment: parseFloat(data.down_payment) || 0,
            loan_protection_insurance: parseFloat(data.loan_protection_insurance) || 0,
            accident_insurance: parseFloat(data.accident_insurance) || 0,
            car_insurance: parseFloat(data.car_insurance) || 0,
            bank_documents: parseFloat(data.bank_documents) || 0,
          },
        sellout:  {
            ...selectedCar.sellout,
            car_id: selectedCar.stockInfo.car_id,
            updated_at: getThailandTimeISO(),

            sale_date: data.sale_date || null,
            owner_name: data.owner_name || null,
            customer_address: data.customer_address || null,
            actual_selling_price: parseFloat(data.actual_selling_price) || 0,
            commission_s: parseFloat(data.commission_s) || 0,
            commission_agent: parseFloat(data.commission_agent) || 0,
            commission_manager: parseFloat(data.commission_manager) || 0,
            sales_channel: data.sales_channel || null,
          }
      }

      // Get only the changed fields
      const newInformation = getChangedFields(originalData, newData)

      // Log finance_request_date specifically to help debug
      const carId = originalData.stockInfo.car_id
      await VEHICLES_API.PATCH_ITEM(carId, newInformation, authToken)

      // Refresh data from the server to ensure we have the latest state
      console.log("Refreshing data from server after update...")
      const updatedCars = await VEHICLES_API.GET_ALL(authToken)

      // Update both the cars list and filtered data
      setCars(updatedCars)
      setFilteredData(updatedCars.filter(car =>
        filteredData.length === cars.length ||
        car.stockInfo.car_status === filteredData[0]?.stockInfo.car_status
      ))

      // Find the updated car in the refreshed data
      const updatedCar = updatedCars.find((car: CarComplete) => car.stockInfo.car_id === carId)

      if (updatedCar) {
        // Update the selected car with fresh data from the server
        setSelectedCar(updatedCar)
      }

      toast({
        title: "อัพเดทรถยนต์สำเร็จ",
        description: "รถยนต์ได้ถูกอัพเดทเข้าระบบเรียบร้อยแล้ว",
      })

      // Close the drawer
      setIsEditDrawerOpen(false)
    } catch (err) {
      console.error("Error updating car:", err)
      toast({
        title: "เกิดข้อผิดพลาด",
        description: "ไม่สามารถอัปเดตรถยนต์ได้ กรุณาลองใหม่อีกครั้ง",
        variant: "destructive",
      })
    } finally {
      setIsActionLoading(false)
    }
  }

  // Create columns with action handlers
  const columns = createColumns(handleEditCar, handleViewCar, handleDeleteCar)
  // Loading state for initial data fetch
  if (isLoading) {
    return (
      <div className="flex h-screen w-full overflow-hidden">
        <Sidebar />
        <div className="flex flex-1 flex-col overflow-hidden">
          <Header />
          <main className="flex-1 flex flex-col p-4 overflow-hidden">
            <div className="flex justify-between items-center mb-2">
              <div>
                <h1 className="text-xl font-bold text-primary-dark thaifont">รายการรถยนต์</h1>
                <p className="text-sm text-muted-foreground thaifont">จัดการและดูรถยนต์ทั้งหมดในระบบ</p>
              </div>
            </div>
            <div className="flex-1 flex items-center justify-center">
              <div className="flex flex-col items-center">
                <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                <p className="text-muted-foreground thaifont">กำลังโหลดข้อมูลรถยนต์...</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex h-screen w-full overflow-hidden">
        <Sidebar />
        <div className="flex flex-1 flex-col overflow-hidden">
          <Header />
          <main className="flex-1 flex flex-col p-4 overflow-hidden">
            <div className="flex justify-between items-center mb-2">
              <div>
                <h1 className="text-xl font-bold text-primary-dark thaifont">รายการรถยนต์</h1>
                <p className="text-sm text-muted-foreground thaifont">จัดการและดูรถยนต์ทั้งหมดในระบบ</p>
              </div>
            </div>
            <div className="flex-1 flex items-center justify-center">
              <div className="w-full max-w-md">
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle className="thaifont">เกิดข้อผิดพลาด</AlertTitle>
                  <AlertDescription className="thaifont">
                    {error}
                    <div className="mt-4">
                      <Button onClick={fetchCars} className="bg-primary hover:bg-primary-dark text-white thaifont">
                        ลองใหม่อีกครั้ง
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          </main>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen w-full overflow-hidden">
      <Sidebar />
      <div className="flex flex-1 flex-col overflow-hidden">
        <Header />
        <main className="flex-1 flex flex-col p-4 overflow-hidden">
          <div className="flex justify-between items-center mb-2">
            <div>
              <h1 className="text-xl font-bold text-primary-dark thaifont">รายการรถยนต์</h1>
              <p className="text-sm text-muted-foreground thaifont">จัดการและดูรถยนต์ทั้งหมดในระบบ</p>
            </div>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-primary hover:bg-primary-dark text-white font-medium thaifont"
              disabled={isActionLoading}
            >
              {isActionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  กำลังดำเนินการ...
                </>
              ) : (
                <>
                  <Plus className="mr-1 h-4 w-4" /> เพิ่มรถยนต์
                </>
              )}
            </Button>
          </div>

          <FilterTabs onFilterChange={handleStatusFilterChange} data={cars} />

          <div className="flex-1 overflow-hidden mt-2 relative">
            {/* Overlay loading indicator for table actions */}
            {isActionLoading && (
              <div className="absolute inset-0 bg-white/70 flex items-center justify-center z-10">
                <div className="flex flex-col items-center">
                  <Loader2 className="h-8 w-8 text-primary animate-spin mb-2" />
                  <p className="text-sm text-primary thaifont">กำลังดำเนินการ...</p>
                </div>
              </div>
            )}
            <DataTable columns={columns} data={filteredData} />
          </div>
        </main>
      </div>

      {/* Add Item Modal */}
      <AddItemModal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} onSave={handleAddItem} />

      {/* Edit Car Drawer */}
      <EditCarDrawer
        isOpen={isEditDrawerOpen}
        onClose={() => setIsEditDrawerOpen(false)}
        car={selectedCar}
        onSave={handleSaveEdit}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="thaifont">
          <AlertDialogHeader>
            <AlertDialogTitle>คุณแน่ใจหรือไม่ที่จะลบรถยนต์คันนี้?</AlertDialogTitle>
            <AlertDialogDescription>
              การกระทำนี้ไม่สามารถย้อนกลับได้ ข้อมูลรถยนต์
              {selectedCar && (
                <span className="font-medium">
                  {" "}
                  "{selectedCar.buyin?.brand} {selectedCar.buyin?.model} - {selectedCar.stockInfo.index_number}"
                </span>
              )}{" "}
              จะถูกลบออกจากระบบอย่างถาวร
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="thaifont" disabled={isActionLoading}>
              ยกเลิก
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteCar}
              className="bg-red-500 hover:bg-red-600 thaifont"
              disabled={isActionLoading}
            >
              {isActionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  กำลังลบ...
                </>
              ) : (
                "ลบ"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
