{"name": "autocar-backend", "version": "1.0.0", "description": "Backend for Autocar Application", "main": "dist/server.js", "scripts": {"build": "npx tsc", "start": "ts-node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "lint": "eslint . --ext .ts", "test": "jest"}, "engines": {"node": ">=18.x"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "luxon": "^3.6.1", "mongoose": "^7.4.1", "morgan": "^1.10.0", "uuid": "^11.1.0", "winston": "^3.10.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/luxon": "^3.6.2", "@types/morgan": "^1.9.4", "@types/node": "^20.4.5", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0", "jest": "^29.6.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}