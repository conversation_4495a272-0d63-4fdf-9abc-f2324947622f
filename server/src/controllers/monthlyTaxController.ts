import { Request, Response } from "express"
import MonthlyTaxService from "../services/monthlyTaxService"
import { MonthlyTaxParams } from "../models/MonthlyTax"

class MonthlyTaxController {
  private monthlyTaxService: MonthlyTaxService

  constructor(monthlyTaxService: MonthlyTaxService) {
    this.monthlyTaxService = monthlyTaxService
  }

  async getMonthlyTaxData(req: Request, res: Response) {
    try {
      const { month, year } = req.query

      // Validate required parameters
      if (!month || !year) {
        return res.status(400).json({ error: 'Month and year are required parameters' })
      }

      // Parse parameters
      const params: MonthlyTaxParams = {
        month: parseInt(month as string),
        year: parseInt(year as string)
      }

      // Validate parameter values
      if (isNaN(params.month) || params.month < 1 || params.month > 12) {
        return res.status(400).json({ error: 'Month must be a number between 1 and 12' })
      }


      const data = await this.monthlyTaxService.getMonthlyTaxData(params)
      return res.status(200).json(data)
    } catch (error: any) {
      console.error("[MonthlyTaxController.getMonthlyTaxData] ❌ Error:", error.message)
      return res.status(500).json({ error: error.message || 'Failed to fetch monthly tax data' })
    }
  }
}

export default MonthlyTaxController
