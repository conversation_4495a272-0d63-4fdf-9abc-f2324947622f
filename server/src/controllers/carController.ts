import { Request, Response } from "express";
import CarService from "../services/carService";
import { getBangkokISO, generateUUID, normalizeDecimalPrecision } from "../utils/helper";
import { TableBuckets } from "../models/Car";

class CarController {
    private carService: CarService

    constructor(carService: CarService) {
        this.carService = carService
    }

    async getAllItems(req: Request, res: Response) {
        try {
            const items = await this.carService.getAllItems()
            return res.status(200).json(items)
        } catch (error: any) {
            return res.status(500).json({ error: error.message || 'Failed to fetch car data' })
        }
    }

    async createItem(req: Request, res: Response) {
        const data = req.body
        if (!data || typeof data !== 'object') {
            return res.status(400).json({ error: "Invalid request payload" })
        }
        try {
            const timestamp = getBangkokISO()
            const car_id = generateUUID()
            // === Step 1: Map flat payload into structured TableBuckets ===
            const bucket: TableBuckets = {
                car_stock_info: {
                    car_id,
                    created_at: timestamp,
                    updated_at: timestamp,
                    is_auction_car: data.is_auction_car === 'Personal car' ? false : true,
                    registration_book_received_date: null, // optional
                    old_license_plate: data.old_license_plate || null,
                    new_license_plate: data.new_license_plate || null,
                    registration_date: data.registration_date || null,
                    total_investment: data.total_investment || null,
                    listed_price: data.listed_price || null,
                    car_status: 'transfer',
                    notes: data.notes || "",
                },
                car_buyin: {
                    car_id,
                    created_at: timestamp,
                    updated_at: timestamp,
                    purchase_date: data.purchase_date || null,
                    parking_location: data.parking_location || null,
                    transport_1_auction_lot: data.transport_1_auction_lot || 0,
                    transport_2_personal_payment: data.transport_2_personal_payment || 0,
                    transport_3_tl_payment: data.transport_3_tl_payment || 0,
                    qc1_auction_lot: data.qc1_auction_lot || 0,
                    initial_check: data.initial_check || 0,
                    ems_registration_qc3: data.ems_registration_qc3 || 0,
                    qc3: data.qc3 || 0,
                    registration_fee: data.registration_fee || 0,
                    brand: data.brand || '',
                    model: data.model || '',
                    color: data.color || '',
                    year: data.year || null,
                    vat_percent: data.vat_percent || null,
                    purchase_price: data.purchase_price || 0,
                    purchase_vat_percent: data.purchase_vat_percent || 0,
                    operation_cost_incl_vat: data.operation_cost_incl_vat || 0,
                    tax_insurance_cost_zero: data.tax_insurance_cost_zero || 0,
                    other_costs_seven: data.other_costs_seven || 0,
                    five_three_tax_percentage: data.five_three_tax_percentage || 0,
                    total_purchase_cost: data.total_purchase_cost || 0,
                    auction_location: data.auction_name || '',
                    auction_provinced: data.auction_provinced || '',
                    auction_order: data.auction_order || '',
                    auction_checker: data.auction_checker || '',
                    transport_personal: data.type_of_transport === 'Personal' ? data.auction_transporter : "",
                    transport_company: data.type_of_transport !== 'Personal' ? data.auction_transporter : "", // not present in payload
                    tank_number: data.tank_number || '',
                    engine_number: data.engine_number || '',
                    book_deposit: data.book_deposit || 0,
                    type_of_transport: data.type_of_transport || '',
                },
                car_repair: {},
                car_finance: {},
                car_sellout: {}
            }

            // MUTATES BUCKET
            normalizeDecimalPrecision(bucket)
            this.carService.createItems(bucket)
            return res.status(200).json({ message: "success" })

            // CALL REPO
        } catch (error: any) {
            return res.status(500).json({ error: error.message || 'Failed to post car data' })

        }
    }

    // BY ID
    async deleteItem(req: Request, res: Response) {
        const { id } = req.params

        if (!id) {
            return res.status(400).json({ error: 'Missing car ID in request.' })
        }
        try {
            this.carService.deleteItem(id)
            return res.status(200).json({ message: "success " })

        } catch (error: any) {
            return res.status(500).json({ error: error.message || 'Failed to delete car data' })
        }
    }

    async updateItem(req: Request, res: Response) {
        const carId = req.params.id
        const data = req.body

        if (!carId || !data) {
            return res.status(400).json({ error: "Missing car ID or update data" })
        }

        try {
            await this.carService.updateItem(carId, data)
            return res.status(200).json({
                message: "Car updated successfully",
                carId,
            })
        } catch (error: any) {
            console.error("[CarController.updateItem] ❌ Error:", error.message)
            return res.status(500).json({ error: error.message || "Failed to update car" })
        }
    }

}

export default CarController