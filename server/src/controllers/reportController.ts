import { Request, Response } from "express";
import ReportService from "../services/reportService";
import { DailyAuctionParams } from "../models/Report";

class ReportController {
  private reportService: ReportService;

  constructor(reportService: ReportService) {
    this.reportService = reportService;
  }

  async getDailyAuctionSummary(req: Request, res: Response) {
    try {
      const { date, location, province } = req.query;
      
      if (!date) {
        return res.status(400).json({ error: 'Date parameter is required' });
      }
      
      const params: DailyAuctionParams = {
        date: date as string,
        location: location as string | undefined,
        province: province as string | undefined
      };
      
      const auctionData = await this.reportService.getDailyAuctionSummary(params);
      
      return res.status(200).json(auctionData);
    } catch (error: any) {
      console.error("[ReportController.getDailyAuctionSummary] ❌ Error:", error.message);
      return res.status(500).json({ error: error.message || 'Failed to fetch auction data' });
    }
  }
}

export default ReportController;
