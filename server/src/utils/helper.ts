import { v4 as uuidv4 } from 'uuid'

export function getBangkokISO(): string {
    // Thailand is UTC+7
    const date = new Date();
    // Convert to Thailand time
    const thailandOffset = 7 * 60; // minutes
    const localOffset = date.getTimezoneOffset(); // minutes
    const diff = thailandOffset + localOffset;
    const thailandDate = new Date(date.getTime() + diff * 60 * 1000);

    const pad = (n: number, width: number = 2) => n.toString().padStart(width, '0');
    const year = thailandDate.getFullYear();
    const month = pad(thailandDate.getMonth() + 1);
    const day = pad(thailandDate.getDate());
    const hour = pad(thailandDate.getHours());
    const minute = pad(thailandDate.getMinutes());
    const second = pad(thailandDate.getSeconds());
    const ms = pad(thailandDate.getMilliseconds(), 3);

    // Format as 'YYYY-MM-DDTHH:mm:ss.SSS+07:00' (ISO format with Thailand timezone)
    return `${year}-${month}-${day}T${hour}:${minute}:${second}.${ms}+07:00`;
}

export function generateUUID(): string {
    return uuidv4()
}


// FRONTEND TO BACKEND
export function normalizeDecimalPrecision(bucket: Record<string, any>): void {
    for (const tableKey in bucket) {
        const tableData = bucket[tableKey]
        if (!tableData || typeof tableData !== 'object') continue

        for (const key in tableData) {
            const value = tableData[key]
            if (typeof value === 'number' && !isNaN(value)) {
                tableData[key] = parseFloat(value.toFixed(2))
            }
        }
    }
}

// BACKEND TO FRONTEND
export function preprocessDatabaseData(obj: Record<string, any>): Record<string, any> {
    const cloned = { ...obj }

    for (const key in cloned) {
        const value = cloned[key]

        // Convert all ISO date strings to Buddhist calendar format
        if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
            try {
                const date = new Date(value)
                if (!isNaN(date.getTime())) {
                    const buddhistYear = date.getFullYear()
                    const formatted = `${buddhistYear}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
                        date.getDate()
                    ).padStart(2, '0')}`
                    cloned[key] = formatted
                }
            } catch (err) {
                // If conversion fails, keep original value
                console.warn(`⚠️ Failed to convert date on key "${key}":`, value)
            }
        }

        // Handle is_auction_car conversion
        if (key === 'is_auction_car') {
            cloned[key] = value ? 'Company car' : 'Personal car'
        }
    }

    return cloned
}
