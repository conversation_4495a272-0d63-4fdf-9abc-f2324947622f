// middleware/auth.ts
import { Request, Response, NextFunction } from 'express'
import { supabase } from '../config/database'

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Unauthorized: Missing token' })
    }

    const rawToken = authHeader.split(' ')[1]
    const token = rawToken.replace(/^"|"$/g, '')

    const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('auth_token', token)
        .single()

    if (error || !data) {
        return res.status(401).json({ error: 'Unauthorized: Invalid token' })
    }

    // ✅ Attach user data to request
    (req as any).user = data
    next()
}
