import { supabase } from "../config/database";
import { DailyAuctionParams, AuctionItem, AuctionProvince, AuctionLocation } from "../models/Report";
import { getBangkokISO } from "../utils/helper";

class ReportRepository {
  async getDailyAuctionSummary(params: DailyAuctionParams): Promise<AuctionLocation[]> {
    const { date, location, province } = params;
    
    // Format date for comparison (YYYY-MM-DD)
    const formattedDate = date.split('T')[0];
    
    // Query cars purchased on the specified date
    let query = supabase
      .from('car_stock_info')
      .select(`
        *,
        car_buyin(*)
      `)
      .eq('car_buyin.purchase_date', formattedDate);
    
    // Apply additional filters if provided
    if (location) {
      query = query.eq('car_buyin.auction_name', location);
    }
    
    if (province) {
      query = query.eq('car_buyin.auction_provinced', province);
    }
    
    const { data: cars, error } = await query;
    
    if (error) {
      console.error("[ReportRepository.getDailyAuctionSummary] ❌ Error:", error.message);
      throw new Error(`Error fetching auction data: ${error.message}`);
    }
    
    // Group cars by auction location and province
    const locationMap = new Map<string, Map<string, AuctionItem[]>>();
    
    cars.forEach((car) => {
      if (!car.car_buyin) return;
      
      const location = car.car_buyin.auction_location || "Unknown Location";
      const province = car.car_buyin.auction_provinced || "Unknown Province";
      
      if (!locationMap.has(location)) {
        locationMap.set(location, new Map<string, AuctionItem[]>());
      }
      
      const provinceMap = locationMap.get(location)!;
      if (!provinceMap.has(province)) {
        provinceMap.set(province, []);
      }
      
      provinceMap.get(province)!.push({
        id: car.car_id,
        indexNumber: car.index_number || "",
        auctionOrder: car.car_buyin.auction_order || "",
        brand: car.car_buyin.brand || "",
        model: car.car_buyin.model || "",
        color: car.car_buyin.color || "",
        year: car.car_buyin.year || 0,
        tankNumber: car.car_buyin.tank_number || "",
        engineNumber: car.car_buyin.engine_number || "",
        purchasePrice: car.car_buyin.purchase_price || 0,
        operationCost: car.car_buyin.operation_cost_incl_vat || 0,
        transportCost: car.car_buyin.transport_1_auction_lot || 0,
        otherCosts: car.car_buyin.other_costs_seven || 0,
      });
    });
    
    // Convert the nested maps to the desired structure
    const groupedData: AuctionLocation[] = [];
    
    locationMap.forEach((provinceMap, location) => {
      const provinces: AuctionProvince[] = [];
      
      provinceMap.forEach((items, province) => {
        provinces.push({ province, items });
      });
      
      groupedData.push({ location, provinces });
    });
    
    return groupedData;
  }
}

export default ReportRepository;
