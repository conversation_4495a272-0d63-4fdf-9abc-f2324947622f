import { supabase } from '../config/database'
import { TableBuckets } from '../models/Car'
import { preprocessDatabaseData } from '../utils/helper'

// Helper function to check if payload only contains updated_at
const hasOnlyUpdatedAt = (payload: any): boolean => {
    if (!payload) return true

    const keys = Object.keys(payload)
    if (keys.length === 0) return true
    if (keys.length === 1 && keys[0] === 'updated_at') return true
    if (keys.length === 2 && keys.includes('updated_at') && keys.includes('car_id')) return true

    return false
}

class CarRepository {
    async getAllItems(): Promise<any[]> {
        const { data: stockData, error } = await supabase
            .from('car_stock_info')
            .select(`
          *,
          car_buyin(*),
          car_repair(*),
          car_finance(*),
          car_sellout(*)
        `)

        if (error) {
            throw new Error(`Error fetching data: ${error.message}`)
        }

        // Map structure to match the required format
        const result = stockData.map((row: any) => {
            const { car_buyin, car_repair, car_finance, car_sellout, ...stockInfo } = row

            // PREPROCESS FE TO BE
            const normalizedStockInfo = preprocessDatabaseData(stockInfo)
            const normalizedBuyin = car_buyin ? preprocessDatabaseData(car_buyin) : null
            const normalizedRepair = car_repair ? preprocessDatabaseData(car_repair) : null
            const normalizedFinance = car_finance ? preprocessDatabaseData(car_finance) : null
            const normalizedSellout = car_sellout ? preprocessDatabaseData(car_sellout) : null

            return {
                stockInfo: normalizedStockInfo,
                buyin: {
                    ...normalizedBuyin,
                    ems_registration: normalizedBuyin?.ems_registration || 0,
                    qc3: normalizedBuyin?.qc3 || 0,
                    gear: normalizedBuyin?.gear || null
                },
                repair: normalizedRepair,
                finance: normalizedFinance,
                sellout: normalizedSellout
            }

        })

        return result
    }

    async createItems(bucket: TableBuckets): Promise<void> {
        const insertedTables: { table: string; id: string }[] = []

        // Verify car_id is present in stockInfo
        if (!bucket.car_stock_info || !bucket.car_stock_info.car_id) {
            throw new Error("car_stock_info is missing or doesn't have car_id")
        }

        const carId = bucket.car_stock_info.car_id

        // Fix any dates with abnormally large years
        const fixedStockInfo = this.fixDateFields(bucket.car_stock_info);
        const fixedBuyin = this.fixDateFields(bucket.car_buyin);
        const fixedRepair = this.fixDateFields(bucket.car_repair);
        const fixedFinance = this.fixDateFields(bucket.car_finance);
        const fixedSellout = this.fixDateFields(bucket.car_sellout);

        // Log the fixed data for debugging
        console.log("Using fixed date fields for create:");
        console.log("- Fixed stockInfo:", fixedStockInfo !== bucket.car_stock_info ? "Changes applied" : "No changes needed");
        console.log("- Fixed buyin:", fixedBuyin !== bucket.car_buyin ? "Changes applied" : "No changes needed");
        console.log("- Fixed repair:", fixedRepair !== bucket.car_repair ? "Changes applied" : "No changes needed");
        console.log("- Fixed finance:", fixedFinance !== bucket.car_finance ? "Changes applied" : "No changes needed");
        console.log("- Fixed sellout:", fixedSellout !== bucket.car_sellout ? "Changes applied" : "No changes needed");

        const insertAndCheck = async (table: keyof TableBuckets, payload: any) => {
            if (!payload || Object.keys(payload).length === 0 || hasOnlyUpdatedAt(payload)) {
                return
            }

            // Ensure car_id is in the payload
            payload.car_id = carId

            const { error } = await supabase.from(table).insert([payload])
            if (error) {
                throw new Error(`${table} insert failed: ${error.message}`)
            }

            insertedTables.push({ table, id: payload.car_id })
        }

        try {
            // Insert car_stock_info first using fixed data
            await insertAndCheck('car_stock_info', { ...fixedStockInfo, car_id: carId })

            // Prepare and filter payloads using fixed data
            const payloads = [
                { table: 'car_buyin' as keyof TableBuckets, data: fixedBuyin },
                { table: 'car_repair' as keyof TableBuckets, data: fixedRepair },
                { table: 'car_finance' as keyof TableBuckets, data: fixedFinance },
                { table: 'car_sellout' as keyof TableBuckets, data: fixedSellout }
            ].filter(item => item.data && !hasOnlyUpdatedAt(item.data))

            // Process filtered payloads
            for (const { table, data } of payloads) {
                await insertAndCheck(table, { ...data, car_id: carId })
            }
        } catch (err: any) {
            console.error("❌ Error during create:", err.message)

            // Rollback logic
            if (insertedTables.length > 0) {
                for (const { table, id } of insertedTables.reverse()) {
                    await supabase.from(table).delete().eq('car_id', id)
                }
            }

            throw err
        }
    }

    // BY /:id
    async deleteItem(carId: string): Promise<void> {
        const tables = [
            'car_sellout',
            'car_finance',
            'car_repair',
            'car_buyin',
            'car_stock_info'
        ]

        for (const table of tables) {
            const { error } = await supabase
                .from(table)
                .delete()
                .eq('car_id', carId)

            if (error) {
                throw new Error(`Failed to delete from ${table}: ${error.message}`)
            }
        }
    }
    async recordExists(table: string, carId: string): Promise<boolean> {
        const { data, error } = await supabase
            .from(table)
            .select("car_id")
            .eq("car_id", carId)
            .maybeSingle()

        if (error) {
            console.error(`[CarRepository.updateItem] ❌ Error checking ${table}:`, error.message)
            throw error
        }

        return !!data
    }
    private fixDateFields(obj: any): any {
        if (!obj) return obj;

        const result = { ...obj };
        const dateFields = [
            'registration_date',
            'registration_book_received_date',
            'purchase_date',
            'transfer_date',
            'repair_date',
            'finance_request_date',
            'finance_received_date',
            'car_tax_invoice_date',
            'commission_tax_invoice_date',
            'sale_date'
        ];

        for (const field of dateFields) {
            if (result[field] && typeof result[field] === 'string') {
                // Check if the date has an abnormally large year (> 3000)
                const year = parseInt(result[field].substring(0, 4));
                if (year > 3000) {
                    // Fix the double-converted year by subtracting 543
                    const correctedYear = year - 543;
                    result[field] = correctedYear + result[field].substring(4);
                    console.log(`Fixed double-converted date in ${field}: ${obj[field]} -> ${result[field]}`);
                }
            }
        }

        return result;
    }

    async updateItem(carId: string, data: any): Promise<void> {
        const { stockInfo, buyin, repair, finance, sellout } = data
        console.log("Finance information", finance)

        // Fix any dates with abnormally large years
        const fixedStockInfo = this.fixDateFields(stockInfo);
        const fixedBuyin = this.fixDateFields(buyin);
        const fixedRepair = this.fixDateFields(repair);
        const fixedFinance = this.fixDateFields(finance);
        const fixedSellout = this.fixDateFields(sellout);

        // Log the fixed data for debugging
        try {
            // Ensure stockInfo exists
            if (!stockInfo) {
                throw new Error("stockInfo is missing")
            }

            // Update car_stock_info with car_id using the fixed data
            const { error: stockError } = await supabase
                .from("car_stock_info")
                .update({ ...fixedStockInfo, car_id: carId })
                .eq("car_id", carId)

            if (stockError) throw new Error(`Failed to update stockInfo: ${stockError.message}`)

            // Prepare payloads with car_id and filter out empty ones using the fixed data
            const payloads = [
                { table: "car_buyin", data: buyin ? { ...fixedBuyin, car_id: carId } : null },
                { table: "car_repair", data: repair ? { ...fixedRepair, car_id: carId } : null },
                { table: "car_finance", data: finance ? { ...fixedFinance, car_id: carId } : null },
                { table: "car_sellout", data: sellout ? { ...fixedSellout, car_id: carId } : null }
            ].filter(item => item.data && !hasOnlyUpdatedAt(item.data))

            console.log("Check paylods", payloads)

            // Upsert each table
            const upsertTable = async (table: string, payload: any) => {
                const { data: existingRow, error: checkError } = await supabase
                    .from(table)
                    .select("car_id")
                    .eq("car_id", carId)
                    .maybeSingle()

                if (checkError) throw new Error(`Error checking ${table}: ${checkError.message}`)

                if (!existingRow) {
                    const { error: insertError } = await supabase.from(table).insert(payload)
                    if (insertError) throw new Error(`Failed to insert into ${table}: ${insertError.message}`)
                } else {
                    // Problem is here, debug
                    const { error: updateError } = await supabase
                        .from(table)
                        .update(payload)
                        .eq("car_id", carId)
                    if (updateError) throw new Error(`Failed to update ${table}: ${updateError.message}`)
                }
            }

            // Process all payloads
            for (const { table, data } of payloads) {
                await upsertTable(table, data)
            }
        } catch (error: any) {
            console.error("[CarRepository.updateItem] ❌ Error:", error.message)
            throw error
        }
    }
}

export default CarRepository
