import { supabase } from '../config/database'

export const testDatabaseConnection = async () => {
    const { data, error } = await supabase
        .from('car_stock_info')  // Use a small, always-present table
        .select('car_id')
        .limit(1)

    if (error) {
        console.error('❌ Supabase connection failed:', error.message)
        return false
    }

    console.log('✅ Supabase connected successfully!')
    return true
}
