import { supabase } from "../config/database"

class AuthRepository {
    async checkUserCredentials(username: string, password: string): Promise<any> {
        const fakeEmail = `${username}@sp-autocar.fakeemail.com`

        const { data, error } = await supabase.auth.signInWithPassword({
            email: fakeEmail,
            password,
        })

        if (error) {
            throw new Error(`Invalid credentials: ${error.message}`)
        }

        const userId = data.user?.id
        if (!userId) {
            throw new Error("User ID not found after login")
        }

        // 🔥 FIXED: Lookup by user ID (not email)
        const { data: profile, error: profileError } = await supabase
            .from("profiles")
            .select("auth_token")
            .eq("id", userId)
            .single()

        if (profileError || !profile) {
            throw new Error(`Could not retrieve auth_token: ${profileError?.message || "Unknown error"}`)
        }

        return {
            user: data.user,
            session: data.session,
            auth_token: profile.auth_token,
        }
    }
}

export default AuthRepository
