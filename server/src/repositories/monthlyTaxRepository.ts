import { supabase } from '../config/database'
import { MonthlyTaxParams } from '../models/MonthlyTax'

// Define interfaces for the data we're working with
interface StockInfo {
  car_id: string
  index_number: string
  car_status: string
  old_license_plate?: string
  new_license_plate?: string
}



interface FinanceInfo {
  car_id: string
  finance_received_date: string | null
  car_tax_invoice_date: string | null
  car_tax_invoice_number: string | null
  car_amount: number | null
  car_vat_amount: number | null
  commission_tax_invoice_date: string | null
  commission_tax_invoice_number: string | null
  car_commission_amount: number | null
  input_vat_commission: number | null
  withholding_tax: number | null
  salesperson: string | null
  bank: string | null
  marketing_person: string | null
  promotion_customer: number | null
  bonus_insurance_car_life_engine: number | null
  customer_name: string | null
  customer_address_or_advance_payment: string | null
  down_payment: number | null
  loan_protection_insurance: number | null
  accident_insurance: number | null
  car_insurance: number | null
  bank_documents: number | null
  // These will be added from other tables
  index_number?: string
  car_status?: string
  old_license_plate?: string
  new_license_plate?: string
  tank_number?: string
  engine_number?: string
  sale_date?: string
  vat_percent?: number
  brand?: string
  model?: string
  color?: string
  year?: number
  auction_location?: string
  owner_name?: string
  customer_address?: string
  actual_selling_price?: number
  commission_s?: number
  commission_agent?: number
  commission_manager?: number
  sales_channel?: string
}

class MonthlyTaxRepository {
  async getPurchaseTaxData(params: MonthlyTaxParams) {
    const { month, year } = params

    // Create date range for the selected month
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`

    // Calculate the last day of the month correctly
    const nextMonth = month === 12 ? 1 : month + 1
    const nextYear = month === 12 ? year + 1 : year
    const lastDay = new Date(nextYear, nextMonth - 1, 0).getDate()
    const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`

    // First, get car_buyin records that match the date criteria
    const { data: buyinData, error: buyinError } = await supabase
      .from('car_buyin')
      .select(`
        car_id,
        purchase_date,
        brand,
        model,
        color,
        year,
        vat_percent,
        purchase_price,
        purchase_vat_percent,
        operation_cost_incl_vat,
        transport_1_auction_lot,
        initial_check,
        tax_insurance_cost_zero,
        other_costs_seven,
        five_three_tax_percentage,
        tank_number,
        auction_location
      `)
      .gte('purchase_date', startDate)
      .lte('purchase_date', endDate)
      .not('purchase_date', 'is', null)

    if (buyinError) {
      console.error("[MonthlyTaxRepository.getPurchaseTaxData] ❌ Error fetching buyin data:", buyinError.message)
      throw buyinError
    }

    if (!buyinData || buyinData.length === 0) {
      console.log("[MonthlyTaxRepository.getPurchaseTaxData] ⚠️ No purchase data found for the specified period")
      return []
    }

    // Get the car_ids from the filtered buyin data
    const carIds = buyinData.map(item => item.car_id)

    // Now get the corresponding stock info for these cars
    const { data: stockData, error: stockError } = await supabase
      .from('car_stock_info')
      .select(`
        car_id,
        index_number,
        car_status,
        old_license_plate
      `)
      .in('car_id', carIds)

    if (stockError) {
      console.error("[MonthlyTaxRepository.getPurchaseTaxData] ❌ Error fetching stock data:", stockError.message)
      throw stockError
    }

    // Create a map of car_id to stock info for efficient lookup
    const stockMap: Record<string, any> = {}
    stockData?.forEach(stock => {
      stockMap[stock.car_id] = stock
    })

    // Combine the data
    const combinedData = buyinData.map(buyin => {
      const stock = stockMap[buyin.car_id]
      return {
        car_id: buyin.car_id,
        index_number: stock?.index_number || '',
        car_status: stock?.car_status || '',
        old_license_plate: stock?.old_license_plate || '',
        car_buyin: buyin
      }
    })

    return combinedData
  }

  async getTaxInvoiceData(params: MonthlyTaxParams) {
    const { month, year } = params

    // Create date range for the selected month
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`

    // Calculate the last day of the month correctly
    const nextMonth = month === 12 ? 1 : month + 1
    const nextYear = month === 12 ? year + 1 : year
    const lastDay = new Date(nextYear, nextMonth - 1, 0).getDate()
    const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`

    console.log(`[MonthlyTaxRepository.getTaxInvoiceData] 📅 Querying date range: ${startDate} to ${endDate}`)

    // Query 1: Get finance data based on car_tax_invoice_date
    const { data: taxInvoiceData, error: taxInvoiceError } = await supabase
      .from('car_finance')
      .select(`
        car_id,
        finance_received_date,
        car_tax_invoice_date,
        car_tax_invoice_number,
        car_amount,
        car_vat_amount,
        commission_tax_invoice_date,
        commission_tax_invoice_number,
        car_commission_amount,
        input_vat_commission,
        withholding_tax,
        bank
      `)
      .gte('car_tax_invoice_date', startDate)
      .lte('car_tax_invoice_date', endDate)
      .not('car_tax_invoice_date', 'is', null)

    if (taxInvoiceError) {
      console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error fetching tax invoice data:", taxInvoiceError.message)
      throw taxInvoiceError
    }

    // Query 2: Get finance data based on finance_received_date
    const { data: financeReceivedData, error: financeReceivedError } = await supabase
      .from('car_finance')
      .select(`
        car_id,
        finance_received_date,
        car_tax_invoice_date,
        car_tax_invoice_number,
        car_amount,
        car_vat_amount,
        commission_tax_invoice_date,
        commission_tax_invoice_number,
        car_commission_amount,
        input_vat_commission,
        withholding_tax,
        bank
      `)
      .gte('commission_tax_invoice_date', startDate)
      .lte('commission_tax_invoice_date', endDate)
      .not('commission_tax_invoice_date', 'is', null)

    if (financeReceivedError) {
      console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error fetching finance received data:", financeReceivedError.message)
      throw financeReceivedError
    }

    // Union the results based on car_id (remove duplicates)
    const carIdMap = new Map<string, FinanceInfo>()

    // Add tax invoice data first
    if (taxInvoiceData) {
      taxInvoiceData.forEach(item => {
        carIdMap.set(item.car_id, item as FinanceInfo)
      })
    }

    // Add finance received data, but don't overwrite existing entries
    if (financeReceivedData) {
      financeReceivedData.forEach(item => {
        if (!carIdMap.has(item.car_id)) {
          carIdMap.set(item.car_id, item as FinanceInfo)
        }
      })
    }

    // Convert map back to array
    const data: FinanceInfo[] = Array.from(carIdMap.values())

    // If we have finance data, fetch the related stock info, buyin, and sellout data
    if (data && data.length > 0) {
      const carIds = data.map(item => item.car_id);

      // Get stock info
      const { data: stockData, error: stockError } = await supabase
        .from('car_stock_info')
        .select('car_id, index_number, car_status, old_license_plate, new_license_plate')
        .in('car_id', carIds);

      if (stockError) {
        console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error fetching stock data:", stockError.message);
      } else if (stockData) {
        // Create a map of car_id to stock info
        const stockMap: Record<string, StockInfo> = {};
        stockData.forEach(item => {
          stockMap[item.car_id] = item as StockInfo;
        });

        // Add stock info to each finance record
        data.forEach(item => {
          const stockInfo = stockMap[item.car_id];
          if (stockInfo) {
            item.index_number = stockInfo.index_number;
            item.car_status = stockInfo.car_status;
            item.old_license_plate = stockInfo.old_license_plate;
            item.new_license_plate = stockInfo.new_license_plate;
          }
        });
      }

      // Get buyin data (for tank_number and vat_percent)
      const { data: buyinData, error: buyinError } = await supabase
        .from('car_buyin')
        .select('car_id, tank_number, engine_number ,vat_percent, brand, model, color, year ')
        .in('car_id', carIds);

      if (buyinError) {
        console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error fetching buyin data:", buyinError.message);
      } else if (buyinData) {
        // Create a map of car_id to buyin info
        const buyinMap: Record<string, any> = {};
        buyinData.forEach(item => {
          buyinMap[item.car_id] = item;
        });

        // Add buyin info to each finance record
        data.forEach(item => {
          const buyinInfo = buyinMap[item.car_id];
          if (buyinInfo) {
            item.tank_number = buyinInfo.tank_number || '';
            item.engine_number = buyinInfo.engine_number || '';
            item.vat_percent = buyinInfo.vat_percent || 7; // Default to 7% if not specified
            item.brand = buyinInfo.brand || '';
            item.model = buyinInfo.model || '';
            item.color = buyinInfo.color || '';
            item.year = buyinInfo.year || 0;
          }
        });
      }

      // Get sellout data (for sale_date and other sellout fields)
      const { data: selloutData, error: selloutError } = await supabase
        .from('car_sellout')
        .select(`
          car_id,
          sale_date,
          owner_name,
          customer_address,
          actual_selling_price,
          commission_s,
          commission_agent,
          commission_manager,
          sales_channel
        `)
        .in('car_id', carIds);

      if (selloutError) {
        console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error fetching sellout data:", selloutError.message);
      } else if (selloutData) {
        // Create a map of car_id to sellout info
        const selloutMap: Record<string, any> = {};
        selloutData.forEach(item => {
          selloutMap[item.car_id] = item;
        });

        // Add sellout info to each finance record
        data.forEach(item => {
          const selloutInfo = selloutMap[item.car_id];
          if (selloutInfo) {
            item.sale_date = selloutInfo.sale_date || '';
            item.owner_name = selloutInfo.owner_name || '';
            item.customer_address = selloutInfo.customer_address || '';
            item.actual_selling_price = selloutInfo.actual_selling_price || 0;
            item.commission_s = selloutInfo.commission_s || 0;
            item.commission_agent = selloutInfo.commission_agent || 0;
            item.commission_manager = selloutInfo.commission_manager || 0;
            item.sales_channel = selloutInfo.sales_channel || '';
          }
        });
      }
    }

    // Filter to only include sold cars and ensure we have valid data
    const filteredData = data?.filter(item => {
      const isSold = item.car_status === 'sold'
      const hasValidData = item.car_amount !== null || item.car_commission_amount !== null
      return isSold && hasValidData
    }) || [];

    return filteredData;
  }
}

export default MonthlyTaxRepository
