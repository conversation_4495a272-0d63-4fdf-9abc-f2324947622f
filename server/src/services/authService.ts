import AuthRepository from '../repositories/authRepository'

class AuthService {
    private authRepository: AuthRepository

    constructor(authRepository: AuthRepository) {
        this.authRepository = authRepository
    }

    async checkUserCredentials(username: string, password: string): Promise<any> {
        return this.authRepository.checkUserCredentials(username, password)
    }
}

export default AuthService