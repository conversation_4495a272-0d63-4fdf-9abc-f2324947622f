import ReportRepository from "../repositories/reportRepository";
import { DailyAuctionParams, AuctionLocation, DailyAuctionResponse } from "../models/Report";

class ReportService {
  private reportRepository: ReportRepository;

  constructor(reportRepository: ReportRepository) {
    this.reportRepository = reportRepository;
  }

  async getDailyAuctionSummary(params: DailyAuctionParams): Promise<DailyAuctionResponse> {
    try {
      const locations = await this.reportRepository.getDailyAuctionSummary(params);
      
      return {
        date: params.date,
        locations
      };
    } catch (error: any) {
      console.error("[ReportService.getDailyAuctionSummary] ❌ Error:", error.message);
      throw error;
    }
  }
}

export default ReportService;
