import CarRepository from "../repositories/carRepository";
import { TableBuckets } from "../models/Car";

class CarService {
    private carRepository: CarRepository

    constructor(carRepository: CarRepository) {
        this.carRepository = carRepository
    }

    async getAllItems() {
        return this.carRepository.getAllItems()
    }
    async createItems(dataBuckets: TableBuckets) {
        return this.carRepository.createItems(dataBuckets)
    }

    // BY /:id
    async deleteItem(carId: string) {
        return this.carRepository.deleteItem(carId)
    }

    async updateItem(carId: string ,data: any) {
        return this.carRepository.updateItem(carId, data)
    }

}

export default CarService