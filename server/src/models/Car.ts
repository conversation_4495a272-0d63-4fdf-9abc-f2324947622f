import { CarBuyin } from '../models/CarBuyin'
import { CarRepair } from '../models/CarRepair'
import { CarFinance } from '../models/CarFinance'
import { CarSellout } from '../models/CarSellout'

export interface BaseModel {
    car_id: string
    created_at: string
    updated_at: string
}

export interface CarStockInfo extends BaseModel {
    car_id: string                      // UUID
    index_number: number               // SERIAL (auto-incremented)
    is_auction_car: boolean | null     // nullable boolean
    registration_book_received_date: string | null // 'YYYY-MM-DD' date string
    old_license_plate: string | null
    new_license_plate: string | null
    registration_date: string | null   // 'YYYY-MM-DD' date string
    total_investment: number | null
    listed_price: number | null
    car_status:
    | 'purchase'
    | 'transfer'
    | 'repair'
    | 'available'
    | 'finance_request'
    | 'finance_done'
    | 'sold'
    | 'reserved'
    notes: string | null
}

export type TableBuckets = {
    car_stock_info: Partial<CarStockInfo>
    car_buyin: Partial<CarBuyin>
    car_repair: Partial<CarRepair>
    car_finance: Partial<CarFinance>
    car_sellout: Partial<CarSellout>
}