export interface BaseModel {
    car_id: string
    created_at: string
    updated_at: string
}


// car_repair table

export interface CarRepair extends BaseModel {
    car_id: string                         // UUID
    repainting_cost: number | null
    engine_repair_cost: number | null
    suspension_repair_cost: number | null
    autopart_cost: number | null
    battery_cost: number | null
    tires_wheels_cost: number | null
}