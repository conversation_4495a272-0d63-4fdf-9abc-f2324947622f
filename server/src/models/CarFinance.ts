export interface BaseModel {
    car_id: string
    created_at: string
    updated_at: string
}

export interface CarFinance extends BaseModel {
    car_id: string                                     // UUID
    finance_received_date: string | null               // 'YYYY-MM-DD'
    car_tax_invoice_date: string | null
    car_tax_invoice_number: string | null
    car_amount: number | null
    car_vat_amount: number | null
    commission_tax_invoice_date: string | null
    commission_tax_invoice_number: string | null
    car_commission_amount: number | null
    input_vat_commission: number | null
    withholding_tax: number | null
    salesperson: string | null
    bank: string | null
    marketing_person: string | null
    promotion_customer: number | null
    bonus_insurance_car_life_engine: number | null
    customer_name: string | null
    customer_address_or_advance_payment: string | null
    down_payment: number | null
    loan_protection_insurance: number | null
    accident_insurance: number | null
    car_insurance: number | null
    bank_documents: number | null
}
