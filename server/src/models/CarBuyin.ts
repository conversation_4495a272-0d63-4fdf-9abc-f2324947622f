export interface BaseModel {
    car_id: string
    created_at: string
    updated_at: string
}

// car_buyin table
export interface CarBuyin extends BaseModel {
    car_id: string                             // UUID
    purchase_date: string | null               // 'YYYY-MM-DD'
    parking_location: string | null
    transport_1_auction_lot: number | null
    transport_2_personal_payment: number | null
    transport_3_tl_payment: number | null
    qc1_auction_lot: number | null
    initial_check: number | null
    ems_registration_qc3: number | null
    qc3: number | null
    registration_fee: number | null
    brand: string | null
    model: string | null
    color: string | null
    year: number | null
    vat_percent: number | null                // numeric(5, 2)
    purchase_price: number | null
    purchase_vat_percent: number | null       // numeric(10, 2)
    operation_cost_incl_vat: number | null
    tax_insurance_cost_zero: number | null
    other_costs_seven: number | null
    five_three_tax_percentage: number | null  // numeric(10, 2)
    total_purchase_cost: number | null
    auction_location: string | null
    auction_provinced: string | null
    auction_order: string | null
    auction_checker: string | null
    transport_personal: string | null
    transport_company: string | null
    tank_number: string | null
    engine_number: string | null
    book_deposit: number | null
    type_of_transport: string | null
}

