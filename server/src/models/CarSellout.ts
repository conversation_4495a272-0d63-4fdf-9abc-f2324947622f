export interface BaseModel {
    car_id: string
    created_at: string
    updated_at: string
}

export interface CarSellout extends BaseModel {
    car_id: string                               // UUID
    sale_date: string | null                     // 'YYYY-MM-DD'
    owner_name: string | null
    customer_address: string | null
    actual_selling_price: number | null
    commission_s: number | null
    commission_agent: number | null
    commission_manager: number | null
    sales_channel: string | null
}