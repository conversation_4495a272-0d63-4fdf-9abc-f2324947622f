// Monthly Tax Models

export interface MonthlyTaxParams {
  month: number
  year: number
}

export interface PurchaseTaxData {
  id: string
  indexNumber: string
  carStatus: string
  purchase_date: string
  brand: string
  model: string
  color: string
  year: number
  old_license_plate: string
  vat_percent: number
  purchase_price: number
  purchase_vat_percent: number
  operation_cost_incl_vat: number
  transport_1_auction_lot: number
  initial_check: number
  tax_insurance_cost_zero: number
  other_costs_seven: number
  five_three_tax_percentage: number
  total_purchase_cost: number
}

export interface SelloutTaxData {
  id: string
  indexNumber: string
  sale_date: string
  finance_received_date: string
  car_tax_invoice_date: string
  car_tax_invoice_number: string
  car_amount: number
  car_vat_amount: number
  commission_tax_invoice_date: string
  commission_tax_invoice_number: string
  car_commission_amount: number
  input_vat_commission: number
  withholding_tax: number
  vat_percent: number
  tank_number: string
}

export interface MonthlyTaxResponse {
  purchaseData: PurchaseTaxData[]
  selloutData: SelloutTaxData[]
}
