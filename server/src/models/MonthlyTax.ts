// Monthly Tax Models

export interface MonthlyTaxParams {
  month: number
  year: number
}

export interface PurchaseTaxData {
  id: string
  indexNumber: string
  carStatus: string
  purchase_date: string
  brand: string
  model: string
  color: string
  year: number
  old_license_plate: string
  vat_percent: number
  purchase_price: number
  purchase_vat_percent: number
  operation_cost_incl_vat: number
  transport_1_auction_lot: number
  initial_check: number
  tax_insurance_cost_zero: number
  other_costs_seven: number
  five_three_tax_percentage: number
  total_purchase_cost: number
}

export interface SelloutTaxData {
  id: string
  indexNumber: string
  sale_date: string
  finance_received_date: string
  car_tax_invoice_date: string
  car_tax_invoice_number: string
  car_amount: number
  car_vat_amount: number
  commission_tax_invoice_date: string
  commission_tax_invoice_number: string
  car_commission_amount: number
  input_vat_commission: number
  withholding_tax: number
  vat_percent: number
  tank_number: string
  // Additional fields from other tables
  old_license_plate: string
  new_license_plate: string
  engine_number: string
  brand: string
  model: string
  color: string
  year: number
  auction_location: string
  owner_name: string
  customer_address: string
  actual_selling_price: number
  commission_s: number
  commission_agent: number
  commission_manager: number
  sales_channel: string
  // Finance fields
  salesperson: string
  bank: string
  marketing_person: string
  promotion_customer: number
  bonus_insurance_car_life_engine: number
  customer_name: string
  customer_address_or_advance_payment: string
  down_payment: number
  loan_protection_insurance: number
  accident_insurance: number
  car_insurance: number
  bank_documents: number
}

export interface MonthlyTaxResponse {
  purchaseData: PurchaseTaxData[]
  selloutData: SelloutTaxData[]
}
