import express from 'express'
import AuthController from '../controllers/authController'
import AuthService from '../services/authService'
import AuthRepository from '../repositories/authRepository'

const router = express.Router()

const authService = new AuthService( new AuthRepository())
const authController = new AuthController(authService)

router.route('/login')
.post(authController.checkUserCredentials.bind(authController))

export default router
