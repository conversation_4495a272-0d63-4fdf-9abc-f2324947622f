import express from 'express'
import CarController from '../controllers/carController'
import CarService from '../services/carService'
import CarRepository from '../repositories/carRepository'
import { authMiddleware } from '../middleware/authMiddleware'

const router = express.Router()

const carSevice = new CarService( new CarRepository())
const carController = new CarController(carSevice)

router.route('/')
.get(authMiddleware, carController.getAllItems.bind(carController))
.post(authMiddleware, carController.createItem.bind(carController))

router.route('/:id')
.delete(authMiddleware, carController.deleteItem.bind(carController))
.patch(authMiddleware, carController.updateItem.bind(carController))

export default router