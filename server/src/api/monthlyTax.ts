import express from 'express'
import MonthlyTaxController from '../controllers/monthlyTaxController'
import MonthlyTaxService from '../services/monthlyTaxService'
import MonthlyTaxRepository from '../repositories/monthlyTaxRepository'
import { authMiddleware } from '../middleware/authMiddleware'

const router = express.Router()

// Initialize repository, service, and controller
const monthlyTaxRepository = new MonthlyTaxRepository()
const monthlyTaxService = new MonthlyTaxService(monthlyTaxRepository)
const monthlyTaxController = new MonthlyTaxController(monthlyTaxService)

// Monthly tax endpoint
router.route('/monthly-tax')
  .get(authMiddleware, monthlyTaxController.getMonthlyTaxData.bind(monthlyTaxController))

export default router
