"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = require("../config/database");
// import type { CarComplete } from '../../../types/models'
class CarRepository {
    async getAllItems() {
        const { data: stockData, error } = await database_1.supabase
            .from('car_stock_info')
            .select(`
          *,
          car_buyin(*),
          car_repair(*),
          car_finance(*),
          car_sellout(*)
        `);
        if (error) {
            throw new Error(`Error fetching data: ${error.message}`);
        }
        // Map structure to match the required format
        const result = stockData.map((row) => {
            const { car_buyin, car_repair, car_finance, car_sellout, ...stockInfo } = row;
            return {
                stockInfo: {
                    ...stockInfo
                },
                buyin: {
                    ...car_buyin,
                    ems_registration: car_buyin?.ems_registration || 0,
                    qc3: car_buyin?.qc3 || 0,
                    gear: car_buyin?.gear || null
                },
                repair: car_repair || null,
                finance: car_finance || null,
                sellout: car_sellout || null
            };
        });
        return result;
    }
}
exports.default = CarRepository;
