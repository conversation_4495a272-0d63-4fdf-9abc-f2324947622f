"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testDatabaseConnection = void 0;
const database_1 = require("../config/database");
const testDatabaseConnection = async () => {
    const { data, error } = await database_1.supabase
        .from('car_stock_info') // Use a small, always-present table
        .select('car_id')
        .limit(1);
    if (error) {
        console.error('❌ Supabase connection failed:', error.message);
        return false;
    }
    console.log('✅ Supabase connected successfully!');
    return true;
};
exports.testDatabaseConnection = testDatabaseConnection;
