"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.supabase = void 0;
const dotenv_1 = require("dotenv");
const path_1 = require("path");
const supabase_js_1 = require("@supabase/supabase-js");
// ✅ Load env from server/.env.local
(0, dotenv_1.config)({ path: (0, path_1.resolve)(__dirname, '../../.env.local') });
exports.supabase = (0, supabase_js_1.createClient)(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
