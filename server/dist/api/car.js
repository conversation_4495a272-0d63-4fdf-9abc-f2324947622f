"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const carController_1 = __importDefault(require("../controllers/carController"));
const carService_1 = __importDefault(require("../services/carService"));
const carRepository_1 = __importDefault(require("../repositories/carRepository"));
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
const carSevice = new carService_1.default(new carRepository_1.default());
const carController = new carController_1.default(carSevice);
router.route('/')
    .get(authMiddleware_1.authMiddleware, carController.getAllItems.bind(carController));
exports.default = router;
