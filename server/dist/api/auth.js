"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authController_1 = __importDefault(require("../controllers/authController"));
const authService_1 = __importDefault(require("../services/authService"));
const authRepository_1 = __importDefault(require("../repositories/authRepository"));
const router = express_1.default.Router();
const authService = new authService_1.default(new authRepository_1.default());
const authController = new authController_1.default(authService);
router.route('/login')
    .post(authController.checkUserCredentials.bind(authController));
exports.default = router;
