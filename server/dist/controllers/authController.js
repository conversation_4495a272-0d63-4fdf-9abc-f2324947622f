"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async checkUserCredentials(req, res) {
        try {
            const { username, password } = req.body;
            if (!username || !password) {
                return res.status(400).json({ error: 'Username and password are required.' });
            }
            const result = await this.authService.checkUserCredentials(username, password);
            return res.status(200).json({
                message: 'Login successful',
                session: result.session,
                user: result.user,
                auth_token: result.auth_token
            });
        }
        catch (error) {
            return res.status(401).json({ error: error.message || 'Authentication failed' });
        }
    }
}
exports.default = AuthController;
