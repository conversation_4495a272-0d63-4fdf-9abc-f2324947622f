"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class CarController {
    constructor(carService) {
        this.carService = carService;
    }
    async getAllItems(req, res) {
        try {
            const items = await this.carService.getAllItems();
            return res.status(200).json(items);
        }
        catch (error) {
            return res.status(500).json({ error: error.message || 'Failed to fetch car data' });
        }
    }
}
exports.default = CarController;
