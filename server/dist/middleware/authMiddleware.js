"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = void 0;
const database_1 = require("../config/database");
const authMiddleware = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Unauthorized: Missing token' });
    }
    const rawToken = authHeader.split(' ')[1];
    const token = rawToken.replace(/^"|"$/g, '');
    const { data, error } = await database_1.supabase
        .from('profiles')
        .select('*')
        .eq('auth_token', token)
        .single();
    if (error || !data) {
        return res.status(401).json({ error: 'Unauthorized: Invalid token' });
    }
    // ✅ Attach user data to request
    req.user = data;
    next();
};
exports.authMiddleware = authMiddleware;
