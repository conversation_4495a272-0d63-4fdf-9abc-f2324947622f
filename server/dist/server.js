"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const auth_1 = __importDefault(require("./api/auth"));
const car_1 = __importDefault(require("./api/car"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
// dotenv.config({ path: '../.env.local' })
dotenv_1.default.config({
    path: path_1.default.resolve(__dirname, '../.env.local') // ensures the correct path
});
const app = (0, express_1.default)();
const allowedOrigins = (process.env.CORS_ORIGINS || '').split(',');
console.log("✔️ CORS Origin:", allowedOrigins);
// MIDDLEWARE
app.use((0, cors_1.default)({
    origin: function (origin, callback) {
        if (!origin)
            return callback(null, true);
        const isAllowed = allowedOrigins.includes(origin) || origin.endsWith('.vercel.app');
        if (isAllowed) {
            callback(null, true);
        }
        else {
            console.warn(`❌ Blocked by CORS: ${origin}`);
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true
}));
app.use(express_1.default.json());
// ROUTES
app.use('/auth', auth_1.default);
app.use('/items', car_1.default);
const PORT = process.env.PORT || 4000;
app.listen(PORT, () => console.log(`🚀 Server running on http://localhost:${PORT}`));
