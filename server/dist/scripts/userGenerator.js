"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = require("../config/database");
async function createAdminUser() {
    const username = 'admin';
    const password = 'adminsp';
    // const fakeEmail = `${username}@sp-autocar.fake`
    const fakeEmail = `${username}@sp-autocar.fakeemail.com`; // ✅ Fix: RFC-compliant email
    // 1. Sign up
    const { data: authUser, error: signUpError } = await database_1.supabase.auth.signUp({
        email: fakeEmail,
        password
    });
    if (signUpError)
        throw signUpError;
    // 2. Insert to profiles
    if (authUser.user) {
        const { error: profileError } = await database_1.supabase.from('profiles').insert({
            id: authUser.user.id,
            username,
            full_name: '<PERSON><PERSON><PERSON><PERSON>',
            role: 'admin'
        });
        if (profileError)
            throw profileError;
    }
    console.log('✅ Admin user created successfully');
}
createAdminUser();
