"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateUUID = generateUUID;
exports.generateAuditTrail = generateAuditTrail;
exports.parseStockSheet = parseStockSheet;
exports.calculateIsAuctionCar = calculateIsAuctionCar;
// ✅ The full script with rollback support in uploadToSupabase()
// === Add at the top ===
const XLSX = __importStar(require("xlsx"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const uuid_1 = require("uuid");
const database_1 = require("../config/database");
function generateUUID() {
    return (0, uuid_1.v4)();
}
let globalAuditId = 1;
const mappingPath = path.resolve(__dirname, '../../excel_files/thai_to_property_map.json');
const fieldMap = JSON.parse(fs.readFileSync(mappingPath, 'utf8'));
// Expected types per property for debugging
const expectedTypes = {
    purchase_date: 'date',
    parking_location: 'string',
    transport_1_auction_lot: 'number',
    transport_2_personal_payment: 'number',
    transport_3_tl_payment: 'number',
    qc1_auction_lot: 'number',
    initial_check: 'number',
    ems_registration_qc3: 'number',
    registration_fee: 'number',
    brand: 'string',
    model: 'string',
    color: 'string',
    year: 'number',
    vat_percent: 'number',
    purchase_price: 'number',
    purchase_vat_percent: 'number',
    operation_cost_incl_vat: 'number',
    tax_insurance_cost_zero: 'number',
    other_costs_seven: 'number',
    five_three_tax_percentage: 'number',
    total_purchase_cost: 'number',
    auction_location: 'string',
    auction_provinced: 'string',
    auction_order: 'string',
    auction_checker: 'string',
    transport_personal: 'string',
    transport_company: 'string',
    tank_number: 'string',
    engine_number: 'string',
    book_deposit: 'number',
    type_of_transport: 'string',
    finance_received_date: 'date',
    car_tax_invoice_date: 'date',
    car_tax_invoice_number: 'string',
    car_amount: 'number',
    car_vat_amount: 'number',
    commission_tax_invoice_date: 'date',
    commission_tax_invoice_number: 'string',
    car_commission_amount: 'number',
    input_vat_commission: 'number',
    withholding_tax: 'number',
    salesperson: 'string',
    bank: 'string',
    marketing_person: 'string',
    promotion_customer: 'number',
    bonus_insurance_car_life_engine: 'number',
    customer_name: 'string',
    customer_address_or_advance_payment: 'string',
    down_payment: 'number',
    loan_protection_insurance: 'number',
    accident_insurance: 'number',
    car_insurance: 'number',
    bank_documents: 'number'
};
const carBuyinColumns = [
    'parking_location', 'type_of_transport',
    'transport_personal', 'transport_company',
    'transport_2_personal_payment', 'transport_3_tl_payment'
];
const carRepairColumns = [
    'repainting_cost', 'engine_repair_cost', 'suspension_repair_cost',
    'autopart_cost', 'battery_cost', 'tires_wheels_cost'
];
function getBangkokISO() {
    // Thailand is UTC+7
    const date = new Date();
    // Convert to Thailand time
    const thailandOffset = 7 * 60; // minutes
    const localOffset = date.getTimezoneOffset(); // minutes
    const diff = thailandOffset + localOffset;
    const thailandDate = new Date(date.getTime() + diff * 60 * 1000);
    const pad = (n, width = 2) => n.toString().padStart(width, '0');
    const year = thailandDate.getFullYear();
    const month = pad(thailandDate.getMonth() + 1);
    const day = pad(thailandDate.getDate());
    const hour = pad(thailandDate.getHours());
    const minute = pad(thailandDate.getMinutes());
    const second = pad(thailandDate.getSeconds());
    const ms = pad(thailandDate.getMilliseconds(), 3);
    // Format as 'YYYY-MM-DD HH:mm:ss.SSS+00'
    return `${year}-${month}-${day} ${hour}:${minute}:${second}.${ms}+00`;
}
function generateAuditTrail(carId, carData, startingAuditId = 1) {
    const timestamp = getBangkokISO();
    const headers = [];
    const details = [];
    let auditIdCounter = startingAuditId;
    const pushAudit = (table, columns, data) => {
        const auditId = auditIdCounter++;
        headers.push({ car_id: carId, table_name: table, timestamp, description: `Initial import for ${table}` });
        for (const col of columns) {
            let newVal = col in data ? data[col] : null;
            if (typeof newVal === 'undefined' || newVal === null) {
                newVal = (col === 'tire_wheels_cost' || col.includes('cost') || col.includes('payment')) ? 0 : "";
            }
            const oldVal = typeof newVal === 'number' ? '0' : "";
            const newValStr = typeof newVal === 'number' ? newVal.toString() : newVal;
            details.push({ audit_id: auditId, column_name: col, old_value: oldVal, new_value: newValStr });
        }
    };
    if (carData.car_buyin)
        pushAudit('car_buyin', carBuyinColumns, carData.car_buyin);
    if (carData.car_repair)
        pushAudit('car_repair', carRepairColumns, carData.car_repair);
    return { headers, details };
}
function parseStockSheet(filepath, sheetName = 'STOCK_S') {
    const workbook = XLSX.readFile(filepath);
    const sheet = workbook.Sheets[sheetName];
    if (!sheet)
        throw new Error(`Sheet "${sheetName}" not found.`);
    const rows = XLSX.utils.sheet_to_json(sheet);
    return rows.map((row, index) => {
        const bucket = {
            car_stock_info: {},
            car_buyin: {},
            car_repair: {},
            car_finance: {},
            car_sellout: {}
        };
        for (const rawColumn in row) {
            const thaiColumn = rawColumn.trim();
            const mapping = fieldMap[thaiColumn];
            if (!mapping)
                continue;
            const [table, key] = mapping;
            if (!bucket[table])
                continue;
            let value = row[rawColumn];
            if (key.includes('date'))
                value = convertBuddhistDate(value);
            else if (typeof value === 'string' && /^[\d,]+(\.\d+)?$/.test(value))
                value = sanitizeNumber(value);
            else if (typeof value === 'number')
                value = sanitizeNumber(value);
            else
                value = sanitizeString(value);
            const expected = expectedTypes[key];
            if (expected) {
                let actualType = typeof value;
                if (expected === 'date' && typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
                    actualType = 'date';
                }
                // Fix: type mismatch warnings & safe casting for strings
                if (expected === 'string' && typeof value === 'number') {
                    if (key === 'transport_company' && value === 0) {
                        console.warn(`⚠️ Overriding transport_company from number 0 to empty string in row ${index + 2}`);
                        value = '';
                    }
                    else {
                        console.warn(`⚠️ Type mismatch on key "${key}" in row ${index + 2}: expected string, got number data`, value);
                        value = String(value);
                    }
                }
                else if (expected === 'number' && (value === '' || isNaN(Number(value)))) {
                    console.warn(`⚠️ Invalid number at "${key}" in row ${index + 2}. Got:`, value);
                    value = null;
                }
                else if (actualType !== expected) {
                    console.warn(`⚠️ Type mismatch on key "${key}" in row ${index + 2}: expected ${expected}, got ${actualType}, value:`, value);
                    value = null;
                }
            }
            bucket[table][key] = value;
        }
        if (!bucket.car_stock_info.car_id)
            bucket.car_stock_info.car_id = generateUUID();
        const carId = bucket.car_stock_info.car_id;
        for (const table in bucket) {
            if (table !== 'car_stock_info')
                bucket[table]['car_id'] = carId;
        }
        const { headers, details } = generateAuditTrail(carId, bucket, globalAuditId);
        globalAuditId += headers.length;
        const timestamp = getBangkokISO();
        for (const table of Object.keys(bucket)) {
            if (['car_stock_info', 'car_buyin', 'car_repair', 'car_finance', 'car_sellout'].includes(table)) {
                bucket[table]['created_at'] = timestamp;
                bucket[table]['updated_at'] = timestamp;
            }
        }
        bucket.car_stock_info.is_auction_car = calculateIsAuctionCar(bucket.car_buyin.vat_percent);
        bucket.car_stock_info.car_status = 'available';
        const t2 = bucket.car_buyin.transport_2_personal_payment;
        if (typeof t2 === 'undefined' || t2 === null) {
            bucket.car_buyin.type_of_transport = '';
        }
        else {
            bucket.car_buyin.type_of_transport = typeof t2 === 'number' && t2 === 0 ? 'company' : 'personal';
        }
        return { ...bucket, audit_header: headers, audit_detail: details };
    });
}
function convertBuddhistDate(raw) {
    if (raw == null || raw === '')
        return null;
    if (typeof raw === 'number') {
        const parsed = XLSX.SSF.parse_date_code(raw);
        if (!parsed)
            return null;
        let year = parsed.y > 2200 ? parsed.y - 543 : parsed.y;
        return new Date(Date.UTC(year, parsed.m - 1, parsed.d)).toISOString();
    }
    const match = String(raw).trim().match(/^([\d]{1,2})[\/\-]([\d]{1,2})[\/\-]([\d]{4})$/);
    if (match) {
        let yyyy = parseInt(match[3], 10);
        if (yyyy > 2200)
            yyyy -= 543;
        return new Date(Date.UTC(yyyy, parseInt(match[2]) - 1, parseInt(match[1]))).toISOString();
    }
    return null;
}
function sanitizeNumber(val) {
    const num = parseFloat(String(val).replace(/,/g, ''));
    return isNaN(num) ? null : parseFloat(num.toFixed(2));
}
function sanitizeString(val) {
    return String(val).trim();
}
function calculateIsAuctionCar(vatPercent) {
    return vatPercent != null && !isNaN(vatPercent) && vatPercent !== 0;
}
async function uploadToSupabase(data) {
    for (const [index, row] of data.entries()) {
        const { car_stock_info, car_buyin, car_repair, car_finance, car_sellout, audit_header, audit_detail } = row;
        const insertedTables = [];
        try {
            const insertOrFail = async (table, payload) => {
                const { error } = await database_1.supabase.from(table).insert([payload]);
                if (error) {
                    console.error(`🚨 Payload that caused failure in ${table}:`, payload);
                    throw new Error(`${table} insert failed: ${error.message}`);
                }
                insertedTables.push({ table, id: payload.car_id });
            };
            await insertOrFail('car_stock_info', car_stock_info);
            if (Object.keys(car_buyin).length)
                await insertOrFail('car_buyin', car_buyin);
            if (Object.keys(car_repair).length)
                await insertOrFail('car_repair', car_repair);
            if (Object.keys(car_finance).length)
                await insertOrFail('car_finance', car_finance);
            if (Object.keys(car_sellout).length)
                await insertOrFail('car_sellout', car_sellout);
            const { data: insertedAuditHeaders, error: headerError } = await database_1.supabase
                .from('audit_header').insert(audit_header).select('audit_id, table_name');
            if (headerError || !insertedAuditHeaders?.length) {
                throw new Error(`audit_header insert failed: ${headerError?.message || 'No headers returned'}`);
            }
            const mappedAuditDetails = audit_detail.map(detail => {
                let table;
                if (carBuyinColumns.includes(detail.column_name)) {
                    table = 'car_buyin';
                }
                else if (carRepairColumns.includes(detail.column_name)) {
                    table = 'car_repair';
                }
                const realAuditId = insertedAuditHeaders.find(h => h.table_name === table)?.audit_id;
                if (!realAuditId) {
                    throw new Error(`🚨 No inserted audit_header found for column: ${detail.column_name}`);
                }
                return {
                    ...detail,
                    audit_id: realAuditId
                };
            });
            const { error: detailError } = await database_1.supabase.from('audit_detail').insert(mappedAuditDetails);
            if (detailError) {
                throw new Error(`audit_detail insert failed: ${detailError.message}`);
            }
            // ✅ Final step: update car_status to 'sold'
            const { error: updateStatusError } = await database_1.supabase
                .from('car_stock_info')
                .update({ car_status: 'sold' })
                .eq('car_id', car_stock_info.car_id);
            if (updateStatusError) {
                throw new Error(`car_stock_info update failed: ${updateStatusError.message}`);
            }
            console.log(`✅ Uploaded row ${index + 1}`);
        }
        catch (err) {
            console.error(`❌ Upload failed at row ${index + 1}:`, err);
            for (const { table, id } of insertedTables.reverse()) {
                await database_1.supabase.from(table).delete().eq('car_id', id);
            }
            process.exit(1);
        }
    }
}
const data = parseStockSheet('./excel_files/จัดเรียงข้อมูล_stock_รถ.xlsx');
// console.log(data[0])
uploadToSupabase(data).then(() => console.log('🎉 All data uploaded successfully'));
